package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.DEAL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static com.kylas.sales.workflow.mq.event.DealEvent.getDealCreatedEventName;
import static com.kylas.sales.workflow.mq.event.DealEvent.getDealUpdatedEventName;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.LENIENT;
import static org.skyscreamer.jsonassert.JSONCompareMode.STRICT;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.DealEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class DealWorkflowProcessorIntegrationTests {

  static final String DEAL_UPDATE_COMMAND = "workflow.deal.update";
  static final String DEAL_CREATE_SHARE_RULE_COMMAND = "workflow.create.deal.shareRule";

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;


  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-for-integration-test.sql")
  public void givenDealCreatedEvent_shouldUpdatePropertiesAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);

    MockMqListener mockMqListener = new MockMqListener();

    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.workflow.deal.update.1", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealCreatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/deal-create-patch-command.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    container.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-share-action.sql")
  public void givenDealEvent_havingWorkflowWithShareAction_shouldPublishCommand()
          throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);

    MockMqListener mockMqListener = new MockMqListener();

    var container = initializeRabbitMqListener(DEAL_CREATE_SHARE_RULE_COMMAND, "q.workflow.deal.update.10", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealCreatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
            getResourceAsString("/contracts/mq/command/deal-create-share-rule-command.json"),
            mockMqListener.actualMessage,
            JSONCompareMode.STRICT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-deal-update-workflow.sql")
  public void givenDealUpdatedEvent_shouldUpdatePropertiesAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-updated-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.workflow.deal.update.2", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/deal-update-patch-command.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-new-value.sql")
  public void givenDealCreatedEvent_withTriggerOnNewValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-new-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealCreatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-new-value.sql")
  public void givenDealUpdatedEvent_withTriggerOnNewValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-new-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-old-value.sql")
  public void givenDealCreatedEvent_withTriggerOnOldValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-old-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealCreatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-old-value.sql")
  public void givenDealUpdatedEvent_withTriggerOnOldValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-old-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-is-changed-value.sql")
  public void givenDealCreatedEvent_withTriggerOnIsChangedValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-is-changed-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealCreatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-workflow-with-multiple-conditions-trigger-on-is-changed-value.sql")
  public void givenDealUpdatedEvent_withTriggerOnIsChangedValue_shouldTriggerWorkflowConditionally()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-with-is-changed-value-conditions.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(
      initializers = {
          TestMqSetup.class,
          TestDatabaseInitializer.class
      })
  @DisplayName("Tests that execute webhooks on Deal event")
  class WebhookDealIntegrationTests {

    @Test
    @Sql("/test-scripts/create-webhook-workflow-for-deal.sql")
    public void givenDealCreateEvent_usingMethodGET_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815?forecasting%20type=OPEN&Stage%20Reason=Some%20Reason&multiValuePicklist1=val1&multiValuePicklist1=val2&Meeting%20Scheduled%20On=&Task%20Due%20On=Thu%20Mar%2023%2012:11:57%20UTC%202023&param3=SomeTenant&param4=100&Latest%20Activity%20CreatedAt=Thu%20Mar%2023%2012:11:57%20UTC%202023&param1=new%20deal&param2=Stark")
              .willReturn(
                  okForContentType(MediaType.APPLICATION_JSON_VALUE, "{}")));

      mockAPI(authenticationToken);

      String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event.json");
      DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      // when
      rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

      WireMock.verify(WireMock.getRequestedFor(urlEqualTo("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815?forecasting%20type=OPEN&Stage%20Reason=Some%20Reason"
          + "&multiValuePicklist1=val1&multiValuePicklist1=val2&Meeting%20Scheduled%20On=&Task%20Due%20On=Thu%20Mar%2023%2012:11:57%20UTC%202023&param3=SomeTenant&param4=100&Latest%20Activity%20CreatedAt=Thu%20Mar%2023%2012:11:57%20UTC%202023&param1=new%20deal&param2=Stark")));
    }

    @Test
    @Sql("/test-scripts/create-post-webhook-workflow-for-deal.sql")
    public void givenDealCreateEvent_usingMethodPOST_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      mockAPI(authenticationToken);

      stubFor(
          post("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815")
              .willReturn(
                  okForContentType(MediaType.APPLICATION_JSON_VALUE, "{}")));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/deal-created-event.json");
      DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      // when
      rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

      WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
          .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/webhook-request.json"), true, false)));
    }

    @Test
    @Sql("/test-scripts/create-put-webhook-workflow-for-deal.sql")
    public void givenDealCreateEvent_usingMethodPUT_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          put("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815")
              .willReturn(
                  okForContentType(MediaType.APPLICATION_JSON_VALUE, "{}")));

      mockAPI(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/deal-created-event.json");
      DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      // when
      rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
      mockMqListener.latch.await(3, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/webhook/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
          .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/webhook-request.json"), true, false)));
    }
  }

  @Test
  @Sql("/test-scripts/insert-create-deal-with-custom-fields-workflow.sql")
  public void givenDealCreateEvent_withCustomFields_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-created-event-2.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.workflow.deal.update.4", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    //then
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/deal-update-patch-command-2.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/insert-deal-update-workflow-with-edit-property-action.sql")
  public void givenDealUpdatedEvent_withEditFieldPipelineToUpdatePipelineStageReason_shouldUpdatePropertiesAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-updated-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.workflow.deal.update.5", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(10, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/deal-update-patch-command-3.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-deal-update-workflow-with-edit-property-action.sql")
  public void givenDealUpdateEvent_withCustomAndCopyEditPropertyAction_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(101L, 55L, true, true, true, true, true).withName("User 101");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/deal/v1/deals/fields")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-all-deal-fields.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        get("/iam/v1/tenants/55/creator")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-updated-event-for-custom-copy.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.workflow.deal.update.5", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, getDealUpdatedEventName(), dealEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/deal-update-patch-command-4.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.REPLACE.createdAt", (o1, o2) -> true),
            new Customization("entity.REPLACE.estimatedClosureOn", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(3001);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1001);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-deal-workflow-with-multi-picklist-field.sql")
  public void givenDealCreateEvent_withMultiValueCustomPicklistFields_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/deal-updated-event-with-multi-picklist-value-field-2.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND, "q.updated.33", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealUpdatedEventName(), dealEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/deal-update-patch-with-multi-value-custom-picklist.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  private void mockAPI(String authenticationToken) throws IOException {
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));



    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MockMqListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);

    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

}
