
package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.CALL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.DEAL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.MEETING_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.BDDMockito.given;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.ExecutionLogRepository;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog_;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob_;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import com.kylas.sales.workflow.mq.event.CallLogEvent;
import com.kylas.sales.workflow.mq.event.ContactEvent;
import com.kylas.sales.workflow.mq.event.DealEvent;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import com.kylas.sales.workflow.mq.event.MeetingEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class WorkflowProcessor_MarketplaceActionIntegrationTests {

  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  ExecutionLogRepository executionLogRepository;
  @Autowired
  Environment environment;
  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tt-nPLZWtVP29NgyujjWMfJLaIR5Rlxv_cyP43b_7SM";


  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-marketplace-workflow.sql"})
  public void givenLeadCreateEvent_usingMethodGET_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b815?param5=100ft&param4=123&param1=tony").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-workflow.sql"})
  public void givenLeadCreateEvent_usingMethodPOST_havingActiveMarketplaceAction_shouldExecuteAndCreateExecutionLog()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = getStubbedUser();
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("contracts/marketplace/lead-marketplace-webhook-response.json"), true, false)));

    List<ExecutionLog> executionLogs = executionLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(
        criteriaBuilder.equal(root.get(ExecutionLog_.WORKFLOW_ACTION), ActionType.MARKETPLACE_ACTION),
        criteriaBuilder.equal(root.get(ExecutionLog_.WORKFLOW_ID), 301L),
        criteriaBuilder.equal(root.get(ScheduledJob_.ENTITY_ID), 1L),
        criteriaBuilder.equal(root.get(ScheduledJob_.ENTITY_TYPE), EntityType.LEAD)
    ));
    Assertions.assertThat(executionLogs).hasSize(1);
    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/execution-logs/" + executionLogs.stream().findFirst().get().getId())
            .retrieve()
            .bodyToMono(String.class);
    var expectedResponse =
        getResourceAsString(
            "contracts/workflow/api/integration/lead-marketplace-action-execution-log-success-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("executedAt", (o1, o2) -> true),
                        new Customization("id", (o1, o2) -> true)
                    ));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-for-Inactive-workflow.sql"})
  public void givenLeadCreateEvent_usingMethodPOST_havingInActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-put-marketplaceAction-workflow.sql"})
  public void givenLeadCreateEvent_usingMethodPUT_havingActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson("{\n"
            + "  \"Zip\" : \"411045\",\n"
            + "  \"Owner\" : \"RJ\",\n"
            + "  \"SirNmae\" : \"Stark\",\n"
            + "  \"Address\" : \"company address\",\n"
            + "  \"multiValuePicklist2\" : \"\",\n"
            + "  \"City\" : \"My City\",\n"
            + "  \"multiValuePicklist1\" : [ \"Load\", \"Aim\" ],\n"
            + "  \"multiValuePicklist3\" : [ \"First\", \"Second\", \"Third\" ],\n"
            + "  \"phone\" : [ {\n"
            + "    \"type\" : \"MOBILE\",\n"
            + "    \"code\" : \"IN\",\n"
            + "    \"value\" : \"1231231231\",\n"
            + "    \"dialCode\" : \"+91\",\n"
            + "    \"primary\" : true\n"
            + "  }, {\n"
            + "    \"type\" : \"WORK\",\n"
            + "    \"code\" : \"IN\",\n"
            + "    \"value\" : \"1231231232\",\n"
            + "    \"dialCode\" : \"+91\",\n"
            + "    \"primary\" : false\n"
            + "  } ],\n"
            + "  \"State\" : \"My State\",\n"
            + "  \"name\" : \"tony\",\n"
            + "  \"Country\" : \"IN\",\n"
            + "  \"Pipeline reason\" : \"Bought product/service with competitor\",\n"
            + "  \"ConvertedBy\" : \"RJ\",\n"
            + "  \"ImportedBy\" : \"\",\n"
            + "  \"ExpectedClosureOn\" : \"Thu Jan 12 06:11:44 UTC 2023\",\n"
            + "  \"ActualClosureDate\" : \"Thu Jan 12 06:11:44 UTC 2023\"\n"
            + "}")));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-put-marketplaceAction-forInactive-action.sql"})
  public void givenLeadCreateEvent_usingMethodPUT_havingInActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-marketplace-workflow.sql"})
  public void givenContactCreateEvent_usingMethodGET_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b815?param5=100ft&param4=123&param1=tony").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(302);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(153);
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-workflow.sql"})
  public void givenContactCreateEvent_usingMethodPOST_havingActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(302);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/marketplace/contact-webhook-request.json"), true, false)));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-put-marketplaceAction-workflow.sql"})
  public void givenContactCreateEvent_usingMethodPUT_havingActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(302);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson("{\n"
            + "  \"owner\" : \"RJ\",\n"
            + "  \"phone\" : [ {\n"
            + "    \"type\" : \"MOBILE\",\n"
            + "    \"code\" : \"IN\",\n"
            + "    \"value\" : \"1231231231\",\n"
            + "    \"dialCode\" : \"+91\",\n"
            + "    \"primary\" : true\n"
            + "  }, {\n"
            + "    \"type\" : \"WORK\",\n"
            + "    \"code\" : \"IN\",\n"
            + "    \"value\" : \"1231231232\",\n"
            + "    \"dialCode\" : \"+91\",\n"
            + "    \"primary\" : false\n"
            + "  } ],\n"
            + "  \"name\" : \"tony\",\n"
            + "  \"SirName\" : \"Stark\"\n"
            + "}")));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-marketplace-workflow.sql"})
  public void givenDealCreateEvent_usingMethodGET_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/3e0d9676-ad3c-4cf2-a449-ca334e43b815?param3=SomeTenant&param4=100&param1=new%20deal&param2=Stark")
            .willReturn(
                okForContentType(MediaType.APPLICATION_JSON_VALUE, "{}")));

    mockAPI(authenticationToken);

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/deal-created-event-for-tenant-id-99.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(303);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(153);
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-workflow.sql"})
  public void givenDealCreateEvent_usingMethodPOST_havingActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/deal-created-event-for-tenant-id-99.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(303);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/marketplace/marketplace-webhook-on-post-method.json"),
            true, false)));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-put-marketplaceAction-workflow.sql"})
  public void givenDealCreateEvent_usingMethodPUT_havingActiveMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    mockAPI(authenticationToken);

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/deal-created-event-for-tenant-id-99.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    // when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(303);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/marketplace/marketplace-webhook-on-put-method.json"),
            true, false)));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-workflow.sql"})
  public void givenCallLogEvent_havingWorkflowActionAsMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-created-event-for-tenant-id-99.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);
    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(304);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/marketplace/call-log-marketplace-webhook-on-post-method.json"),
            true, false)));
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/create-post-marketplace-action-workflow.sql"})
  public void givenMeetingEvent_havingWorkflowActionAsMarketplaceAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/meeting-created-event-for-tenant-id-99.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);
    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(305);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/marketplace/meeting-marketplace-webhook-response.json"),
            true, false)));
  }

  private void mockAPI(String authenticationToken) throws IOException {
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));


    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
  }


  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private User getStubbedUser() {
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");

    Action lead = new Action();
    lead.setRead(true);
    lead.setReadAll(true);
    Set<Permission> permissions = aUser.getPermissions();
    permissions.add(new Permission(11L, "lead", "lead", lead));
    aUser.withPermissions(permissions);
    return aUser;
  }
}
