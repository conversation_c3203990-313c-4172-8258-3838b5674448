package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.PRODUCTIVITY_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.NON_EXTENSIBLE;
import static org.skyscreamer.jsonassert.JSONCompareMode.STRICT;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.EmailActionEvent;
import com.kylas.sales.workflow.mq.event.TaskEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class TaskWorkflowProcessorIntegrationTests {

  private static final String TASK_UPDATE_COMMAND = "workflow.task.update";
  private static final String TASK_ASSIGN_COMMAND = "workflow.task.assign";
  private static final String ASSOCIATED_LEAD_WORKFLOW_EXECUTE = "execute.associate.lead";
  private static final String ASSOCIATED_DEAL_WORKFLOW_EXECUTE = "execute.associate.deal";
  private static final String ASSOCIATED_CONTACT_WORKFLOW_EXECUTE = "execute.associate.contact";


  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);

  @Test
  @Sql("/test-scripts/insert-task-workflow-for-id-5322.sql")
  public void givenTaskCreatedEvent_shouldUpdatePropertiesAndPublishCommand() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(7250L, 3212L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("contracts/mq/events/task-created-event-for-id -3212.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(TASK_UPDATE_COMMAND, "q.workflow.task.update.12", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/task-update-patch-command.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.REPLACE.dueDate", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(5322);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }


  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskUpdatedEvent_shouldUpdatePropertiesAndPublishCommand() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(60L, 60L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("contracts/mq/events/task-updated-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(TASK_UPDATE_COMMAND, "q.workflow.task.updated", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskUpdatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        mockMqListener.actualMessage,
        getResourceAsString("/contracts/mq/command/task-update-patch-command-2.json"),
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(308);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskCreatedEvent_shouldPublishAssignToEvent() throws IOException, JSONException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("contracts/mq/events/task-created-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(TASK_ASSIGN_COMMAND, "q.workflow.task.assign", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/task-assign-command.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(309);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskUpdatedEvent_shouldPublishAssignToEvent() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(60L, 60L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("contracts/mq/events/task-updated-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(TASK_ASSIGN_COMMAND, "q.workflow.task.assign1", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskUpdatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/task-assign-command-2.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(308);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-task.sql")
  public void givenTaskCreatedEvent_usingMethodGET_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-created-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(902);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

    WireMock.verify(WireMock.getRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816?param3=987&param3=1381&param1=100&param2=Task")));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-task.sql")
  public void givenTaskCreatedEvent_usingMethodPOST_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-created-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(901);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/task/verify-webhook-request-on-task.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-task.sql")
  public void givenTaskCreatedEvent_usingMethodPUT_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b817").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-create-event.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(903);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b817"))
        .withRequestBody(equalToJson("{\n"
            + "  \"param5\" : \"55553\",\n"
            + "  \"param6\" : \"55555\",\n"
            + "  \"param4\" : \"\"\n"
            + "}")));
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  @Test
  @Sql("/test-scripts/integration/insert-workflow-for-email-action-test-on-task.sql")
  public void givenTaskCreatedEvent_withEmailAction_shouldPublishEmailActionEvent() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(794L, 478L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));


    String resourceAsString = getResourceAsString("/contracts/mq/events/task-created-event-for-email-action.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.workflow.task.send.email", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/task-create-email-action-event-response.json"),
        getResourceAsString("contracts/mq/command/task-create-email-action-event-response.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("to[*].id", (o1, o2) -> true),
            new Customization("to[*].email", (o1, o2) -> true),
            new Customization("to[*].name", (o1, o2) -> true),
            new Customization("bcc[*].id", (o1, o2) -> true),
            new Customization("bcc[*].email", (o1, o2) -> true),
            new Customization("bcc[*].name", (o1, o2) -> true),
            new Customization("cc[*].id", (o1, o2) -> true),
            new Customization("cc[*].email", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true),
            new Customization("cc[*].name", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(909);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(11);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-workflow-for-email-action-test-on-task.sql")
  public void givenTaskUpdatedEvent_withEmailAction_shouldPublishEmailActionEvent() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(794L, 478L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    stubFor(
        get("/iam/v1/users/17")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("contracts/user/responses/email-action-user-details-by-id-17.json"))));


    String resourceAsString = getResourceAsString("contracts/mq/events/task-updated-event-for-email-action.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.workflow.task.send.email.2", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskUpdatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/task-update-email-action-response.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("to[*].id", (o1, o2) -> true),
            new Customization("to[*].email", (o1, o2) -> true),
            new Customization("to[*].name", (o1, o2) -> true),
            new Customization("bcc[*].id", (o1, o2) -> true),
            new Customization("bcc[*].email", (o1, o2) -> true),
            new Customization("bcc[*].name", (o1, o2) -> true),
            new Customization("cc[*].id", (o1, o2) -> true),
            new Customization("cc[*].email", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true),
            new Customization("cc[*].name", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(1000);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(11);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-workflows-for-trigger-workflow-action.sql")
  public void givenTaskCreatedEventWithAssociatedEntities_onWorkflowWithTriggerWorkflowAction_shouldPublishedEventOnLeadContactAndDeals()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response-for-id-195341.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/contact-all-details-response.json"))));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-created-event-with-related-entities.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    MockMqListener leadMockMqListener = new MockMqListener();
    var container1 = initializeRabbitMqListener(ASSOCIATED_LEAD_WORKFLOW_EXECUTE, "q.updated.1", leadMockMqListener);

    MockMqListener dealMockMqListener = new MockMqListener();
    var container2 = initializeRabbitMqListener(ASSOCIATED_DEAL_WORKFLOW_EXECUTE, "q.updated.2", dealMockMqListener);

    MockMqListener contactMockMqListener = new MockMqListener();
    var container3 = initializeRabbitMqListener(ASSOCIATED_CONTACT_WORKFLOW_EXECUTE, "q.updated.3", contactMockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);
    contactMockMqListener.latch.await(2, TimeUnit.SECONDS);

    // then
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-lead-workflow-event-on-task.json"),
        leadMockMqListener.actualMessage, new CustomComparator(NON_EXTENSIBLE,
            new Customization("leadEvent.metadata.eventId", (o1, o2) -> true)));

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-deal-workflow-event-on-task.json"),
        dealMockMqListener.actualMessage, new CustomComparator(NON_EXTENSIBLE,
            new Customization("leadEvent.metadata.eventId", (o1, o2) -> true)));

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-contact-workflow-event-on-task.json"),
        contactMockMqListener.actualMessage, new CustomComparator(NON_EXTENSIBLE,
            new Customization("leadEvent.metadata.eventId", (o1, o2) -> true)));
    container1.stop();
    container2.stop();
    container3.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-task-workflow-with-associated-webhook-parameter.sql")
  public void givenTaskCreatedEvent_usingPostMethod_shouldExecuteWebhookWithAssociatedEntities() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response-for-id-195341.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/contact-all-details-response.json"))));

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-created-event-with-related-entities.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(300);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/task/verify-webhook-response-on-associated-entities-on-task.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/integration/insert-task-workflow-with-edit-property-actions.sql")
  public void givenTaskCreatedEvent_tryToCopyFromRelationField_shouldCopyPropertyAndPublishCommand() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response-for-id-195341.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-id.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/contact-all-details-response.json"))));

    stubFor(
        get("/config/v1/entities/task/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-task-fields.json"))));

    stubFor(
        get("/iam/v1/tenants/191/creator")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/task-created-event-with-related-entities.json");
    TaskEvent taskEvent = objectMapper.readValue(resourceAsString, TaskEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(TASK_UPDATE_COMMAND, "q.workflow.task.updated", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(300);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/task-update-patch-command-3.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.REPLACE.description", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true),
            new Customization("entity.REPLACE.completedAt", (o1, o2) -> true)));
    container.stop();
  }

  private void mockAPI(String authenticationToken) throws IOException {

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MockMqListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);

    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }
}
