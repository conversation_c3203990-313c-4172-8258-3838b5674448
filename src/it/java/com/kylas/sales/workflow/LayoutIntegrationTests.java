package com.kylas.sales.workflow;

import static org.junit.Assert.fail;

import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import java.io.IOException;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;


@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
public class LayoutIntegrationTests {

  @Autowired
  Environment environment;
  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fcqC0tgtZBzxpU5Si1IT8eOi4CNMckmPnVTze2xfmIk";

  @Test
  public void shouldReturnListLayoutForWorkflows() throws IOException {
    // given
    var client = buildWebClient();

    // when
    var responseMono =
        client.get().uri("/v1/workflows/layout/list").retrieve().bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("contracts/layout/responses/list-workflow-layout.json");
    StepVerifier.create(responseMono)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(expectedResponse, json, false);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .expectComplete()
        .verify();
  }

  @Test
  public void shouldReturnListLayoutForScheduledJobs() throws IOException {
    // given
    var client = buildWebClient();

    // when
    var responseMono =
        client.get().uri("/v1/scheduled-jobs/layout/list").retrieve().bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("contracts/layout/responses/list-scheduled-jobs-layout.json");
    StepVerifier.create(responseMono)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(expectedResponse, json, false);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .expectComplete()
        .verify();
  }

  @Test
  public void shouldReturnListLayoutForExecutionLogs() throws IOException {
    // when
    var responseMono =
        buildWebClient().get().uri("/v1/execution-logs/layout/list").retrieve().bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("contracts/layout/responses/list-execution-logs-layout.json");
    StepVerifier.create(responseMono)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(expectedResponse, json, false);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .expectComplete()
        .verify();
  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
