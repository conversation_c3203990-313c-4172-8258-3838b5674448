package com.kylas.sales.workflow;

import static com.github.dockerjava.zerodep.shaded.org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.LENIENT;
import static org.skyscreamer.jsonassert.JSONCompareMode.STRICT;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.dockerjava.zerodep.shaded.org.apache.hc.core5.http.HttpHeaders;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import com.kylas.sales.workflow.mq.event.MarketplaceTriggerEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class LeadWorkflowProcessorIntegrationTests {

  static final String LEAD_UPDATE_COMMAND_QUEUE = "workflow.lead.update";

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired private AmqpAdmin rabbitAdmin;
  @Autowired private RabbitTemplate rabbitTemplate;
  @Autowired private ObjectMapper objectMapper;
  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;


  @Test
  @Sql({"/test-scripts/table-cleanup.sql","/test-scripts/integration/lead-workflow-399-with-conditions-and-actions.sql"})
  public void givenLeadCreateEvent_shouldExecuteWorkflow() throws Exception {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);
    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));
    stubFor(
        get(urlEqualTo(
            "/3e0d9676-ad3c-4cf2-a449-ca334e43b815?pipeline=LeadPipelineNew&product=CRM&product=Marketing&leadName=Tony&pipelineStage=Won"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)));
    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event-for-workflow-id-399.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    MockMqListener mockMqListener = new MockMqListener();
    SimpleMessageListenerContainer simpleMessageListenerContainer = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.listener.1",
        mockMqListener);
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/lead-update-patch-command-workflowId-399.json"),
        mockMqListener.actualMessage,
        LENIENT);
    // then
    verify(
        WireMock.getRequestedFor(
            urlEqualTo(
                "/3e0d9676-ad3c-4cf2-a449-ca334e43b815?pipeline=LeadPipelineNew&Score=100.0&product=CRM&product=Marketing&leadName=Tony&pipelineStage=Won"))
    );
    simpleMessageListenerContainer.stop();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql","/test-scripts/integration/lead-workflow-400-with-conditions-and-actions.sql"})
  public void givenLeadCreateEvent_withConditionOnUTMAndSourceField_andEditActionOnUTMField_shouldExecuteWorkflow() throws Exception {
    // given
    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event-for-workflow-id-400.json");
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    MockMqListener mockMqListener = new MockMqListener();
    SimpleMessageListenerContainer simpleMessageListenerContainer = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.listener.2",
        mockMqListener);
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(10, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/lead-update-patch-command-workflowId-400.json"),
        mockMqListener.actualMessage,
        LENIENT);
    simpleMessageListenerContainer.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-with-pipeline-stage-reason-on-workflow.sql")
  public void givenLeadCreateEvent_withCustomAndCopyEditPropertyAction_ShouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given

    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/tenants/99/creator")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-v2-event-for-custom-copy.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.3", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/lead-update-patch-with-custom-and-copy-operation.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/create-workflow-with-marketplace-trigger-and-copy-actions.sql")
  public void givenMarketPlaceTriggerExecuteRequest_withCopyActionFromMarketplaceApp_shouldCopyFieldsAndPublishEvent()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/tenants/99/creator")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=1"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/lead-with-all-details-response.json"))));


    String resourceAsString = getResourceAsString("/contracts/mq/events/marketplace-trigger-with-all-fields.json");
    MarketplaceTriggerEvent marketplaceTriggerEvent = objectMapper.readValue(resourceAsString, MarketplaceTriggerEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.15", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, MarketplaceTriggerEvent.getMarketplaceTriggerExecuteEventName(), marketplaceTriggerEvent);

    // then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/lead-update-patch-command-5.json"),
        mockMqListener.actualMessage,
        new CustomComparator(STRICT,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MockMqListener mockMqListener) {

    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

}
