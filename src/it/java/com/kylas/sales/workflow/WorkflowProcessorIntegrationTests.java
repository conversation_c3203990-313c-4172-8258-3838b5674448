package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.CALL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.DEAL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.LENIENT;
import static org.skyscreamer.jsonassert.JSONCompareMode.STRICT;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.ScheduledJobFacade;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.command.ScheduleJobCommand;
import com.kylas.sales.workflow.mq.event.AssociatedContactWorkflowExecuteEvent;
import com.kylas.sales.workflow.mq.event.AssociatedDealWorkflowExecuteEvent;
import com.kylas.sales.workflow.mq.event.AssociatedLeadWorkflowExecuteEvent;
import com.kylas.sales.workflow.mq.event.CallLogEvent;
import com.kylas.sales.workflow.mq.event.DealEvent;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class WorkflowProcessorIntegrationTests {

  static final String LEAD_UPDATE_COMMAND_QUEUE = "workflow.lead.update";
  static final String LEAD_REASSIGN_COMMAND_QUEUE = "workflow.lead.reassign";
  static final String DEAL_REASSIGN_COMMAND_QUEUE = "workflow.deal.reassign";
  static final String DEAL_UPDATE_COMMAND_QUEUE = "workflow.deal.update";
  static final String CONTACT_UPDATE_COMMAND_QUEUE = "workflow.contact.update";
  static final String ASSOCIATED_LEAD_WORKFLOW_EXECUTE = "execute.associate.lead";
  static final String ASSOCIATED_DEAL_WORKFLOW_EXECUTE = "execute.associate.deal";
  static final String ASSOCIATED_CONTACT_WORKFLOW_EXECUTE = "execute.associate.contact";
  static final String GENERATE_CALL_SUMMARY_COMMAND = "workflow.generate.callLog.summary";
  static final String CALL_UPDATE_COMMAND_QUEUE = "workflow.callLog.update";

  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;


  @Test
  @Sql("/test-scripts/insert-create-lead-workflow.sql")
  public void givenLeadCreateEvent_withEligibleWorkflow_shouldEnqueueTheEventForProcessing()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);
    MockMqListener mockMqListener = new MockMqListener();
    SimpleMessageListenerContainer simpleMessageListenerContainer = initializeRabbitMqListener(LeadEvent.getProcessLeadCreatedCommandName(), "q.process.lead.created",
        mockMqListener);
    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/events/lead-created-event.json"),
        mockMqListener.actualMessage,
        LENIENT);
    simpleMessageListenerContainer.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-workflow.sql")
  public void leadCreateEvent_withNoEligibleWorkflowForTenant_shouldNotPublishCommand() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 100L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);
    MockMqListener mockMqListener = new MockMqListener();
    SimpleMessageListenerContainer simpleMessageListenerContainer = initializeRabbitMqListener(LeadEvent.getProcessLeadCreatedCommandName(), "q.process.lead.created.v3",
        mockMqListener);
    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event-tenant-id-100.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Assertions.assertThat(mockMqListener.actualMessage).isNull();
    simpleMessageListenerContainer.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-workflow.sql")
  public void leadCreateEvent_withExecuteWorkflowFlagAsFalse_shouldNotTriggerWorkflows() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);
    MockMqListener mockMqListener = new MockMqListener();
    SimpleMessageListenerContainer simpleMessageListenerContainer = initializeRabbitMqListener(LeadEvent.getProcessLeadCreatedCommandName(), "q.process.lead.created.v4",
        mockMqListener);
    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event-with-execute-workflow-flag.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Assertions.assertThat(mockMqListener.actualMessage).isNull();
    simpleMessageListenerContainer.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-with-custom-fields-workflow.sql")
  public void givenLeadCreateEvent_withCustomFields_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event-2.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.1", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/lead-update-patch-command-4.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-with-multi-value-custom-picklist.sql")
  public void givenLeadCreateEvent_withMultiValueCustomPicklistFields_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.2", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadCreatedCommandName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/lead-update-patch-command-with-multi-value-custom-picklist.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-create-lead-with-pipeline-stage-reason-on-workflow.sql")
  public void givenLeadCreateEvent_withPipelineStageReason_ShouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);
    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/iam/v1/tenants/99/creator")
            .withHeader(com.github.dockerjava.zerodep.shaded.org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.3", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(10, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("contracts/mq/command/lead-update-patch-with-pipeline-stage-reason.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-workflow.sql")
  public void givenLeadCreateEvent_shouldExecuteTwoWorkflowHavingActiveAndInactiveMarketplaceActionAndUpdateExecutedEvents()
      throws IOException, InterruptedException {
    // given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);

    Workflow workflow301 = workflowFacade.get(301);
    Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    Workflow workflow302 = workflowFacade.get(302);
    Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(16);
  }

  @Test
  @Sql("/test-scripts/insert-update-lead-workflow.sql")
  public void givenLeadUpdatedEvent_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    // given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/sales-lead-updated-event-payload.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.2", mockMqListener);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/lead-update-patch-command-3.json"),
        mockMqListener.actualMessage,
        LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/insert-update-lead-workflow.sql")
  public void givenLeadUpdatedEvent_tryToReProcessSameWorkflow_shouldNotProcess()
      throws IOException, InterruptedException {
    // given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString =
        getResourceAsString(
            "/contracts/mq/events/sales-lead-updated-event-payload-with-executedWorkflow.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(151);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MockMqListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {

      this.actualMessage = new String(messageInBytes);
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that verify workflow condition evaluation")
  class WorkflowConditionIntegrationTests {

    @Test
    @Sql("/test-scripts/integration/insert-workflows-with-condition.sql")
    public void givenLeadUpdatedEvent_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }


    @Test
    @Sql("/test-scripts/integration/insert-workflows-with-condition.sql")
    public void givenLeadUpdatedEventWithConditionNEW_VALUE_shouldNotTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-NEW-VALUE-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-condition-and-old-value-trigger.sql")
    public void givenLeadUpdatedEvent_withOldValueTriggerOn_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }


    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-condition-and-is-changed-trigger.sql")
    public void givenLeadUpdatedEvent_withIsChangedTriggerOn_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-condition-on-custom-field-and-is-changed-trigger.sql")
    public void givenLeadUpdatedEvent_withIsChangedTriggerOnCustomField_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-updated-event-with-custom-fields.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-condition-and-is-changed-trigger-idname-field.sql")
    public void givenLeadUpdatedEvent_withIsChangedTriggerOnIdNameField_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows-id-name-field.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }


    @Test
    @Sql("/test-scripts/integration/insert-workflow-with-multiple-condition.sql")
    public void givenLeadUpdatedEvent_withMultipleConditions_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-old-value-trigger-and-multiple-conditions.sql")
    public void givenLeadUpdatedEvent_withOldValueTriggerOnAndMultipleConditions_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-created-triggering-conditional-workflows.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-pipeline-stage.sql")
    public void givenLeadUpdatedEvent_withPipelineStage_shouldTriggerWorkflowConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-updated-event-with-pipeline.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-deal-workflow-with-pipeline-stage.sql")
    public void givenDealUpdatedEvent_withPipelineStage_shouldTriggerWorkflowConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/deal-updated-event-with-pipeline.json");
      DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      // when
      rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealUpdatedEventName(), dealEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-forecasting-type.sql")
    public void givenLeadUpdatedEvent_withForeCastingType_shouldTriggerWorkflowConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/lead-updated-event-with-forecasting-type.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-deal-workflow-with-forecasting-type.sql")
    public void givenDealUpdatedEvent_withForecastingType_shouldTriggerWorkflowConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/deal-updated-event-with-forecasting-type.json");
      DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      // when
      rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealUpdatedEventName(), dealEvent);
      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-workflows-with-condition.sql")
    public void givenLeadUpdatedEvent_withBetweenOperatorConditionOnDateField_shouldTriggerWorkflowConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString = getResourceAsString("/contracts/mq/events/lead-updated-event-with-datetime-fields.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);

      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);

      // then
      latch.await(2, TimeUnit.SECONDS);

      Workflow workflow = workflowFacade.get(304);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that publish reassign event when entity created/updated")
  class ReassignIntegrationTests {

    @Test
    @Sql("/test-scripts/insert-reassign-lead-workflow.sql")
    public void givenLeadCreatedEvent_shouldPublish_reassignEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/lead-created-reassign-event.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_REASSIGN_COMMAND_QUEUE, "q.updated.3", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/lead-reassign-patch-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/insert-reassign-update-lead-workflow.sql")
    public void givenLeadUpdatedEvent_shouldPublish_reassignEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/sales-lead-reassigned-event-payload.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_REASSIGN_COMMAND_QUEUE, "q.updated.4", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadUpdatedEventName(), leadEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/lead-reassign-patch-command-2.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/insert-reassign-deal-workflow.sql")
    public void givenDealCreatedEvent_shouldPublish_reassignEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/deal-created-reassign-event.json");
      DealEvent dealCreatedEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(DEAL_REASSIGN_COMMAND_QUEUE, "q.updated.5", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(
          DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealCreatedEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/deal-reassign-patch-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/insert-reassign-update-deal-workflow.sql")
    public void givenDealUpdatedEvent_shouldPublish_reassignEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/deal-updated-reassign-event-payload.json");
      DealEvent dealUpdatedEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(DEAL_REASSIGN_COMMAND_QUEUE, "q.updated.6", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(
          DEAL_EXCHANGE, DealEvent.getDealUpdatedEventName(), dealUpdatedEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/deal-reassign-patch-command-2.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-reassign-action.sql")
    public void givenEntityCreatedEvent_havingWorkflowWithReassignAction_shouldPublish_reassignEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/lead-created-reassign-event.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_REASSIGN_COMMAND_QUEUE, "q.updated.20", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/lead-reassign-to-created-by-patch-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(100);
      container.stop();
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  public class WorkflowProcessorMultiActionIntegrationTests {

    @Test
    @Sql("/test-scripts/integration/multiple-action-lead-workflow.sql")
    public void givenLeadCreateEvent_shouldExecuteMultipleActions()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.7", mockMqListener);
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
      // then
      mockMqListener.latch.await(3, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/lead-update-patch-command-2.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(788);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  public class DelayedActionWorkflowProcessorIntegrationTests {

    @Autowired
    ScheduledJobFacade scheduledJobFacade;

    @Test
    @Sql("/test-scripts/integration/add-delayed-action-for-execution.sql")
    public void givenLeadDelayedWorkflowAction_onJobScheduledTime_shouldExecuteAction()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      stubFor(
          post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=1"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/lead/lead-with-all-details-response.json"))));

      stubFor(
          get("/iam/v1/users/539")
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(
                          getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(post("/api/users").willReturn(aResponse().withStatus(200)));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/workflow-execute-scheduled-job-command.json");
      ScheduleJobCommand scheduleJobCommand = objectMapper.readValue(resourceAsString, ScheduleJobCommand.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.8", mockMqListener);
      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      verify(WireMock.postRequestedFor(urlEqualTo("/api/users")).withRequestBody(equalToJson(getResourceAsString("/contracts/webhook/webhook-test-response.json"))));

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/events/update-lead-with-delayed-action.json"),
          mockMqListener.actualMessage,
          LENIENT);
      Workflow workflow = workflowFacade.get(11);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(scheduledJobFacade.getUnExecutedJob(11L, 22L).isEmpty()).isTrue();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/add-delayed-action-for-execution.sql")
    public void givenLeadDelayedWorkflowAction_onJobScheduledTimeForMarketPlaceTrigger_shouldExecuteAction()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      stubFor(
          post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=1"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/lead/lead-with-all-details-response.json"))));

      stubFor(
          get("/iam/v1/users/539")
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(
                          getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(post("/api/users").willReturn(aResponse().withStatus(200)));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/workflow-execute-scheduled-job-command-wf13.json");
      ScheduleJobCommand scheduleJobCommand = objectMapper.readValue(resourceAsString, ScheduleJobCommand.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.9", mockMqListener);
      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      verify(WireMock.postRequestedFor(urlEqualTo("/api/users")).withRequestBody(equalToJson(getResourceAsString("/contracts/webhook/webhook-test-response.json"))));

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/events/update-lead-with-delayed-action-13.json"),
          mockMqListener.actualMessage,
          LENIENT);
      Workflow workflow = workflowFacade.get(13);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(scheduledJobFacade.getUnExecutedJob(13L, 24L).isEmpty()).isTrue();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/add-delayed-action-for-execution.sql")
    public void givenDealDelayedWorkflowAction_onJobScheduledTime_shouldExecuteAction()
        throws IOException, InterruptedException, JSONException {
      // given
      User aUser = UserStub.aUser(12L, 55L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      stubFor(
          post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=1"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/deal/deal-details-without-product-response.json"))));

      stubFor(
          get("/iam/v1/users/14")
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(
                          getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          get("/company/v1/companies/6547")
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(
                          getResourceAsString("/contracts/company/company-with-all-details.json"))));

      stubFor(
          get("/config/v1/currencies?id=431")
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/config/response/get-currency.json"))));

      stubFor(post("/api/deals").willReturn(aResponse().withStatus(200)));

      ScheduleJobCommand scheduleJobCommand = new ScheduleJobCommand(12L, 23L, 12L, 55L);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      verify(WireMock.postRequestedFor(urlEqualTo("/api/deals")).withRequestBody(equalToJson(getResourceAsString("/contracts/webhook/deal/deal-webhook-integration-test.json"))));

      Workflow workflow = workflowFacade.get(12);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(scheduledJobFacade.getUnExecutedJob(12L, 23L).isEmpty()).isTrue();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/call-log-associated-entity-workflow.sql")
    public void givenCallLogDelayedWorkflow_withAssociatedLeadAndDealWebhookAction_withJobScheduledTime_shouldExecuteWebhookOnAssociatedLeadAndDeal()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(120L, 196L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/iam/v1/users/539")
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(
                          getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          post(urlEqualTo("/call/v1/call-logs/search?sort=updatedAt,desc&page=1&size=1"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/call/call-log-search-request.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/config/response/call-search-response-with-associated-lead-and-deal.json"))));

      stubFor(
          post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

      stubFor(
          post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

      mockAPI(authenticationToken);

      stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b820").willReturn(aResponse().withStatus(200)));

      String resourceAsString =
          getResourceAsString("contracts/mq/events/workflow-execute-scheduled-job-command-for-id-196.json");
      ScheduleJobCommand scheduleJobCommand = objectMapper.readValue(resourceAsString, ScheduleJobCommand.class);
      // when

      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);
      // then
      latch.await(2, TimeUnit.SECONDS);


      Workflow workflow = workflowFacade.get(907);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b820"))
          .withRequestBody(equalToJson(getResourceAsString("/contracts/call/verify-webhook-request-on-call-associated-lead-and-deal.json"), true, false)));
    }

    private void mockAPI(String authenticationToken) throws IOException {

      stubFor(
          get("/sales/v1/contacts?id=14&view=full")
              .withHeader(AUTHORIZATION, matching("Bearer .*" ))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/contacts/contact-response.json"))));

      stubFor(
          get("/company/v1/companies/100")
              .withHeader(AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/company/company-with-all-details.json"))));

      stubFor(
          get("/product/v1/products/100")
              .withHeader(AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

      stubFor(
          get("/config/v1/currencies?id=400")
              .withHeader(AUTHORIZATION, matching("Bearer .*" ))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/config/response/get-currency.json"))));
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  public class AssociatedEntityWorkflowProcessorIntegrationTests{

    @Test
    @Sql("/test-scripts/integration/insert-associated-entity-event-workflow.sql")
    public void givenLeadAssociatedEvent_shouldExecuteWorkflow() throws IOException, InterruptedException, JSONException {
      // given
      stubFor(
          get(urlEqualTo(
              "/3e0d9676-ad3c-4cf2-a449-ca334e43b815?pipeline=LeadPipelineNew&product=CRM&product=Marketing&leadName=Tony&pipelineStage=Won"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)));

      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/associated-lead-workflow-execute-event.json");
      AssociatedLeadWorkflowExecuteEvent associatedWorkflowEntityEvent = objectMapper.readValue(resourceAsString, AssociatedLeadWorkflowExecuteEvent.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(LEAD_UPDATE_COMMAND_QUEUE, "q.updated.9", mockMqListener);

      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, AssociatedLeadWorkflowExecuteEvent.getExecuteAssociatedLeadEventName(), associatedWorkflowEntityEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      JSONAssert.assertEquals(
          getResourceAsString("contracts/mq/command/lead-update-patch-command-on-associate-lead-event.json"),
          mockMqListener.actualMessage,
          LENIENT);

      verify(
          WireMock.getRequestedFor(
              urlEqualTo(
                  "/3e0d9676-ad3c-4cf2-a449-ca334e43b815?pipeline=LeadPipelineNew&product=CRM&product=Marketing&leadName=Tony&pipelineStage=Won"))
      );

      Workflow workflow = workflowFacade.get(3990);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/insert-associated-entity-event-workflow.sql")
    public void givenDealAssociatedEvent_shouldExecuteWorkflow() throws IOException, InterruptedException, JSONException {
      // given

      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/associated-deal-workflow-execute-event.json");
      AssociatedDealWorkflowExecuteEvent associatedDealWorkflowExecuteEvent = objectMapper.readValue(resourceAsString, AssociatedDealWorkflowExecuteEvent.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(DEAL_UPDATE_COMMAND_QUEUE, "q.updated.10", mockMqListener);

      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, AssociatedDealWorkflowExecuteEvent.getExecuteAssociatedDealEventName(), associatedDealWorkflowExecuteEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/deal-update-patch-command-on-associated-deal-event.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(4000);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/insert-associated-entity-event-workflow.sql")
    public void givenContactAssociatedEvent_shouldExecuteWorkflow() throws IOException, InterruptedException, JSONException {
      // given

      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/associated-contact-workflow-execute-event.json");
      AssociatedContactWorkflowExecuteEvent associatedContactWorkflowExecuteEvent = objectMapper.readValue(resourceAsString, AssociatedContactWorkflowExecuteEvent.class);
      // when
      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, "q.updated.11", mockMqListener);

      rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, AssociatedContactWorkflowExecuteEvent.getExecuteAssociatedContactEventName(), associatedContactWorkflowExecuteEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/contact-update-patch-command-on-associated-contact-event.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(4010);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/integration/insert-workflow-with-trigger-action.sql")
    public void givenCallLogEventWithAssociatedEntities_onWorkflowWithTriggerWorkflowAction_shouldPublishedEventOnLeadContactAndDeals()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/iam/v1/users/115")
              .withHeader(AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          get("/iam/v1/tenants")
              .withHeader(AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/tenant-details.json"))));

      stubFor(
          post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

      stubFor(
          post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

      stubFor(
          post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
              .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
              .willReturn(
                  aResponse()
                      .withStatus(200)
                      .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                      .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/call-log-event-on-associated-lead-for-tenant-id-191.json");
      CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);

      MockMqMultiValueListener leadMockMqListener = new MockMqMultiValueListener();
      var container1 = initializeRabbitMqListener(ASSOCIATED_LEAD_WORKFLOW_EXECUTE, "q.updated.12", leadMockMqListener);

      MockMqMultiValueListener dealMockMqListener = new MockMqMultiValueListener();
      var container2 = initializeRabbitMqListener(ASSOCIATED_DEAL_WORKFLOW_EXECUTE, "q.updated.13", dealMockMqListener);

      MockMqMultiValueListener contactMockMqListener = new MockMqMultiValueListener();
      var container3 = initializeRabbitMqListener(ASSOCIATED_CONTACT_WORKFLOW_EXECUTE, "q.updated.14", contactMockMqListener);

      MockMqListener callMockMqListener = new MockMqListener();
      var container4 = initializeRabbitMqListener(CALL_UPDATE_COMMAND_QUEUE, "q.updated.34", callMockMqListener);

      // when
      rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
      contactMockMqListener.latch.await(5, TimeUnit.SECONDS);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/associated-lead-workflow-event.json"),
          leadMockMqListener.actualPayloads.toString(), LENIENT);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/associated-deal-workflow-event.json"),
          dealMockMqListener.actualPayloads.toString(), LENIENT);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/associated-contact-workflow-event.json"),
          contactMockMqListener.actualPayloads.toString(), LENIENT);

      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/call-log-patch-command.json"),
          callMockMqListener.actualMessage,
          new CustomComparator(STRICT, new Customization("metadata.eventId", (o1, o2) -> true)));

      container1.stop();
      container2.stop();
      container3.stop();
      container4.stop();
    }
  }
  class MockMqMultiValueListener {
    List<String> actualPayloads = new ArrayList<>();
    CountDownLatch latch = new CountDownLatch(3);

    public void receiveMessage(byte[]  messageInBytes) {
      actualPayloads.add(new String(messageInBytes));
      latch.countDown();
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  class GenerateCallSummaryIntegrationTests {

    @Test
    @Sql("/test-scripts/insert-call-log-workflow.sql")
    public void givenCallCreatedEvent_havingWorkflowWithGenerateCallSummaryAction_shouldPublish_generateCallSummaryEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(2L, 14L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/call-log-created-event.json");
      CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);

      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener(GENERATE_CALL_SUMMARY_COMMAND, "q.workflow.generate.call.summary", mockMqListener);
      // when
      rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/command/generate-call-summary-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(310);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
      container.stop();
    }
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MockMqMultiValueListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that publish convert lead event when entity created/updated")
  class ConvertLeadActionIntegrationTests {
    @Test
    @Sql("/test-scripts/insert-create-lead-workflow.sql")
    public void givenLeadCreateEvent_andWorkflowWithConvertLeadActionWithoutAnyEntity_shouldProcessItAndPublishEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);

      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener("workflow.lead.convert", "q.updated.30", mockMqListener);

      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);

      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("contracts/mq/command/lead-convert-without-any-entity-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
      container.stop();
    }

    @Test
    @Sql("/test-scripts/insert-create-lead-workflow.sql")
    public void givenLeadCreateEvent_andWorkflowWithConvertLeadActionToDealContactAndCompany_shouldProcessItAndPublishEvent()
        throws IOException, InterruptedException, JSONException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(15L, 75L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString = getResourceAsString("/contracts/mq/events/lead-created-event-for-conversion.json");
      LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);

      MockMqListener mockMqListener = new MockMqListener();
      var container = initializeRabbitMqListener("workflow.lead.convert", "q.updated.31", mockMqListener);

      stubFor(
          get("/config/v1/conversion-mappings")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/config/response/conversion-mapping-response.json"))));


      stubFor(
          get("/config/v1/entities/lead/fields")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

      stubFor(
          get("/config/v1/entities/contact/fields")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));

      stubFor(
          get("/deal/v1/deals/fields")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/deal/get-all-deal-fields.json"))));

      stubFor(
          get("/company/v1/companies/fields")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/config/response/get-all-company-fields.json"))));

      stubFor(
          get("/config/v1/currencies?id")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/config/response/get-currency.json"))));

      stubFor(
          get("/product/v1/products?id=1598&id=1599")
              .withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  aResponse()
                      .withHeader("Content-Type", "application/json")
                      .withStatus(200)
                      .withBody(getResourceAsString("/contracts/product/responses/multiple-products-details.json"))));

      stubFor(
          get("/iam/v1/tenants/75/creator")
              .withHeader(AUTHORIZATION, matching("Bearer .*"))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);

      // then
      mockMqListener.latch.await(4, TimeUnit.SECONDS);
      JSONAssert.assertEquals(
          getResourceAsString("contracts/mq/command/lead-convert-with-deal-contact-company-entity-command.json"),
          mockMqListener.actualMessage,
          LENIENT);

      Workflow workflow = workflowFacade.get(302);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(16);
      container.stop();
    }

  }

}
