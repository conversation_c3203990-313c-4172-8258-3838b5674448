package com.kylas.sales.workflow;

import static org.junit.jupiter.api.Assertions.fail;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.ExecutionLogRepository;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class ExecutionLogsIntegrationTests {
  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tt-nPLZWtVP29NgyujjWMfJLaIR5Rlxv_cyP43b_7SM";
  @Autowired
  Environment environment;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  WorkflowFacade workflowFacade;

  @Autowired
  private RabbitTemplate rabbitTemplate;
  CountDownLatch latch = new CountDownLatch(1);
  @Autowired
  ExecutionLogRepository executionLogRepository;

  @Test
  @Sql("/test-scripts/integration/create-execution-logs.sql")
  public void givenSearchRequest_shouldReturnExecutionLogsOfWorkflow_whereUserHasReadAllOnWorkflowEntity() throws IOException {
    // when
    var executionLogsResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows/execution-logs/search?page=0&size=10&sort=executedAt,desc")
            .contentType(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString(
            "contracts/workflow/api/integration/execution-logs-search-response.json");
    StepVerifier.create(executionLogsResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("content[0].executedAt", (o1, o2) -> true)
                    ));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/create-execution-logs.sql")
  public void givenExecutionLogId_shouldReturnLogDetails() throws IOException {
    // when
    var executionLog =
        buildWebClient()
            .get()
            .uri("/v1/workflows/execution-logs/80")
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString(
            "contracts/workflow/api/integration/execution-log-details.json");
    StepVerifier.create(executionLog)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("executedAt", (o1, o2) -> true)
                    ));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/create-execution-logs.sql")
  public void givenSearchRequestWithFilter_shouldReturnFilteredExecutionLogs() throws IOException {
    // given
    String filterRequest = getResourceAsString("contracts/execution-logs/responses/execution-log-filter-request.json");

    // when
    var executionLogsResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows/execution-logs/search?page=0&size=10&sort=executedAt,desc")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(filterRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString(
            "contracts/execution-logs/responses/execution-logs-filtered-response.json");
    StepVerifier.create(executionLogsResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(expectedResponse, json, JSONCompareMode.STRICT);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

}
