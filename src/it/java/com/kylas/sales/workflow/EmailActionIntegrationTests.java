package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.DEAL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.junit.jupiter.api.Assertions.fail;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.api.response.WorkflowDetail;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.mq.event.ContactEvent;
import com.kylas.sales.workflow.mq.event.DealEvent;
import com.kylas.sales.workflow.mq.event.EmailActionEvent;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class  EmailActionIntegrationTests {

  @Autowired
  Environment environment;

  private String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xzQ-Ih5N1nllqkqgsBdS1NJgqhgNVJi1hiSZcuOrxp8";

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test.sql"})
  public void givenLeadCreatedEvent_withEmailAction_shouldPublishEmailActionEvent() throws IOException, InterruptedException {
    //given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    MockMqListener mockMqListener = new MockMqListener();
    String resourceAsString = getResourceAsString("/contracts/mq/events/email-action-on-lead-created-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.1", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadCreatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(3, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/lead-create-email-action-event-response.json"),
          mockMqListener.actualMessage, JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test-record-primary-email.sql"})
  public void givenLeadUpdatedEvent_withEmailAction_withTypeRecordPrimaryEmail_shouldPublishEmailActionEvent()
      throws IOException, InterruptedException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/2")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/email-action-on-lead-updated-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.2", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadUpdatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/lead-update-email-action-event-response.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);

    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test-all-available-emails.sql"})
  public void givenLeadUpdatedEvent_withEmailAction_withTypeAllAvailableEmails_shouldPublishEmailActionEvent()
      throws IOException, InterruptedException {
    //given

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/email-action-on-lead-updated-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.3", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadUpdatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/lead-update-all-available-emails-response.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);

    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-contact-workflow-for-email-action-test-record-primary-email.sql"})
  public void givenContactCreatedEvent_withEmailAction_withTypeRecordPrimaryEmail_shouldPublishEmailActionEvent()
      throws IOException, InterruptedException {
    //given

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-on-contact-created-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.4", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/contact-create-email-action-event-response.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.STRICT);

    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test-lead.sql"})
  public void givenLeadUpdatedEvent_withEmailAction_shouldSendEmailToExistingLead()
      throws IOException, InterruptedException {
    //given

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-test-lead.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.5", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadUpdatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/email-action-test-lead-response.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test-lead.sql"})
  public void givenLeadUpdatedEvent_withEmailAction_shouldNotSendEmailToNonExistingLead()
      throws IOException, InterruptedException {
    //given

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-test-lead-1.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.6", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadUpdatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/email-action-test-lead-response-1.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test-lead.sql"})
  public void givenLeadUpdatedEvent_withEmailActionAndDifferentId_shouldSendEmailToExistingLeadExceptUpdatedId()
      throws IOException, InterruptedException {
    //given

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-test-lead-2.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.7", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, LeadEvent.getProcessLeadUpdatedCommandName(), leadEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/email-action-test-lead-response-2.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-deal-workflow-for-email-action-test-all-associated-contacts.sql"})
  public void givenDealCreatedEvent_withEmailAction_withTypeAssociatedContacts_shouldPublishEmailActionEvent()
      throws IOException, InterruptedException {
    //given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));

    stubFor(
        get("/search/v1/summaries/contacts?id=1&id=2")
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/contact-by-id.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-on-deal-created-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    MockMqListener mockMqListener = new MockMqListener();
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.8", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/deal-create-email-action-event-response.json"),
          mockMqListener.actualMessage,
          JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/301")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(152);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  @Sql({"/test-scripts/table-cleanup.sql", "/test-scripts/integration/insert-workflow-for-email-action-test.sql"})
  public void givenDealCreatedEvent_withEmailAction_andEmailTypeAsUserReportingManager_shouldPublishEmailActionEvent() throws IOException, InterruptedException {
    //given
    stubFor(
        get("/iam/v1/users/2")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    MockMqListener mockMqListener = new MockMqListener();
    String resourceAsString = getResourceAsString("/contracts/mq/email/email-action-on-deal-created-event.json");
    DealEvent dealEvent = objectMapper.readValue(resourceAsString, DealEvent.class);
    initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.listener.deal.email.1", mockMqListener);

    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealEvent.getDealCreatedEventName(), dealEvent);

    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    try {
      JSONAssert.assertEquals(
          getResourceAsString("/contracts/mq/email/deal-email-action-response.json"),
          mockMqListener.actualMessage, JSONCompareMode.NON_EXTENSIBLE);
    } catch (JSONException e) {
      fail(e.getMessage());
    }

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/303")
            .retrieve()
            .bodyToMono(String.class);

    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowDetail workflowDetail = objectMapper.readValue(json, WorkflowDetail.class);
                Assertions.assertThat(workflowDetail.getLastTriggeredAt()).isNotNull();
                Assertions.assertThat(workflowDetail.getTriggerCount()).isEqualTo(1);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  private void initializeRabbitMqListener(String command, String consumerQueue,
      MockMqListener mockMqListener) {

    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();

  }
  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }
}
