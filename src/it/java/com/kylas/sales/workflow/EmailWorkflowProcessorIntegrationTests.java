package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.EMAIL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.NON_EXTENSIBLE;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.command.ScheduleJobCommand;
import com.kylas.sales.workflow.mq.event.EmailEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class EmailWorkflowProcessorIntegrationTests {
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);
  static final String ASSOCIATED_LEAD_WORKFLOW_EXECUTE = "execute.associate.lead";
  static final String ASSOCIATED_DEAL_WORKFLOW_EXECUTE = "execute.associate.deal";
  static final String ASSOCIATED_CONTACT_WORKFLOW_EXECUTE = "execute.associate.contact";

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-email.sql")
  public void givenEmailCreatedEvent_andWebhookWorkflowWithPostMethod_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/email-created-v2-event.json");
    HashMap emailEvent = objectMapper.readValue(resourceAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(EMAIL_EXCHANGE, EmailEvent.getEmailCreatedEventName(), emailEvent);
    latch.await(3, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(901);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/email/verify-webhook-post-request-on-email.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-email.sql")
  public void givenEmailUpdatedEvent_andWebhookWorkflowWithGetMethod_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/email-updated-v2-event.json");
    HashMap emailEvent = objectMapper.readValue(resourceAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(EMAIL_EXCHANGE, EmailEvent.getEmailUpdatedEventName(), emailEvent);
    latch.await(3, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(902);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.getRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816?param18=Sample%20Email%20Subject&param19=Fri%20Nov%2011%2007:46:44%20UTC%202022&param17=90351&param21=276981&param22=16&param20=<EMAIL>&param20=<EMAIL>")));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-email.sql")
  public void givenEmailCreatedEvent_andWebhookWorkflowWithPostMethod_shouldExecuteWebhookWithAssociatedEntities() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b817").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/email-created-event-with-associated-entities.json");
    HashMap emailEvent = objectMapper.readValue(resourceAsString, HashMap.class);

    // when
    rabbitTemplate.convertAndSend(EMAIL_EXCHANGE, EmailEvent.getEmailCreatedEventName(), emailEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(903);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b817"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/email/verify-webhook-for-associated-entities-on-email.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-email.sql")
  public void givenEmailDelayedWorkflow_withWebhookAction_shouldExecuteAction() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        get("/email/v1/emails/90351")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/email/email-search-response.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b818").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/workflow-execute-scheduled-job-command-for-email.json");
    ScheduleJobCommand scheduleJobCommand = objectMapper.readValue(resourceAsString, ScheduleJobCommand.class);

    // when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);
    latch.await(3, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(904);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b818"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/email/verify-webhook-post-request-on-email.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/integration/insert-workflows-for-trigger-workflow-action.sql")
  public void givenEmailCreatedEvent_withAssociatedEntities_andWorkflowWithTriggerWorkflowActions_shouldPublishAssociatedEventForLeadContactAndDeals()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/email-created-v2-event-with-related-to.json");
    EmailEvent emailEvent = objectMapper.readValue(resourceAsString, EmailEvent.class);

    MockMqMultiValueListener leadMockMqListener = new MockMqMultiValueListener();
    var container1 = initializeRabbitMqMultiValueListener(ASSOCIATED_LEAD_WORKFLOW_EXECUTE, "q.exec.associate.lead.workflow.12", leadMockMqListener);

    MockMqMultiValueListener dealMockMqListener = new MockMqMultiValueListener();
    var container2 = initializeRabbitMqMultiValueListener(ASSOCIATED_DEAL_WORKFLOW_EXECUTE, "q.exec.associate.deal.workflow.13", dealMockMqListener);

    MockMqMultiValueListener contactMockMqListener = new MockMqMultiValueListener();
    var container3 = initializeRabbitMqMultiValueListener(ASSOCIATED_CONTACT_WORKFLOW_EXECUTE, "q.exec.associate.contact.workflow.14", contactMockMqListener);

    // when
    rabbitTemplate.convertAndSend(EMAIL_EXCHANGE, EmailEvent.getEmailCreatedEventName(), emailEvent);
    latch.await(5, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-lead-workflow-event-on-email.json"),
        leadMockMqListener.actualPayloads.toString(), NON_EXTENSIBLE);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-deal-workflow-event-on-email.json"),
        dealMockMqListener.actualPayloads.toString(), NON_EXTENSIBLE);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-contact-workflow-event-on-email.json"),
        contactMockMqListener.actualPayloads.toString(), NON_EXTENSIBLE);
    container1.stop();
    container2.stop();
    container3.stop();
  }


  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqMultiValueListener(String command, String consumerQueue,
      MockMqMultiValueListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  static class MockMqMultiValueListener {
    List<String> actualPayloads = new ArrayList<>();
    CountDownLatch latch = new CountDownLatch(3);

    public void receiveMessage(byte[]  messageInBytes) {
      actualPayloads.add(new String(messageInBytes));
      latch.countDown();
    }
  }
}
