package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.kylas.sales.workflow.error.ErrorCode.INVALID_VALUE_TYPE;
import static org.junit.jupiter.api.Assertions.fail;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.api.response.WorkflowSummary;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.mq.WorkflowEventPublisher;
import java.io.IOException;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException.BadRequest;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
public class TaskWorkflowIntegrationTests {

  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mDFO6HxEyT1P_cxaZePmCiYlxjpm_esz6bSdyvTJgxk";
  @Autowired
  Environment environment;
  @Autowired
  private ObjectMapper objectMapper;
  @MockBean
  private WorkflowEventPublisher workflowEventPublisher;

  @Test
  public void givenTaskWorkflowRequest_withEditPropertyActions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-workflow-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenTaskWorkflowRequest_withInvalidEditPropertyValueType_shouldThrow() throws IOException {
    //given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("contracts/workflow/api/create-task-workflow-request-with-invalid-edit-action-value-type.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/task-workflow-invalid-edit-property-action-type.json");
    StepVerifier.create(workflowResponse)
        .expectErrorMatches(throwable -> {
          try {
            JSONAssert.assertEquals(
                expectedResponse,
                ((BadRequest) throwable).getResponseBodyAsString(),
                new CustomComparator(
                    JSONCompareMode.STRICT,
                    new Customization("timestamp", (o1, o2) -> true)));
          } catch (JSONException e) {
            fail(e.getMessage());
          }
          return true;
        })
        .verify();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskWorkflowUpdateRequest_withEditPropertyActions_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  public void givenTaskDelayedWorkflowRequest_withScheduleConditionAndForAllTasksExecutionCondition_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-workflow-with-delayed-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskDelayedWorkflowRequest_withScheduleConditionAndForAllTasksExecutionCondition_shouldUpdateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("contracts/workflow/api/create-task-workflow-with-delayed-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("contracts/workflow/api/update-workflow-delayed-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  public void givenTaskWorkflowRequest_withAssignAction_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-workflow-with-assign-action.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskWorkflowUpdateRequest_withAssignAction_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-with-assign-to-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-with-assign-to-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  public void getWebhookConfigurationForTask_shouldReturnIt() throws IOException, JSONException {
    //given
    stubFor(
        get("/config/v1/entities/user/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-user-fields.json"))));

    stubFor(
        get("/config/v1/entities/task/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-task-fields.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/deal/v1/deals/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/deal/get-all-deal-fields.json"))));

    stubFor(
        get("/config/v1/entities/contact/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));

    stubFor(
        get("/company/v1/companies/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/company/get-all-company-fields.json"))));


    // when
    var response =
        buildWebClient()
            .get()
            .uri("/v1/workflows/webhook/TASK/config")
            .retrieve()
            .bodyToMono(String.class)
            .block();

    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/task-webhook-configuration.json");
    JSONAssert.assertEquals(expectedResponse, response, JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/insert-workflows-with-trigger-action.sql")
  public void givenTaskWorkflowRequest_withWebhookAndTriggerWorkflowActions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-workflow-with-webhook-and-triggger-workflow-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskWorkflowUpdateRequest_withWebhookAndAssignToAction_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-with-webhook-and-assign-to-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-with-webhook-and-assign-to-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  public void givenTaskWorkflowRequest_withConditions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-with-condition-workflow-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskWorkflowUpdateRequest_withConditions_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/search/v1/summaries/users?id=19&id=20")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-by-ids.json"))));

    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-task-request-with-condition.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-with-condition-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenWorkflowId_withConditionExpression_shouldGetIt() throws IOException, JSONException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/909").retrieve().bodyToMono(String.class).block();
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/get-workflow-response-with-condition-expression.json");
    JSONAssert.assertEquals(expectedResponse, workflowResponse, new CustomComparator(JSONCompareMode.STRICT,
        new Customization("createdAt", (o1, o2) -> true),
        new Customization("updatedAt", (o1, o2) -> true),
        new Customization("actions[0].id", (o1, o2) -> true),
        new Customization("actions[1].id", (o1, o2) -> true),
        new Customization("actions[0].payload.name", (o1, o2) -> true),
        new Customization("actions[1].payload.name", (o1, o2) -> true),
        new Customization("actions[0].payload.value", (o1, o2) -> true),
        new Customization("actions[1].payload.value", (o1, o2) -> true)));
  }

  @Test
  public void givenTaskWorkflowRequest_withSendEmailActions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-task-workflow-request-with-send-email-actions.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (JsonProcessingException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-task-workflow.sql")
  public void givenTaskWorkflowUpdateRequest_withSendEmailActions_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-request-with-send-email-actions.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/909")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-send-email-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @Test
  @Sql("/test-scripts/insert-workflows-with-trigger-action.sql")
  public void givenTaskWorkflowUpdateRequest_withTriggerWorkflowActions_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/794")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task-update-request-with-trigger-actions.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/313")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-task-workflow-with-trigger-actions-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();

  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
