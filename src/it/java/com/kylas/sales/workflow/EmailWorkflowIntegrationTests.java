package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static org.junit.jupiter.api.Assertions.fail;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.api.response.WorkflowSummary;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.mq.WorkflowEventPublisher;
import java.io.IOException;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
public class EmailWorkflowIntegrationTests {
  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxTZO8PNDJ2bDxgTOUIlo6zGKHQqhhokdjaSeGAKOGU";
  @Autowired
  Environment environment;
  @Autowired
  private ObjectMapper objectMapper;
  @MockBean
  private WorkflowEventPublisher workflowEventPublisher;

  @Test
  public void getWebhookConfigurationForEmail_shouldReturnIt() throws IOException, JSONException {
    //given
    stubFor(
        get("/config/v1/entities/user/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-user-fields.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/deal/v1/deals/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/deal/get-all-deal-fields.json"))));

    stubFor(
        get("/config/v1/entities/contact/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));

    // when
    var response =
        buildWebClient()
            .get()
            .uri("/v1/workflows/webhook/EMAIL/config")
            .retrieve()
            .bodyToMono(String.class)
            .block();

    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/email-webhook-configuration.json");
    JSONAssert.assertEquals(expectedResponse, response, JSONCompareMode.STRICT);
  }

  @Test
  public void givenWorkflowRequest_withWebhookAction_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/email/v1/emails/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/email/email-list-layout-response.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-email-workflow-with-webhook-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class)
            .block();

    // then
    WorkflowSummary workflowSummary = objectMapper.readValue(workflowResponse, WorkflowSummary.class);
    Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
    Mono<String> workflowResponseMono = buildWebClient().get().uri("/v1/workflows/" + workflowSummary.getId()).retrieve().bodyToMono(String.class);
    StepVerifier.create(workflowResponseMono)
        .assertNext(
            json -> {
              try {
                var expectedResponse =
                    getResourceAsString("/contracts/workflow/api/email-workflow-with-webhook-action-response.json");
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("id", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true)));
              } catch (IOException | JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }


  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-email.sql")
  public void givenWorkflowUpdateRequest_withWebhookAction_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/email/v1/emails/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/email/email-list-layout-response.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-email-workflow-with-webhook-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/902")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-email-workflow-with-webhook-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenDelayedWorkflowRequest_withConditionAndExecutionCondition_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/email/v1/emails/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/email/email-list-layout-response.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-email-delayed-workflow-with-conditions-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class)
            .block();

    // then
    WorkflowSummary workflowSummary = objectMapper.readValue(workflowResponse, WorkflowSummary.class);
    Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
    Mono<String> workflowResponseMono = buildWebClient().get().uri("/v1/workflows/" + workflowSummary.getId()).retrieve().bodyToMono(String.class);
    StepVerifier.create(workflowResponseMono)
        .assertNext(
            json -> {
              try {
                var expectedResponse =
                    getResourceAsString("/contracts/workflow/api/email-delayed-workflow-with-conditions-response.json");
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("id", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true)));
              } catch (IOException | JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-workflows-with-trigger-action.sql")
  public void givenWorkflowRequest_withWorkflowTriggerActions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/email/v1/emails/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/email/email-list-layout-response.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-email-workflow-with-trigger-associated-workflow-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class)
            .block();

    // then
    WorkflowSummary workflowSummary = objectMapper.readValue(workflowResponse, WorkflowSummary.class);
    Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
    Mono<String> workflowResponseMono = buildWebClient().get().uri("/v1/workflows/" + workflowSummary.getId()).retrieve().bodyToMono(String.class);
    StepVerifier.create(workflowResponseMono)
        .assertNext(
            json -> {
              try {
                var expectedResponse =
                    getResourceAsString("/contracts/workflow/api/email-workflow-with-trigger-associated-workflow-action-response.json");
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("id", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true)));
              } catch (IOException | JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }


  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
