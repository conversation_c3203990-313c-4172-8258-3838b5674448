package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.CALL_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.PRODUCTIVITY_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.BDDMockito.given;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.api.WorkflowService;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.ExecutionLogRepository;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog_;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob_;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import com.kylas.sales.workflow.mq.command.ScheduleJobCommand;
import com.kylas.sales.workflow.mq.event.CallLogEvent;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import com.kylas.sales.workflow.mq.event.MarketplaceTriggerEvent;
import com.kylas.sales.workflow.mq.event.TaskEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class WorkflowProcessor_WebhookIntegrationTests {


  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  ExecutionLogRepository executionLogRepository;
  @Autowired
  Environment environment;
  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tt-nPLZWtVP29NgyujjWMfJLaIR5Rlxv_cyP43b_7SM";


  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-task-with-associated-company.sql")
  public void givenTaskCreatedEvent_withWebhookAction_shouldExecuteWebhook() throws Exception {
    // given
    String authenticationToken = "some-token";
    User aUser = getStubbedUser();
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    TaskEvent taskCreatedEvent = objectMapper.readValue(getResourceAsString("contracts/workflow/api/task/task-create-event-via-workflow.json"), TaskEvent.class);

    stubFor(
        post(urlEqualTo("/search/v2/search/company?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("contracts/company/company-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("contracts/company/company-search-response.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b845").willReturn(aResponse().withStatus(200)));


    //when
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TaskEvent.getTaskCreatedEventName(), taskCreatedEvent);
    latch.await(2, TimeUnit.SECONDS);

    //then
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b845"))
        .withRequestBody(equalToJson(getResourceAsString("contracts/webhook/webhook-request-on-task-created-event.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow.sql")
  public void givenLeadCreateEvent_usingMethodGET_shouldExecuteAndCreateExecutionLog()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = getStubbedUser();
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b815?param5=[100ft]&multiValuePicklist1=&param3=SomeTenant&param4=123&param1=tony&param2=Stark")
        .willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    // then
    latch.await(2, TimeUnit.SECONDS);
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    List<ExecutionLog> executionLogs = executionLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(
        criteriaBuilder.equal(root.get(ExecutionLog_.WORKFLOW_ACTION), ActionType.WEBHOOK),
        criteriaBuilder.equal(root.get(ExecutionLog_.WORKFLOW_ID), 301L),
        criteriaBuilder.equal(root.get(ScheduledJob_.ENTITY_ID), 1L),
        criteriaBuilder.equal(root.get(ScheduledJob_.ENTITY_TYPE), EntityType.LEAD)
    ));
    Assertions.assertThat(executionLogs).hasSize(1);

    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/execution-logs/" + executionLogs.stream().findFirst().get().getId())
            .retrieve()
            .bodyToMono(String.class);
    var expectedResponse =
        getResourceAsString(
            "contracts/workflow/api/integration/lead-webhook-action-execution-log-success-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("executedAt", (o1, o2) -> true),
                        new Customization("id", (o1, o2) -> true)
                    ));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/create-post-webhook-workflow.sql")
  public void givenLeadCreateEvent_usingMethodPOST_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson("{\n"
            + "  \"Zip\" : \"411045\",\n"
            + "  \"Owner\" : \"RJ\",\n"
            + "  \"param5\" : [ \"100ft\" ],\n"
            + "  \"Address\" : \"company address\",\n"
            + "  \"my_text_field\" : \"123\",\n"
            + "  \"City\" : \"My City\",\n"
            + "  \"multiValuePicklist1\" : [ \"Load\", \"Aim\" ],\n"
            + "  \"param3\" : \"SomeTenant\",\n"
            + "  \"param1\" : \"tony\",\n"
            + "  \"param2\" : \"Stark\",\n"
            + "  \"State\" : \"My State\",\n"
            + "  \"Country\" : \"IN\",\n"
            + "  \"Pipeline reason\" : \"Bought product/service with competitor\",\n"
            + "  \"ConvertedBy\" : \"RJ\",\n"
            + "  \"ImportedBy\" : \"\",\n"
            + "  \"ExpectedClosureOn\" : \"Thu Jan 12 06:11:44 UTC 2023\",\n"
            + "  \"ActualClosureDate\" : \"Thu Jan 12 06:11:44 UTC 2023\"\n"
            + "}")));
  }

  @Test
  @Sql("/test-scripts/create-put-webhook-workflow.sql")
  public void givenLeadCreateEvent_usingMethodPUT_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(2, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson("{\"param5\":[\"100ft\"],\"param3\":\"SomeTenant\",\"param4\":\"123\",\"param1\":\"tony\",\"param2\":\"Stark\"}")));
  }


  @Test
  @Sql("/test-scripts/create-put-webhook-workflow-marketplace-trigger.sql")
  public void givenMarketPlaceTriggerExecuteRequest_withWebhookAction_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/539")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=1"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/lead-with-all-details-response.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/marketplace-trigger-execute.json");
    MarketplaceTriggerEvent marketplaceTriggerEvent = objectMapper.readValue(resourceAsString, MarketplaceTriggerEvent.class);
    // when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, MarketplaceTriggerEvent.getMarketplaceTriggerExecuteEventName(), marketplaceTriggerEvent);
    latch.await(3, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson(getResourceAsString("/contracts/lead/marketplace-trigger-webhook.json"))));
  }

  @Test
  @Sql("/test-scripts/create-put-webhook-workflow-having-multiValue-custom-parameter.sql")
  public void givenLeadCreateEvent_usingMethodPUT_AndHavingMultiValueCustomPicklist_shouldExecute()
      throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/10")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/lead-created-v2-event.json");
    LeadEvent leadEvent = objectMapper.readValue(resourceAsString, LeadEvent.class);
    // when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, LeadEvent.getLeadCreatedEventName(), leadEvent);
    latch.await(5, TimeUnit.SECONDS);
    // then
    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(WireMock.equalToJson("{\"multiValuePicklist2\":\"\",\"multiValuePicklist1\":[\"Load\",\"Aim\"],"
            + "\"multiValuePicklist3\":[\"First\",\"Second\",\"Third\"],\"param1\":\"123\",\"param2\":[\"100ft\"]}")));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-call.sql")
  public void givenCallLogCreatedEvent_usingMethodPOST_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-response.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-event-on-associated-lead.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);


    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(901);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/call/verify-webhook-request-on-call.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-call.sql")
  public void givenCallLogCreatedEvent_usingMethodGET_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-response.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));


    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-event-on-associated-lead.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);


    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(902);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

    WireMock.verify(WireMock.getRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816?Related%20To=195341&Related%20To=195342&Related%20To=195343&Outcome=NO_ANSWER&name=SomeTenant")));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-call.sql")
  public void givenCallLogCreatedEvent_usingPostMethod_shouldExecuteWebhookWorkflowWithAssociatedLeads() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b817").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-event-on-associated-lead-for-tenant-id-191.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(903);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b817"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/call/verify-webhook-request-on-call-associated-lead.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-call.sql")
  public void givenCallLogCreatedEvent_usingMethodPOST_shouldExecuteWebhookWorkflowWithAssociatedDeal() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(116L, 192L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/116")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    mockAPI(authenticationToken);


    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b818").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-event-on-associated-deals.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);


    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(904);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b818"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/call/verify-webhook-request-on-associated-deal.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-call.sql")
  public void givenCallLogCreatedEvent_usingMethodPOST_shouldExecuteWebhookWorkflowWithAssociatedContacts() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(117L, 193L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/117")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b819").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/call-log-event-on-associated-contact-1.json");
    CallLogEvent callLogEvent = objectMapper.readValue(resourceAsString, CallLogEvent.class);

    // when
    rabbitTemplate.convertAndSend(CALL_EXCHANGE, CallLogEvent.getCallLogDetailCreatedEventName(), callLogEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(905);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b819"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/call/verify-webhook-request-on-call-associated-contact.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/call-log-associated-entity-workflow.sql")
  public void givenCallLogDelayedWorkflow_withAssociatedContactWebhookAction_withJobScheduledTime_shouldExecuteWebhookWithAssociatedContacts()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(127L, 203L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/127")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));


    stubFor(
        post(urlEqualTo("/call/v1/call-logs/search?sort=updatedAt,desc&page=1&size=1"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/call/call-log-search-request.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("contracts/config/response/call-search-response-with-associated-contact-and-deal.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b821").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("contracts/mq/events/workflow-execute-scheduled-job-command-for-id-203.json");
    ScheduleJobCommand scheduleJobCommand = objectMapper.readValue(resourceAsString, ScheduleJobCommand.class);

    // when
    rabbitTemplate.convertAndSend(WORKFLOW_EXCHANGE, ScheduleJobCommand.getExecuteCommandName(), scheduleJobCommand);

    // then
    latch.await(2, TimeUnit.SECONDS);

    Workflow workflow = workflowFacade.get(914);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b821"))
        .withRequestBody(equalToJson(getResourceAsString("contracts/call/verify-webhook-request-on-delayed-workflow-for-associated-entities.json"), true, false)));
  }

  private void mockAPI(String authenticationToken) throws IOException {

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private User getStubbedUser() {
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");

    Action lead = new Action();
    lead.setRead(true);
    lead.setReadAll(true);
    Set<Permission> permissions = aUser.getPermissions();
    permissions.add(new Permission(11L, "lead", "lead", lead));
    aUser.withPermissions(permissions);
    return aUser;
  }

}
