package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.SALES_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.ContactEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class ContactWorkflowProcessorIntegrationTests {

  static final String SALES_CONTACT_UPDATE_QUEUE = "q.workflow.contact.update.sales";
  static final String SALES_CONTACT_UPDATE_QUEUE_NEW = "q.workflow.contact.update.sales_new";
  static final String SALES_CONTACT_UPDATE_QUEUE_WORKFlOW = "q.workflow.contact.update.sales_workflow";
  static final String CONTACT_UPDATE_COMMAND_QUEUE = "workflow.contact.update";
  static final String SALES_CONTACT_REASSIGN_QUEUE = "workflow.contact.reassign.sales";
  static final String SALES_CONTACT_REASSIGN_QUEUE_NEW = "workflow.contact.reassign.sales_new";
  static final String CONTACT_REASSIGN_COMMAND_QUEUE = "workflow.contact.reassign";

  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  private MockMqListener mockMqListener = new MockMqListener();
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;

  @Test
  @Sql("/test-scripts/insert-create-contact-workflow.sql")
  public void givenContactCreateEvent_shouldUpdatePropertyAndPublishCommand() throws IOException, InterruptedException, JSONException {
    //given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/insert-create-contact-workflow-with-custom-fields.sql")
  public void givenContactCreateEvent_withCustomFields_shouldUpdatePropertyAndPublishCommand()
      throws IOException, InterruptedException, JSONException {
    //given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-event-with-custom-fields.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert
        .assertEquals(getResourceAsString("/contracts/mq/command/contact-update-patch-command-with-custom-fields.json"), mockMqListener.actualMessage,
            JSONCompareMode.LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }


  @Test
  @Sql("/test-scripts/insert-contact-workflow.sql")
  public void givenContactCreateEvent_shouldExecuteTwoWorkflowAndUpdateExecutedEvents() throws IOException, InterruptedException, JSONException {
    //given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-event.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    Workflow workflow301 = workflowFacade.get(301);
    Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);

    Workflow workflow302 = workflowFacade.get(302);
    Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(16);
  }

  @Test
  @Sql("/test-scripts/insert-update-contact-workflow.sql")
  public void givenContactUpdatedEvent_shouldUpdatePropertyAndPublishCommand() throws IOException, InterruptedException, JSONException {
    //given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString = getResourceAsString("/contracts/mq/events/sales-contact-updated-event-payload.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE_NEW);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert
        .assertEquals(getResourceAsString("/contracts/mq/command/contact-update-patch-command-3.json"), mockMqListener.actualMessage,
            JSONCompareMode.LENIENT);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
  }

  @Test
  @Sql("/test-scripts/insert-update-contact-workflow.sql")
  public void givenContactUpdatedEvent_tryToReProcessSameWorkflow_shouldNotProcess() throws IOException, InterruptedException, JSONException {
    //given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString = getResourceAsString("/contracts/mq/events/sales-contact-updated-event-payload-with-executedWorkflow.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(151);
  }

  @Test
  @Sql("/test-scripts/integration/contact-workflow-400-with-conditions-and-actions.sql")
  public void givenContactCreateEvent_withConditionOnSourceCampaignAndUtmFields_andEditActionOnUTMField_shouldExecuteWorkflow() throws IOException, InterruptedException, JSONException {
    //given
    User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
        .withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);

    String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-v2-event-for-workflow-id-400.json");
    ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
    initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE_WORKFlOW);
    //when
    rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(),
        contactEvent);
    //then
    mockMqListener.latch.await(5, TimeUnit.SECONDS);
    JSONAssert
        .assertEquals(getResourceAsString("/contracts/mq/command/contact-update-patch-cammand-400.json"),
            mockMqListener.actualMessage,
            JSONCompareMode.LENIENT);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private void initializeRabbitMqListener(String command, String consumerQueue) {

    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);

    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that verify workflow condition evaluation")
  class ContactWorkflowConditionIntegrationTests {

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflows-with-condition.sql")
    public void givenContactUpdatedEvent_shouldTriggerWorkflowsConditionally() throws IOException, InterruptedException, JSONException {
      //given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
          .withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-triggering-conditional-workflows.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      //when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
          contactEvent);
      //then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflows-with-condition.sql")
    public void givenContactUpdatedEventWithConditionNEW_VALUE_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException, JSONException {
      //given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
          .withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-triggering-new-value-conditional-workflows.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      //when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
          contactEvent);
      //then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);
    }

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflow-with-multiple-condition.sql")
    public void givenContactUpdatedEvent_withMultipleConditions_shouldTriggerWorkflowsConditionally() throws IOException, InterruptedException {
      //given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
          .withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-triggering-conditional-workflows.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      //when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
          contactEvent);
      //then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(0);

      Workflow workflow302 = workflowFacade.get(302);
      Assertions.assertThat(workflow302.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflow-with-condition-and-old-value-trigger.sql")
    public void givenContactUpdatedEvent_withOldValueTriggerOn_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/contact-created-triggering-conditional-workflows.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(), contactEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

    }

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflow-with-condition-and-is-changed-trigger.sql")
    public void givenContactUpdatedEvent_withIsChangedTriggerOn_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/contact-created-triggering-conditional-workflows.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(), contactEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }

    @Test
    @Sql("/test-scripts/integration/insert-contact-workflow-with-condition-on-custom-field-and-is-changed-trigger.sql")
    public void givenContactUpdatedEvent_withIsChangedTriggerOnCustomField_shouldTriggerWorkflowsConditionally()
        throws IOException, InterruptedException {
      // given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString =
          getResourceAsString(
              "/contracts/mq/events/contact-updated-event-with-custom-fields.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(), contactEvent);
      // then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);

      Workflow workflow301 = workflowFacade.get(301);
      Assertions.assertThat(workflow301.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that execute webhooks on Contact event")
  class WebhookContactIntegrationTests {

    @Test
    @Sql("/test-scripts/create-webhook-workflow-for-contact.sql")
    public void givenContactCreateEvent_usingMethodGET_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/iam/v1/users/10")
              .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          get("/iam/v1/tenants")
              .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/tenant-details.json"))));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    }

    @Test
    @Sql("/test-scripts/create-post-webhook-workflow-for-contact.sql")
    public void givenContactCreateEvent_usingMethodPOST_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/iam/v1/users/10")
              .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          get("/iam/v1/tenants")
              .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/tenant-details.json"))));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
      mockMqListener.latch.await(3, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    }

    @Test
    @Sql("/test-scripts/create-put-webhook-workflow-for-contact.sql")
    public void givenContactCreateEvent_usingMethodPUT_shouldExecute()
        throws IOException, InterruptedException {
      // given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true).withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      stubFor(
          get("/iam/v1/users/10")
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
          get("/iam/v1/tenants")
              .willReturn(
                  okForContentType(
                      MediaType.APPLICATION_JSON_VALUE,
                      getResourceAsString("/contracts/user/responses/tenant-details.json"))));

      String resourceAsString =
          getResourceAsString("/contracts/mq/events/contact-created-v2-event.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_UPDATE_COMMAND_QUEUE, SALES_CONTACT_UPDATE_QUEUE);
      // when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(), contactEvent);
      mockMqListener.latch.await(3, TimeUnit.SECONDS);
      // then
      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    }
  }

  @Nested
  @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
  @AutoConfigureTestDatabase(replace = Replace.NONE)
  @ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
  @DisplayName("Tests that publish reassign event when contact created/updated")
  class ReassignIntegrationTests {

    @Test
    @Sql("/test-scripts/insert-reassign-contact-workflow.sql")
    public void givenContactCreatedEvent_shouldPublish_reassignEvent() throws IOException, InterruptedException, JSONException {
      //given
      String authenticationToken = "some-token";
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
          .withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);
      given(authService.getAuthenticationToken()).willReturn(authenticationToken);

      String resourceAsString = getResourceAsString("/contracts/mq/events/contact-created-reassign-event.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_REASSIGN_COMMAND_QUEUE, SALES_CONTACT_REASSIGN_QUEUE);
      //when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactCreatedEventName(),
          contactEvent);
      //then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert
          .assertEquals(getResourceAsString("/contracts/mq/command/contact-reassign-patch-command.json"), mockMqListener.actualMessage,
              JSONCompareMode.LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    }

    @Test
    @Sql("/test-scripts/insert-reassign-update-contact-workflow.sql")
    public void givenContactUpdatedEvent_shouldPublish_reassignEvent() throws IOException, InterruptedException, JSONException {
      //given
      User aUser = UserStub.aUser(12L, 99L, true, true, true, true, true)
          .withName("user 1");
      given(authService.getLoggedInUser()).willReturn(aUser);

      String resourceAsString = getResourceAsString("/contracts/mq/events/sales-contact-reassigned-event-payload.json");
      ContactEvent contactEvent = objectMapper.readValue(resourceAsString, ContactEvent.class);
      initializeRabbitMqListener(CONTACT_REASSIGN_COMMAND_QUEUE, SALES_CONTACT_REASSIGN_QUEUE_NEW);
      //when
      rabbitTemplate.convertAndSend(SALES_EXCHANGE, ContactEvent.getContactUpdatedEventName(),
          contactEvent);
      //then
      mockMqListener.latch.await(2, TimeUnit.SECONDS);
      JSONAssert
          .assertEquals(getResourceAsString("/contracts/mq/command/contact-reassign-patch-command-2.json"), mockMqListener.actualMessage,
              JSONCompareMode.LENIENT);

      Workflow workflow = workflowFacade.get(301);
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
      Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    }
  }
}
