package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.MEETING_EXCHANGE;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;
import static org.mockito.BDDMockito.given;
import static org.skyscreamer.jsonassert.JSONCompareMode.NON_EXTENSIBLE;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.EmailActionEvent;
import com.kylas.sales.workflow.mq.event.MeetingEvent;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.stubs.UserStub;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
public class MeetingWorkflowProcessorIntegrationTests {

  private static final String MEETING_UPDATE_COMMAND = "workflow.meeting.update";
  static final String ASSOCIATED_LEAD_WORKFLOW_EXECUTE = "execute.associate.lead";
  static final String ASSOCIATED_DEAL_WORKFLOW_EXECUTE = "execute.associate.deal";
  static final String ASSOCIATED_CONTACT_WORKFLOW_EXECUTE = "execute.associate.contact";

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  WorkflowFacade workflowFacade;
  @MockBean
  AuthService authService;
  CountDownLatch latch = new CountDownLatch(1);

  @Test
  @Sql("/test-scripts/insert-meeting-workflow-for-email-action.sql")
  public void givenMeetingCreatedEvent_shouldUpdatePropertiesAndPublishCommand() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/meeting-created-event.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    MeetingWorkflowProcessorIntegrationTests.MockMqListener mockMqListener = new MeetingWorkflowProcessorIntegrationTests.MockMqListener();
    var container = initializeRabbitMqListener(MEETING_UPDATE_COMMAND, "q.workflow.meeting.update", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/meeting-update-patch-command.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.from.to", (o1, o2) -> true),
            new Customization("entity.from.from", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-meeting-workflow-for-email-action.sql")
  public void givenMeetingUpdatedEvent_shouldUpdatePropertiesAndPublishCommand() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(60L, 60L, true, true, true, true, true).withName("user 1");

    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    String resourceAsString = getResourceAsString("/contracts/mq/events/meeting-updated-event.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    MeetingWorkflowProcessorIntegrationTests.MockMqListener mockMqListener = new MeetingWorkflowProcessorIntegrationTests.MockMqListener();
    var container = initializeRabbitMqListener(MEETING_UPDATE_COMMAND, "q.workflow.meeting.update", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingUpdatedEventName(), meetingEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);


    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/meeting-update-patch-command-2.json"),
        mockMqListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.from.to", (o1, o2) -> true),
            new Customization("entity.from.from", (o1, o2) -> true),
            new Customization("metadata.eventId", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(308);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-meeting-workflow-for-email-action.sql")
  public void givenMeetingCreatedEvent_withEmailAction_shouldPublishEmailActionEvent() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));


    String resourceAsString = getResourceAsString("/contracts/mq/events/meeting-created-event-for-email-action.json");

    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    MeetingWorkflowProcessorIntegrationTests.MockMqListener mockMqListener = new MeetingWorkflowProcessorIntegrationTests.MockMqListener();
    var container = initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.workflow.meeting.send.email", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);


    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/meeting-create-email-action-event-response.json"),
        mockMqListener.actualMessage,
        new CustomComparator(NON_EXTENSIBLE,
            new Customization("metadata.eventId", (o1, o2) -> true)));

    Workflow workflow = workflowFacade.get(301);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/insert-meeting-workflow-for-email-action.sql")
  public void givenMeetingUpdatedEvent_withEmailAction_shouldPublishEmailActionEvent() throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(60L, 60L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/3")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-1.json"))));

    stubFor(
        get("/iam/v1/users/4")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-2.json"))));


    stubFor(
        get("/iam/v1/users/114")
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/email-action-user-details-by-id-114.json"))));

    String resourceAsString = getResourceAsString("/contracts/mq/events/meeting-updated-event-for-email-action.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    MeetingWorkflowProcessorIntegrationTests.MockMqListener mockMqListener = new MeetingWorkflowProcessorIntegrationTests.MockMqListener();
    var container = initializeRabbitMqListener(EmailActionEvent.getEventName(), "q.workflow.meeting.send.email.2", mockMqListener);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingUpdatedEventName(), meetingEvent);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/meeting-update-email-action-response.json"),
        mockMqListener.actualMessage,
        new CustomComparator(NON_EXTENSIBLE,
            new Customization("metadata.eventId", (o1, o2) -> true))
    );

    Workflow workflow = workflowFacade.get(308);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getLastTriggeredAt()).isNotNull();
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    container.stop();
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-meeting.sql")
  public void givenMeetingCreatedEvent_usingMethodGET_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(get("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/meeting-created-event.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(902);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);

    WireMock.verify(WireMock.getRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816?Organizer=Walter%20White&invitees=114"
        + "&invitees=6781&id=3763&title=New%20Meeting%20with%20user%20as%20invitee&relation=5999016")));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-meeting.sql")
  public void givenMeetingCreatedEvent_usingMethodPOST_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b815").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/meeting-created-event.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(901);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b815"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/meeting/verify-webhook-request-on-meeting.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/create-webhook-workflow-for-meeting.sql")
  public void givenMeetingCreatedEvent_usingMethodPUT_shouldExecuteWebhookWorkflow() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(114L, 190L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/114")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(put("/3e0d9676-ad3c-4cf2-a449-ca334e43b817").willReturn(aResponse().withStatus(200)));

    String resourceAsString = getResourceAsString("/contracts/mq/events/meeting-created-event.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(903);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(1);
    WireMock.verify(WireMock.putRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b817"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/meeting/verify-webhook-request-on-put-method.json"), true, false)));
  }

  @Test
  @Sql("/test-scripts/integration/insert-workflows-for-trigger-workflow-action.sql")
  public void givenMeetingCreatedEventWithAssociatedEntities_onWorkflowWithTriggerWorkflowAction_shouldPublishedEventOnLeadContactAndDeals()
      throws IOException, InterruptedException, JSONException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/meeting-event-with-related-entities.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    MockMqMultiValueListener leadMockMqListener = new MockMqMultiValueListener();
    var container1 = initializeRabbitMqMultiValueListener(ASSOCIATED_LEAD_WORKFLOW_EXECUTE, "q.updated.12", leadMockMqListener);

    MockMqMultiValueListener dealMockMqListener = new MockMqMultiValueListener();
    var container2 = initializeRabbitMqMultiValueListener(ASSOCIATED_DEAL_WORKFLOW_EXECUTE, "q.updated.13", dealMockMqListener);

    MockMqMultiValueListener contactMockMqListener = new MockMqMultiValueListener();
    var container3 = initializeRabbitMqMultiValueListener(ASSOCIATED_CONTACT_WORKFLOW_EXECUTE, "q.updated.14", contactMockMqListener);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    contactMockMqListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-lead-workflow-event-on-meeting.json"),
        leadMockMqListener.actualPayloads.toString(), NON_EXTENSIBLE);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-deal-workflow-event-on-meeting.json"),
        dealMockMqListener.actualPayloads.toString(), NON_EXTENSIBLE);

    JSONAssert.assertEquals(
        getResourceAsString("/contracts/mq/command/associated-contact-workflow-event-on-meeting.json"),
        contactMockMqListener.actualPayloads.toString(), new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("[*].metadata.eventId", (o1, o2) -> true)
        ));
    container1.stop();
    container2.stop();
    container3.stop();
  }

  @Test
  @Sql("/test-scripts/integration/insert-meeting-workflow-with-associated-webhook-parameter.sql")
  public void givenMeetingCreatedEvent_usingPostMethod_shouldExecuteWebhookWithAssociatedEntities() throws IOException, InterruptedException {
    // given
    String authenticationToken = "some-token";
    User aUser = UserStub.aUser(115L, 191L, true, true, true, true, true).withName("user 1");
    given(authService.getLoggedInUser()).willReturn(aUser);
    given(authService.getAuthenticationToken()).willReturn(authenticationToken);

    stubFor(
        get("/iam/v1/users/115")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/tenants")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/user/responses/tenant-details.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/lead?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/lead/lead-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/lead/multiple-lead-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/deal?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/deal/deal-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/deal/multiple-deal-search-response.json"))));

    stubFor(
        post(urlEqualTo("/search/v2/search/contact?sort=updatedAt,desc&page=0&size=100"))
            .withRequestBody(equalToJson(getResourceAsString("/contracts/contacts/contact-search-request-with-ids.json")))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("/contracts/contacts/multiple-contact-response.json"))));

    mockAPI(authenticationToken);

    stubFor(post("/3e0d9676-ad3c-4cf2-a449-ca334e43b816").willReturn(aResponse().withStatus(200)));

    String resourceAsString =
        getResourceAsString("/contracts/mq/events/meeting-event-with-related-entities.json");
    MeetingEvent meetingEvent = objectMapper.readValue(resourceAsString, MeetingEvent.class);

    // when
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MeetingEvent.getMeetingCreatedEventName(), meetingEvent);
    latch.await(2, TimeUnit.SECONDS);

    // then
    Workflow workflow = workflowFacade.get(312);
    Assertions.assertThat(workflow.getWorkflowExecutedEvent().getTriggerCount()).isEqualTo(152);
    WireMock.verify(WireMock.postRequestedFor(urlEqualTo("/3e0d9676-ad3c-4cf2-a449-ca334e43b816"))
        .withRequestBody(equalToJson(getResourceAsString("/contracts/meeting/verify-webhook-response-on-associated-entities-on-meeting.json"), true, false)));
  }

  private void mockAPI(String authenticationToken) throws IOException {

    stubFor(
        get("/sales/v1/contacts?id=14&view=full")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/contacts/contact-response.json"))));

    stubFor(
        get("/company/v1/companies/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/company/company-with-all-details.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer .*"))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/product/responses/product-with-all-details.json"))));

    stubFor(
        get("/config/v1/currencies?id=400")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                okForContentType(
                    MediaType.APPLICATION_JSON_VALUE,
                    getResourceAsString("/contracts/config/response/get-currency.json"))));
  }

  class MockMqMultiValueListener {
    List<String> actualPayloads = new ArrayList<>();
    CountDownLatch latch = new CountDownLatch(3);

    public void receiveMessage(byte[]  messageInBytes) {
      actualPayloads.add(new String(messageInBytes));
      latch.countDown();
    }
  }

  private SimpleMessageListenerContainer initializeRabbitMqMultiValueListener(String command, String consumerQueue,
      MockMqMultiValueListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String command, String consumerQueue,
      MeetingWorkflowProcessorIntegrationTests.MockMqListener mockMqListener) {
    Queue queue = new Queue(consumerQueue);
    rabbitAdmin.declareQueue(queue);

    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(WORKFLOW_EXCHANGE))
            .with(command));

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

}
