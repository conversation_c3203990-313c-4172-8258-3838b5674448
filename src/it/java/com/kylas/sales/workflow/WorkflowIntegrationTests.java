package com.kylas.sales.workflow;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static org.junit.jupiter.api.Assertions.fail;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.api.response.WorkflowSummary;
import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.mq.WorkflowEventPublisher;
import java.io.IOException;
import javax.transaction.Transactional;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException.BadRequest;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureWireMock(port = 9090)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
public class WorkflowIntegrationTests {

  private final String authenticationToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xzQ-Ih5N1nllqkqgsBdS1NJgqhgNVJi1hiSZcuOrxp8";
  @Autowired
  Environment environment;
  @Autowired
  private ObjectMapper objectMapper;

  @MockBean
  private WorkflowEventPublisher workflowEventPublisher;

  @Test
  public void givenWorkflowRequest_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-workflow-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/create-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withCreateTaskAction_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    stubFor(
        get("/config/v1/ui/layouts/list/task")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/task/task-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-create-workflow-with-create-task-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/create-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Transactional
  public void givenLeadWorkflowRequest_withCreateTaskActionHavingCustomFields_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-create-workflow-with-create-task-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then

    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/create-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withShareAction_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/iam/v1/users/13")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-with-lead-permission.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-lead-workflow-with-share-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-workflow-with-trigger-action.sql")
  public void givenCallLogWorkflowRequest_withEditPropertyActionAndTriggerWorkflowAction_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/call/v1/call-logs/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/config/response/get-all-call-fields.json"))));

    stubFor(
        get("/call/v1/call-logs/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/call/call-log-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/callLog-workflow-create-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    workflowResponse.block();
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withInvalidShareAction_shouldThrow() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/iam/v1/users/13")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-with-lead-permission.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-lead-workflow-with-invalid-share-action.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/invalid-share-action-bad-request-workflow.json");
    StepVerifier.create(workflowResponse)
        .expectErrorMatches(throwable -> {
          try {
            JSONAssert.assertEquals(
                expectedResponse,
                ((BadRequest) throwable).getResponseBodyAsString(),
                new CustomComparator(
                    JSONCompareMode.STRICT,
                    new Customization("timestamp", (o1, o2) -> true)));
          } catch (JSONException e) {
            fail(e.getMessage());
          }
          return true;
        })
        .verify();
  }

  @Test
  public void givenLeadWorkflowRequest_withDelayedAction_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-create-delayed-workflow.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withEditFieldAndWebhookAction_onCustomFields_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/integration/lead-workflow-request-editfield-webhook-customfield.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withMarketplaceAction_AndWithPipelineStageReasons_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/integration/lead-workflow-request-with-marketplace-actions.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }


  @Test
  public void givenLeadWorkflowRequest_withSendEmailAction_shouldCreate() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/email/lead-create-workflow-with-multivalue-custom-picklist-and-email-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/create-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenWorkflowUpdateRequest_shouldUpdate() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

      stubFor(
              get("/iam/v1/users/13")
                      .withHeader(AUTHORIZATION, containing("Bearer"))
                      .willReturn(
                              aResponse()
                                      .withHeader("Content-Type", "application/json")
                                      .withStatus(200)
                                      .withBody(
                                              getResourceAsString("/contracts/user/responses/user-with-lead-permission.json"))));


      stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-workflow-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/update-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

    @Test
    @Sql("/test-scripts/integration/insert-lead-workflow-with-share-action.sql")
    public void givenWorkflowUpdateRequest_WithShareActionAndReassignActionShouldUpdate() throws IOException {

        stubFor(
                get("/iam/v1/users/12")
                        .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
                        .willReturn(
                                aResponse()
                                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                                        .withStatus(200)
                                        .withBody(
                                                getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

        stubFor(
                get("/iam/v1/users/13")
                        .withHeader(AUTHORIZATION, containing("Bearer"))
                        .willReturn(
                                aResponse()
                                        .withHeader("Content-Type", "application/json")
                                        .withStatus(200)
                                        .withBody(
                                                getResourceAsString("/contracts/user/responses/user-with-lead-permission.json"))));


        stubFor(
              get("/sales/v1/leads/layout/list")
                      .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
                      .willReturn(
                              aResponse()
                                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                                        .withStatus(200)
                                        .withBody(
                                            getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

        var workflowRequest =
                getResourceAsString("/contracts/workflow/api/share-and-reassign-action-update-workflow-request.json");

        // when
        var workflowResponse =
                buildWebClient()
                        .put()
                        .uri("/v1/workflows/301")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(workflowRequest)
                        .retrieve()
                        .bodyToMono(String.class);
        // then
        var expectedResponse =
                getResourceAsString("/contracts/workflow/api/integration/update-workflow-response-with-share-and-reassign-action.json");
        StepVerifier.create(workflowResponse)
                .assertNext(
                        json -> {
                            try {
                                JSONAssert.assertEquals(
                                        expectedResponse,
                                        json,
                                        new CustomComparator(
                                                JSONCompareMode.STRICT,
                                                new Customization("actions[0].id", (o1, o2) -> true),
                                                new Customization("actions[0].type", (o1, o2) -> true),
                                                new Customization("actions[0].payload", (o1, o2) -> true),
                                                new Customization("actions[1].id", (o1, o2) -> true),
                                                new Customization("actions[1].type", (o1, o2) -> true),
                                                new Customization("actions[1].payload", (o1, o2) -> true),
                                                new Customization("actions[2].id", (o1, o2) -> true),
                                                new Customization("actions[2].type", (o1, o2) -> true),
                                                new Customization("actions[2].payload", (o1, o2) -> true),
                                                new Customization("actions[3].id", (o1, o2) -> true),
                                                new Customization("actions[3].type", (o1, o2) -> true),
                                                new Customization("actions[3].payload", (o1, o2) -> true),
                                                new Customization("lastTriggeredAt", (o1, o2) -> true),
                                                new Customization("updatedAt", (o1, o2) -> true),
                                                new Customization("createdAt", (o1, o2) -> true)));
                            } catch (JSONException e) {
                                fail(e.getMessage());
                            }
                        })
                .verifyComplete();
    }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withNewTaskAction_AndMultiValueCustomPicklist_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-update-workflow-with-create-task-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/task/lead-update-workflow-with-create-task-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withNewTaskActionHavingCustomFieldValues_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-update-workflow-with-create-task-action-having-custom-field-values-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("contracts/workflow/api/task/lead-update-workflow-with-create-task-action-having-custom-field-values-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-editfield-webhook-on-custom-fields.sql")
  public void givenLeadWorkflowRequest_withEditFieldAndWebhookAction_onCustomFields_shouldUpdate() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/task/lead-update-workflow-with-create-task-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/task/lead-update-workflow-with-create-task-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withSendEmailAction_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/email/lead-update-workflow-with-send-email-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/email/lead-update-workflow-with-send-email-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withMarketplaceAction_AndPipelineStageWithReasons_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/marketplaceActions/lead-update-workflow-with-marketplace-action-request.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/marketplaceActions/lead-update-workflow-with-marketplace-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withDelayedAction_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/integration/update-event-workflow-to-delayed.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/303")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/update-event-based-to-delayed-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenWorkflowId_shouldGetIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflowId-301-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[5].id", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[5].type", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true),
                        new Customization("actions[5].payload", (o1, o2) -> true),
                        new Customization("actions[0].actionId", (o1, o2) -> true),
                        new Customization("actions[1].actionId", (o1, o2) -> true),
                        new Customization("actions[2].actionId", (o1, o2) -> true),
                        new Customization("actions[3].actionId", (o1, o2) -> true),
                        new Customization("actions[0].appId", (o1, o2) -> true),
                        new Customization("actions[1].appId", (o1, o2) -> true),
                        new Customization("actions[2].appId", (o1, o2) -> true),
                        new Customization("actions[3].appId", (o1, o2) -> true),
                        new Customization("actions[0].active", (o1, o2) -> true),
                        new Customization("actions[1].active", (o1, o2) -> true),
                        new Customization("actions[2].active", (o1, o2) -> true),
                        new Customization("actions[3].active", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/workflow-with-condition-expression.sql")
  public void givenWorkflowId_withConditionExpression_shouldGetIt() throws IOException, JSONException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class).block();
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflow-with-condition-response.json");
    JSONAssert.assertEquals(expectedResponse, workflowResponse, JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/integration/lead-workflow-with-condition-expression-and-old-value-trigger.sql")
  public void givenLeadWorkflowId_withConditionExpressionAndOldValueTriggerType_shouldGetIt() throws IOException, JSONException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class).block();
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflow-with-condition-old-value-response.json");
    JSONAssert.assertEquals(expectedResponse, workflowResponse, JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-scripts/integration/contact-workflow-with-condition-expression-and-old-value-trigger.sql")
  public void givenContactWorkflowId_withConditionExpressionAndOldValueTriggerType_shouldGetIt() throws IOException, JSONException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class).block();
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/contact-workflow-with-condition-old-value-response.json");
    JSONAssert.assertEquals(expectedResponse, workflowResponse, JSONCompareMode.LENIENT);
  }


  @Test
  @Sql("/test-scripts/integration/lead-workflow-with-pipeline-edit-property.sql")
  public void givenWorkflowId_havingPipelineEditProperty_shouldGetIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/search/v1/summaries/pipeline?id=1")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/pipeline/responses/pipeline-details.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflowId-301-with-pipeline-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json, JSONCompareMode.STRICT);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/lead-workflow-with-product-edit-property.sql")
  public void givenWorkflowId_havingProductEditProperty_shouldGetIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/product/v1/products/100")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/pipeline/responses/product-details.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/302").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflowId-301-with-product-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/custom-param-webhook-workflow.sql")
  public void givenWorkflowId_withCustomWebhookParam_shouldGetIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/custom-param-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(expectedResponse, json, JSONCompareMode.LENIENT);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenSearchRequest_tryToSortOnLastTriggeredAt_shouldSortAndGet() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows/search?page=0&size=10&sort=lastTriggeredAt,desc")
            .contentType(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString(
            "/contracts/workflow/api/integration/search-sort-on-lastTriggeredAt.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    JSONCompareMode.NON_EXTENSIBLE);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void getWebhookConfigurations_shouldFetchIt() throws IOException, JSONException {
    stubFor(
        get("/config/v1/entities/user/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-user-fields.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/config/v1/entities/contact/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));
    //when
    String response = buildWebClient()
        .get()
        .uri("/v1/workflows/webhook/LEAD/config")
        .retrieve()
        .bodyToMono(String.class)
        .block();
    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/webhook-configuration.json");
    JSONAssert.assertEquals(expectedResponse, response, JSONCompareMode.STRICT);

  }

  @Test
  @Sql("/test-scripts/insert-marketplace-trigger.sql")
  public void getWebhookConfigurationsWithTriggerId_shouldFetchIt() throws IOException, JSONException {
    stubFor(
        get("/config/v1/entities/user/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-user-fields.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/config/v1/entities/contact/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));
    //when
    String response = buildWebClient()
        .get()
        .uri("/v1/workflows/webhook/LEAD/config?triggerId=1135589a-746c-4daf-a710-3bdb1be7c2d8")
        .retrieve()
        .bodyToMono(String.class)
        .block();
    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/webhook-configuration-with-marketplace-trigger.json");
    JSONAssert.assertEquals(expectedResponse, response, JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-to-search.sql")
  public void givenSearchRequest_tryToFilter_shouldGet() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows/search?page=0&size=10&sort=lastTriggeredAt,desc")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(getResourceAsString("/contracts/workflow/api/integration/search-workflow-apply-filter-request.json"))
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString(
            "contracts/workflow/api/integration/search-workflow-apply-filter-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("content[0].actions[0].id", (o1, o2) -> true),
                        new Customization("content[0].lastTriggeredAt", (o1, o2) -> true),
                        new Customization("content[0].createdAt", (o1, o2) -> true),
                        new Customization("content[0].updatedAt", (o1, o2) -> true),
                        new Customization("content[1].lastTriggeredAt", (o1, o2) -> true),
                        new Customization("content[1].createdAt", (o1, o2) -> true),
                        new Customization("content[1].updatedAt", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-with-multiValue-picklist-condition.sql")
  public void givenWorkflowId_shouldGetWorkflowWithMultiValueCustomPicklistAndWithPipelineStages() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/5")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/iam/v1/users/20003")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/301").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflow-response-for-multivalue-picklist.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[1].id", (o1, o2) -> true),
                        new Customization("actions[2].id", (o1, o2) -> true),
                        new Customization("actions[3].id", (o1, o2) -> true),
                        new Customization("actions[4].id", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[1].type", (o1, o2) -> true),
                        new Customization("actions[2].type", (o1, o2) -> true),
                        new Customization("actions[3].type", (o1, o2) -> true),
                        new Customization("actions[4].type", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("actions[1].payload", (o1, o2) -> true),
                        new Customization("actions[2].payload", (o1, o2) -> true),
                        new Customization("actions[3].payload", (o1, o2) -> true),
                        new Customization("actions[4].payload", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/test-workflow-for-email-template-id.sql")
  public void givenEmailTemplateId_shouldGetAssociatedWorkflowNames() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/email/2/list").retrieve().bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflow-names-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    JSONCompareMode.NON_EXTENSIBLE);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void getWebhookConfigurationForCallLog_shouldReturnIt() throws IOException, JSONException {
    //given
    stubFor(
        get("/config/v1/entities/user/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-user-fields.json"))));

    stubFor(
        get("/call/v1/call-logs/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/call-log-field-response.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));

    stubFor(
        get("/deal/v1/deals/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/deal/get-all-deal-fields.json"))));

    stubFor(
        get("/config/v1/entities/contact/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-contact-fields.json"))));

    // when
    var response =
        buildWebClient()
            .get()
            .uri("/v1/workflows/webhook/CALL_LOG/config")
            .retrieve()
            .bodyToMono(String.class)
            .block();

    //then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/call-log-webhook-configuration.json");
    JSONAssert.assertEquals(expectedResponse, response, JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-scripts/insert-call-log-workflow.sql")
  public void givenCallLogWorkflowUpdateRequest_withAssociatedEntityParametersOnWebhookAndGenerateCalSummaryActions_shouldUpdateIt() throws IOException {

    final String authenticationToken1 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8Sp28HFMP4O0PE_32A6nyP0-XBC1UOoBOdYZ69frbBQ";

    stubFor(
        get("/iam/v1/users/60")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken1))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/call/v1/call-logs/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken1))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/call/call-log-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-call-log-workflow-with-webhook-action-request.json");

    // when
    var workflowResponse =
        WebClient.builder()
            .baseUrl("http://localhost:" + environment.getProperty("local.server.port"))
            .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
            .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken1)
            .build()
            .put()
            .uri("/v1/workflows/308")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-call-log-workflow-with-webhook-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-workflow.sql")
  public void givenWorkflowNameWithEntity_shouldReturnAssociatedWorkflowLookUp() throws IOException {
    //given
    stubFor(
        get("/iam/v1/users/13")
            .withHeader(AUTHORIZATION, containing("Bearer"))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    // when
    var workflowResponse =
        buildWebClient()
            .get()
            .uri("/v1/workflows/lookup?associatedType=LEAD&q=name:WorkflowNo")
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/associatedWorkflowLookupResponse.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    JSONCompareMode.NON_EXTENSIBLE);
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-workflow-with-trigger-action.sql")
  public void givenWorkflow_withTriggerWorkflowActions_shouldGetIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    // when
    var workflowResponse =
        buildWebClient().get().uri("/v1/workflows/308").retrieve().bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/workflow-308-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.LENIENT,
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/insert-workflow-with-trigger-action.sql")
  public void givenWorkflowOnCall_withTriggerWorkflowActions_shouldUpdateIt() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/call/v1/call-logs/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/call/call-log-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-workflow-with-trigger-workflow-action-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/308")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-workflow-with-trigger-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowRequest_withConvertLeadAction_shouldUpdate() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("/contracts/config/response/get-all-lead-fields.json"))));


    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/lead-workflow-update-request-with-convert-lead-action.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/301")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/lead-update-workflow-with-convert-lead-action-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.LENIENT,
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenWorkflowRequest_withDateTimeRelativeValuesFilterInConditions_shouldCreateIt() throws IOException {
    // given
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));
    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/create-workflow-with-datetime-relative-values-condition-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class)
            .block();

    // then
    WorkflowSummary workflowSummary = objectMapper.readValue(workflowResponse, WorkflowSummary.class);
    Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
    Mono<String> workflowResponseMono = buildWebClient().get().uri("/v1/workflows/" + workflowSummary.getId()).retrieve().bodyToMono(String.class);
    StepVerifier.create(workflowResponseMono)
        .assertNext(
            json -> {
              try {
                var expectedResponse =
                    getResourceAsString("/contracts/workflow/api/workflow-with-datetime-relative-values-in-condition-response.json");
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("id", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true)));
              } catch (IOException | JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/workflow-with-condition-expression.sql")
  public void givenWorkflow_withDateTimeRelativeValuesInConditions_shouldUpdateIt() throws IOException {
    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/update-workflow-with-datetime-relative-values-in-condition-request.json");

    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/304")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);

    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/update-workflow-with-datetime-relative-values-in-condition-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[*].id", (o1, o2) -> true),
                        new Customization("actions[*].type", (o1, o2) -> true),
                        new Customization("actions[*].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)
                    )
                );
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  @Sql("/test-scripts/integration/insert-lead-workflow-for-integration-test.sql")
  public void givenLeadWorkflowUpdateRequest_withDelayedActionAndConditionsOnMetafields_shouldUpdateWorkflow() throws IOException {

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/integration/lead-workflow-update-request-with-metafields.json");
    // when
    var workflowResponse =
        buildWebClient()
            .put()
            .uri("/v1/workflows/303")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/integration/lead-workflow-update-response-with-metafields-on-condition.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                JSONAssert.assertEquals(
                    expectedResponse,
                    json,
                    new CustomComparator(
                        JSONCompareMode.STRICT,
                        new Customization("actions[0].id", (o1, o2) -> true),
                        new Customization("actions[0].type", (o1, o2) -> true),
                        new Customization("actions[0].payload", (o1, o2) -> true),
                        new Customization("lastTriggeredAt", (o1, o2) -> true),
                        new Customization("updatedAt", (o1, o2) -> true),
                        new Customization("createdAt", (o1, o2) -> true)));
              } catch (JSONException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @Test
  public void givenLeadWorkflowRequest_withSendWhatsappMessageAction_shouldCreate() throws IOException {
    // given

    stubFor(
        get("/sales/v1/leads/layout/list")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/lead/lead-list-layout-response.json"))));

    stubFor(
        get("/iam/v1/users/12")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader("Content-Type", "application/json")
                    .withStatus(200)
                    .withBody(
                        getResourceAsString("/contracts/user/responses/user-details-by-id.json"))));
    stubFor(
        get("/config/v1/entities/lead/fields")
            .withHeader(AUTHORIZATION, matching("Bearer " + authenticationToken))
            .willReturn(
                aResponse()
                    .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .withStatus(200)
                    .withBody(getResourceAsString("contracts/config/response/get-all-lead-fields.json"))));

    var workflowRequest =
        getResourceAsString("/contracts/workflow/api/whatsapp-message/create-workflow-send-whatsapp-message.json");
    // when
    var workflowResponse =
        buildWebClient()
            .post()
            .uri("/v1/workflows")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(workflowRequest)
            .retrieve()
            .bodyToMono(String.class);
    // then
    var expectedResponse =
        getResourceAsString("/contracts/workflow/api/create-workflow-response.json");
    StepVerifier.create(workflowResponse)
        .assertNext(
            json -> {
              try {
                WorkflowSummary workflowSummary = objectMapper.readValue(json, WorkflowSummary.class);
                Assertions.assertThat(workflowSummary.getId()).isGreaterThan(0);
              } catch (IOException e) {
                fail(e.getMessage());
              }
            })
        .verifyComplete();
  }

  @NotNull
  private WebClient buildWebClient() {
    var port = environment.getProperty("local.server.port");

    return WebClient.builder()
        .baseUrl("http://localhost:" + port)
        .defaultHeader(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE)
        .defaultHeader(AUTHORIZATION, "Bearer " + authenticationToken)
        .build();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    var resource = new ClassPathResource(resourcePath);
    var file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

}
