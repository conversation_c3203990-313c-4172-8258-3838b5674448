[{"entity": "CUSTOM", "entityDisplayName": "Custom Parameter", "fields": []}, {"entity": "TASK", "entityDisplayName": "Task", "fields": [{"name": "id", "displayName": "ID", "isStandard": true}, {"name": "name", "displayName": "Task Name", "isStandard": true}, {"name": "description", "displayName": "Description", "isStandard": true}, {"name": "type", "displayName": "Type", "isStandard": true}, {"name": "dueDate", "displayName": "Due Date", "isStandard": true}, {"name": "status", "displayName": "Status", "isStandard": true}, {"name": "priority", "displayName": "Priority", "isStandard": true}, {"name": "assignedTo", "displayName": "Assigned To", "isStandard": true}, {"name": "reminder", "displayName": "Reminder", "isStandard": true}, {"name": "relation", "displayName": "Relation", "isStandard": true}, {"name": "ownerId", "displayName": "Owner", "isStandard": true}, {"name": "createdAt", "displayName": "Created At", "isStandard": true}, {"name": "updatedAt", "displayName": "Updated At", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}, {"name": "completedAt", "displayName": "Completed At", "isStandard": true}, {"name": "cancelledAt", "displayName": "Cancelled At", "isStandard": true}, {"name": "originalDueDate", "displayName": "Original Due Date", "isStandard": true}, {"name": "createdViaId", "displayName": "Created Via Id", "isStandard": true}, {"name": "createdViaName", "displayName": "Created Via Name", "isStandard": true}, {"name": "createdViaType", "displayName": "Created Via Type", "isStandard": true}, {"name": "updatedViaId", "displayName": "Updated Via Id", "isStandard": true}, {"name": "updatedViaName", "displayName": "Updated Via Name", "isStandard": true}, {"name": "updatedViaType", "displayName": "Updated Via Type", "isStandard": true}, {"name": "cfTextCustomField", "displayName": "Text Custom Field", "isStandard": false}, {"name": "cfTextCustomField1", "displayName": "Text Custom Field 1", "isStandard": false}, {"name": "cfCustomNumberField", "displayName": "Custom Number Field", "isStandard": false}, {"name": "cfCustomPicklistField", "displayName": "Custom Picklist Field", "isStandard": false}, {"name": "cfCustomCheckboxField", "displayName": "Custom Checkbox Field", "isStandard": false}, {"name": "cfCustomDateField", "displayName": "Custom Date Field", "isStandard": false}, {"name": "cfCustomDatetimeField", "displayName": "Custom DateTime Field", "isStandard": false}, {"name": "cfCustomUrlField", "displayName": "Custom URL Field", "isStandard": false}, {"name": "cfCustomCheckboxField1", "displayName": "Custom Checkbox Field 1", "isStandard": false}]}, {"entity": "TASK_ASSIGNEE", "entityDisplayName": "Task Assignee", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "salutation", "displayName": "Salutation", "isStandard": true}, {"name": "firstName", "displayName": "First Name", "isStandard": true}, {"name": "lastName", "displayName": "Last Name", "isStandard": true}, {"name": "email", "displayName": "Email", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "designation", "displayName": "Designation", "isStandard": true}, {"name": "department", "displayName": "Department", "isStandard": true}, {"name": "active", "displayName": "Active", "isStandard": true}, {"name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "signature", "displayName": "Signature", "isStandard": true}, {"name": "language", "displayName": "Language", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}]}, {"entity": "CREATED_BY", "entityDisplayName": "Created By", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "salutation", "displayName": "Salutation", "isStandard": true}, {"name": "firstName", "displayName": "First Name", "isStandard": true}, {"name": "lastName", "displayName": "Last Name", "isStandard": true}, {"name": "email", "displayName": "Email", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "designation", "displayName": "Designation", "isStandard": true}, {"name": "department", "displayName": "Department", "isStandard": true}, {"name": "active", "displayName": "Active", "isStandard": true}, {"name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "signature", "displayName": "Signature", "isStandard": true}, {"name": "language", "displayName": "Language", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}]}, {"entity": "UPDATED_BY", "entityDisplayName": "Updated By", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "salutation", "displayName": "Salutation", "isStandard": true}, {"name": "firstName", "displayName": "First Name", "isStandard": true}, {"name": "lastName", "displayName": "Last Name", "isStandard": true}, {"name": "email", "displayName": "Email", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "designation", "displayName": "Designation", "isStandard": true}, {"name": "department", "displayName": "Department", "isStandard": true}, {"name": "active", "displayName": "Active", "isStandard": true}, {"name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "signature", "displayName": "Signature", "isStandard": true}, {"name": "language", "displayName": "Language", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}]}, {"entity": "TENANT", "entityDisplayName": "Tenant", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "accountName", "displayName": "Account Name", "isStandard": true}, {"name": "industry", "displayName": "Industry", "isStandard": true}, {"name": "address", "displayName": "Address", "isStandard": true}, {"name": "city", "displayName": "City", "isStandard": true}, {"name": "state", "displayName": "State", "isStandard": true}, {"name": "country", "displayName": "Country", "isStandard": true}, {"name": "zipcode", "displayName": "Zipcode", "isStandard": true}, {"name": "language", "displayName": "Language", "isStandard": true}, {"name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "companyName", "displayName": "Company Name", "isStandard": true}, {"name": "website", "displayName": "Website", "isStandard": true}]}, {"entity": "ASSOCIATED_LEAD", "entityDisplayName": "Associated Lead", "fields": [{"name": "id", "displayName": "ID", "isStandard": true}, {"name": "salutation", "displayName": "Salutation", "isStandard": true}, {"name": "firstName", "displayName": "First Name", "isStandard": true}, {"name": "lastName", "displayName": "Last Name", "isStandard": true}, {"name": "emails", "displayName": "Emails", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "pipeline", "displayName": "Pipeline", "isStandard": true}, {"name": "pipelineStage", "displayName": "Pipeline Stage", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "address", "displayName": "Address", "isStandard": true}, {"name": "city", "displayName": "City", "isStandard": true}, {"name": "state", "displayName": "State", "isStandard": true}, {"name": "country", "displayName": "Country", "isStandard": true}, {"name": "zipcode", "displayName": "Zipcode", "isStandard": true}, {"name": "facebook", "displayName": "Facebook", "isStandard": true}, {"name": "twitter", "displayName": "Twitter", "isStandard": true}, {"name": "linkedIn", "displayName": "Linked In", "isStandard": true}, {"name": "companyName", "displayName": "Company Name", "isStandard": true}, {"name": "department", "displayName": "Department", "isStandard": true}, {"name": "designation", "displayName": "Designation", "isStandard": true}, {"name": "companyIndustry", "displayName": "Company Industry", "isStandard": true}, {"name": "companyBusinessType", "displayName": "Business Type", "isStandard": true}, {"name": "companyEmployees", "displayName": "Company Employees", "isStandard": true}, {"name": "companyAnnualRevenue", "displayName": "Company Annual Revenue", "isStandard": true}, {"name": "companyWebsite", "displayName": "Company Website", "isStandard": true}, {"name": "companyPhones", "displayName": "Company Phones", "isStandard": true}, {"name": "requirementName", "displayName": "Requirement", "isStandard": true}, {"name": "products", "displayName": "Products or Services", "isStandard": true}, {"name": "requirementCurrency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "isStandard": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Budget", "isStandard": true}, {"name": "campaign", "displayName": "Campaign", "isStandard": true}, {"name": "source", "displayName": "Source", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "createdAt", "displayName": "Created At", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}, {"name": "updatedAt", "displayName": "Updated At", "isStandard": true}, {"name": "createdViaId", "displayName": "Created Via Id", "isStandard": true}, {"name": "createdViaName", "displayName": "Created Via Name", "isStandard": true}, {"name": "createdViaType", "displayName": "Created Via Type", "isStandard": true}, {"name": "updatedViaId", "displayName": "Updated Via Id", "isStandard": true}, {"name": "updatedViaName", "displayName": "Updated Via Name", "isStandard": true}, {"name": "updatedViaType", "displayName": "Updated Via Type", "isStandard": true}, {"name": "subSource", "displayName": "Sub Source", "isStandard": true}, {"name": "utmSource", "displayName": "UTM Source", "isStandard": true}, {"name": "utmCampaign", "displayName": "UTM Campaign", "isStandard": true}, {"name": "utmContent", "displayName": "UTM Content", "isStandard": true}, {"name": "utmMedium", "displayName": "UTM Medium", "isStandard": true}, {"name": "utmTerm", "displayName": "UTM Term", "isStandard": true}, {"name": "ownerId", "displayName": "Owner", "isStandard": true}, {"name": "companyAddress", "displayName": "Company Address", "isStandard": true}, {"name": "companyCity", "displayName": "Company City", "isStandard": true}, {"name": "companyState", "displayName": "Company State", "isStandard": true}, {"name": "companyZipcode", "displayName": "Company Zipcode", "isStandard": true}, {"name": "companyCountry", "displayName": "Company Country", "isStandard": true}, {"name": "actualClosureDate", "displayName": "Actual Closure Date", "isStandard": true}, {"name": "expectedClosureOn", "displayName": "Expected Closure On", "isStandard": true}, {"name": "convertedBy", "displayName": "Converted By", "isStandard": true}, {"name": "importedBy", "displayName": "Imported By", "isStandard": true}, {"name": "pipelineStageReason", "displayName": "Pipeline Stage Reason", "isStandard": true}, {"name": "score", "displayName": "Score", "isStandard": true}, {"name": "customdatetimepicker", "displayName": "CustomDateTimePicker", "isStandard": false}, {"name": "cfDateCustomField1", "displayName": "Date Custom Field 1", "isStandard": false}, {"name": "cfDateCustomField2", "displayName": "Date Custom Field 2", "isStandard": false}, {"name": "picklistc", "displayName": "PicklistC", "isStandard": false}, {"name": "datetimec", "displayName": "DateTimeC", "isStandard": false}, {"name": "text1", "displayName": "Text1", "isStandard": false}, {"name": "myTextField", "displayName": "MyTextField", "isStandard": false}, {"name": "number1", "displayName": "Number1", "isStandard": false}, {"name": "picklist1", "displayName": "CutomPickListOne", "isStandard": false}, {"name": "datetime1", "displayName": "Datetime1", "isStandard": false}, {"name": "url1", "displayName": "Url1", "isStandard": false}, {"name": "custompicklisttwo", "displayName": "CustomPickListTwo", "isStandard": false}, {"name": "cfMulti", "displayName": "Multi", "isStandard": false}, {"name": "cfNew<PERSON><PERSON><PERSON>", "displayName": "Multi", "isStandard": false}]}, {"entity": "ASSOCIATED_DEAL", "entityDisplayName": "Associated Deal", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "name", "displayName": "Name", "isStandard": true}, {"name": "estimatedValue", "displayName": "Estimated Value", "isStandard": true}, {"name": "actualValue", "displayName": "Actual Value", "isStandard": true}, {"name": "estimatedClosureOn", "displayName": "Estimated Closure Date", "isStandard": true}, {"name": "actualClosureDate", "displayName": "Actual Closure Date", "isStandard": true}, {"name": "associatedContacts", "displayName": "Contacts", "isStandard": true}, {"name": "pipeline", "displayName": "Pipeline", "isStandard": true}, {"name": "pipelineStage", "displayName": "Pipeline Stage", "isStandard": true}, {"name": "company", "displayName": "Company", "isStandard": true}, {"name": "ownedBy", "displayName": "Owner", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "createdAt", "displayName": "Created At", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}, {"name": "updatedAt", "displayName": "Updated At", "isStandard": true}, {"name": "campaign", "displayName": "Campaign", "isStandard": true}, {"name": "source", "displayName": "Source", "isStandard": true}, {"name": "products", "displayName": "Product or Service", "isStandard": true}, {"name": "createdViaId", "displayName": "Created Via Id", "isStandard": true}, {"name": "createdViaName", "displayName": "Created Via Name", "isStandard": true}, {"name": "createdViaType", "displayName": "Created Via Type", "isStandard": true}, {"name": "updatedViaId", "displayName": "Updated Via Id", "isStandard": true}, {"name": "updatedViaName", "displayName": "Updated Via Name", "isStandard": true}, {"name": "updatedViaType", "displayName": "Updated Via Type", "isStandard": true}, {"name": "subSource", "displayName": "Sub Source", "isStandard": true}, {"name": "utmSource", "displayName": "UTM Source", "isStandard": true}, {"name": "utmCampaign", "displayName": "UTM Campaign", "isStandard": true}, {"name": "utmContent", "displayName": "UTM Content", "isStandard": true}, {"name": "utmMedium", "displayName": "UTM Medium", "isStandard": true}, {"name": "utmTerm", "displayName": "UTM Term", "isStandard": true}, {"name": "forecastingType", "displayName": "ForecastingType", "isStandard": true}, {"name": "pipelineStageReason", "displayName": "Pipeline Stage Reason", "isStandard": true}, {"name": "taskDueOn", "displayName": "Task Due On", "isStandard": true}, {"name": "meetingScheduledOn", "displayName": "Meeting Scheduled On", "isStandard": true}, {"name": "latestActivityCreatedAt", "displayName": "Latest Activity On", "isStandard": true}, {"name": "isNew", "displayName": "Is New", "isStandard": true}, {"name": "score", "displayName": "Score", "isStandard": true}, {"name": "custompara", "displayName": "CustomPara", "isStandard": false}, {"name": "customtext", "displayName": "CustomText", "isStandard": false}, {"name": "customurl", "displayName": "CustomURL", "isStandard": false}, {"name": "textpicklistc", "displayName": "TextPicklistC", "isStandard": false}, {"name": "textpicklistc1", "displayName": "TextPickListC1", "isStandard": false}, {"name": "cfCustomdatetimepickerdeal", "displayName": "CustomDateTimePickerDeal", "isStandard": false}, {"name": "cfCustomc", "displayName": "CustomC", "isStandard": false}, {"name": "cfCustomtf1", "displayName": "CustomTF1", "isStandard": false}, {"name": "cfCustomtf2", "displayName": "CustomTF2", "isStandard": false}, {"name": "cfCustomtf3", "displayName": "CustomTF3", "isStandard": false}, {"name": "cfCustompf1", "displayName": "CustomPF1", "isStandard": false}, {"name": "cfCustompf2", "displayName": "CustomPF2", "isStandard": false}, {"name": "cfCustompf3", "displayName": "CustomPF3", "isStandard": false}, {"name": "cfDealmultipicklist", "displayName": "DealMultiPickList", "isStandard": false}, {"name": "cfDealcountrylist", "displayName": "DealCountryList", "isStandard": false}]}, {"entity": "ASSOCIATED_CONTACT", "entityDisplayName": "Associated Contact", "fields": [{"name": "id", "displayName": "Id", "isStandard": true}, {"name": "salutation", "displayName": "Salutation", "isStandard": true}, {"name": "firstName", "displayName": "First Name", "isStandard": true}, {"name": "lastName", "displayName": "Last Name", "isStandard": true}, {"name": "emails", "displayName": "Emails", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "address", "displayName": "Address", "isStandard": true}, {"name": "city", "displayName": "City", "isStandard": true}, {"name": "state", "displayName": "State", "isStandard": true}, {"name": "country", "displayName": "Country", "isStandard": true}, {"name": "zipcode", "displayName": "Zipcode", "isStandard": true}, {"name": "facebook", "displayName": "Facebook", "isStandard": true}, {"name": "twitter", "displayName": "Twitter", "isStandard": true}, {"name": "linkedin", "displayName": "Linkedin", "isStandard": true}, {"name": "company", "displayName": "Company", "isStandard": true}, {"name": "department", "displayName": "Department", "isStandard": true}, {"name": "designation", "displayName": "Designation", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "createdAt", "displayName": "Created At", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}, {"name": "updatedAt", "displayName": "Updated At", "isStandard": true}, {"name": "stakeholder", "displayName": "Decision maker", "isStandard": true}, {"name": "dnd", "displayName": "Do Not Disturb", "isStandard": true}, {"name": "createdViaId", "displayName": "Created Via Id", "isStandard": true}, {"name": "createdViaName", "displayName": "Created Via Name", "isStandard": true}, {"name": "createdViaType", "displayName": "Created Via Type", "isStandard": true}, {"name": "updatedViaId", "displayName": "Updated Via Id", "isStandard": true}, {"name": "updatedViaName", "displayName": "Updated Via Name", "isStandard": true}, {"name": "updatedViaType", "displayName": "Updated Via Type", "isStandard": true}, {"name": "campaign", "displayName": "Campaign", "isStandard": true}, {"name": "source", "displayName": "Source", "isStandard": true}, {"name": "subSource", "displayName": "Sub Source", "isStandard": true}, {"name": "utmSource", "displayName": "UTM Source", "isStandard": true}, {"name": "utmCampaign", "displayName": "UTM Campaign", "isStandard": true}, {"name": "utmContent", "displayName": "UTM Content", "isStandard": true}, {"name": "utmMedium", "displayName": "UTM Medium", "isStandard": true}, {"name": "utmTerm", "displayName": "UTM Term", "isStandard": true}, {"name": "ownerId", "displayName": "Owner", "isStandard": true}, {"name": "score", "displayName": "Score", "isStandard": true}, {"name": "cpicklist", "displayName": "CPickList", "isStandard": false}, {"name": "cfCustompick", "displayName": "CustomPick", "isStandard": false}, {"name": "cfCustompicklist", "displayName": "CustomPickList", "isStandard": false}, {"name": "cfMulti", "displayName": "My MultiPicklist", "isStandard": false}]}, {"entity": "ASSOCIATED_COMPANY", "entityDisplayName": "Associated Company", "fields": [{"name": "id", "displayName": "ID", "isStandard": true}, {"name": "name", "displayName": "Name", "isStandard": true}, {"name": "numberOfEmployees", "displayName": "Number of Employees", "isStandard": true}, {"name": "annualRevenue", "displayName": "Annual Revenue", "isStandard": true}, {"name": "website", "displayName": "Website", "isStandard": true}, {"name": "industry", "displayName": "Industry", "isStandard": true}, {"name": "businessType", "displayName": "Business Type", "isStandard": true}, {"name": "timezone", "displayName": "Timezone", "isStandard": true}, {"name": "dnd", "displayName": "Do Not Disturb", "isStandard": true}, {"name": "address", "displayName": "Address", "isStandard": true}, {"name": "city", "displayName": "City", "isStandard": true}, {"name": "country", "displayName": "Country", "isStandard": true}, {"name": "state", "displayName": "State", "isStandard": true}, {"name": "zipcode", "displayName": "Zipcode", "isStandard": true}, {"name": "facebook", "displayName": "Facebook", "isStandard": true}, {"name": "twitter", "displayName": "Twitter", "isStandard": true}, {"name": "linkedIn", "displayName": "LinkedIn", "isStandard": true}, {"name": "createdAt", "displayName": "Created At", "isStandard": true}, {"name": "updatedAt", "displayName": "Updated At", "isStandard": true}, {"name": "created<PERSON>y", "displayName": "Created By", "isStandard": true}, {"name": "updatedBy", "displayName": "Updated By", "isStandard": true}, {"name": "createdViaId", "displayName": "Created Via Id", "isStandard": true}, {"name": "createdViaName", "displayName": "Created Via Name", "isStandard": true}, {"name": "createdViaType", "displayName": "Created Via Type", "isStandard": true}, {"name": "updatedViaId", "displayName": "Updated Via Id", "isStandard": true}, {"name": "updatedViaName", "displayName": "Updated Via Name", "isStandard": true}, {"name": "updatedViaType", "displayName": "Updated Via Type", "isStandard": true}, {"name": "importedBy", "displayName": "Imported By", "isStandard": true}, {"name": "ownedBy", "displayName": "Owner", "isStandard": true}, {"name": "phoneNumbers", "displayName": "Phone Numbers", "isStandard": true}, {"name": "emails", "displayName": "Emails", "isStandard": true}]}, {"entity": "MARKETPLACE_TRIGGER", "entityDisplayName": "Marketplace Trigger", "fields": []}]