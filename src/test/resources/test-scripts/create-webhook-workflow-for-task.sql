DELETE FROM parameter;
DELETE FROM email_to;
DELETE FROM email_cc;
DELETE FROM email_bcc;
DELETE FROM email_action;
DELETE FROM email_recipient;
DELETE FROM marketplace_action_parameter;
DELETE FROM marketplace_action;
DELETE FROM webhook_action;
DELETE FROM reassign_action;
DELETE FROM create_task_action;
DELETE FROM edit_property_action;
DELETE FROM trigger_workflow_action;
DELETE FROM assign_to_action;
DELETE FROM workflow_executed_event;
DELETE FROM abstract_workflow_action;
DELETE FROM workflow_condition;
DELETE FROM workflow_trigger;
DELETE FROM execution_log;
DELETE FROM workflow;
DELETE FROM users;

INSERT INTO users(id, tenant_id, name)VALUES
(114, 190, 'Steve');

INSERT INTO workflow
(id, name, description, entity_type,
tenant_id, created_by, created_at, updated_by, updated_at )
OVERRIDING SYSTEM VALUE VALUES
(901, 'Workflow 1', 'Workflow 1', 'TASK', 190, 114, now(), 114, now()),
(902, 'Workflow 2', 'Workflow 2', 'TASK', 190, 114, now(), 114, now()),
(903, 'Workflow 3', 'Workflow 3', 'TASK', 190, 114, now(), 114, now());

INSERT INTO workflow_trigger (id, trigger_type, trigger_frequency, workflow_id)
OVERRIDING SYSTEM VALUE VALUES
(101, 'EVENT', 'CREATED', 901),
(102, 'EVENT', 'CREATED', 902),
(103, 'EVENT', 'CREATED', 903);

INSERT INTO workflow_condition (id, type,workflow_id)
OVERRIDING SYSTEM VALUE VALUES
(201, 'FOR_ALL', 901),
(202, 'FOR_ALL', 902),
(203, 'FOR_ALL', 903);

INSERT INTO abstract_workflow_action(id, workflow_id)
VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 901);

INSERT INTO webhook_action (id, workflow_id, name, description, method, authorization_type, request_url) VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',901, 'webhookName', 'webhook desc', 'POST', 'NONE', 'http://localhost:9090/3e0d9676-ad3c-4cf2-a449-ca334e43b815'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',902, 'webhook get', 'webhook desc', 'GET', 'NONE', 'http://localhost:9090/3e0d9676-ad3c-4cf2-a449-ca334e43b816'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14',903, 'webhook put', 'webhook desc', 'PUT', 'NONE', 'http://localhost:9090/3e0d9676-ad3c-4cf2-a449-ca334e43b817');

INSERT INTO parameter (id,name,entity, attribute, webhook_action_id,is_standard) OVERRIDING SYSTEM VALUE VALUES
(2000, 'param1','TASK', 'id', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2001, 'param2','TASK', 'name', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2002, 'param3','TASK', 'description', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2003, 'param4','TASK', 'type', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2004, 'param5','TASK', 'dueDate', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2005, 'param6','TASK', 'status', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2006, 'param7','TASK', 'priority', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2007, 'param8','TASK', 'assignedTo', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2009, 'param10','TASK', 'relation', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2010, 'param11','TASK', 'ownerId', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2011, 'param12','TASK', 'createdAt', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2012, 'param13','TASK', 'updatedAt', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2013, 'param14','TASK', 'createdBy', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2014, 'param15','TASK', 'updatedBy', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2015, 'param16','TASK_ASSIGNEE', 'firstName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2016, 'param17','TASK_ASSIGNEE', 'lastName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2017, 'param18','TASK_ASSIGNEE', 'phoneNumbers', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2018, 'param19','CREATED_BY', 'firstName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2019, 'param20','CREATED_BY', 'lastName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2020, 'param21','CREATED_BY', 'phoneNumbers', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2021, 'param22','TENANT', 'accountName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2022, 'param23','TENANT', 'country', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',true),
(2023, 'param24','CUSTOM', 'custom param value', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',false),
(2024, 'param1','TASK', 'id', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',true),
(2025, 'param2','TASK', 'name', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',true),
(2026, 'param3','TASK', 'relation', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',true),
(2027, 'param4','TASK', 'myPicklist2', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14',false),
(2028, 'param5','TASK', 'myPicklist3', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14',false),
(2029, 'param6','TASK', 'myPicklist4', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14',false);

INSERT INTO workflow_executed_event(id, workflow_id, last_triggered_at, trigger_count)
OVERRIDING SYSTEM VALUE VALUES
(401, 901, null, 0),
(402, 902, null, 0),
(403, 903, null, 0);