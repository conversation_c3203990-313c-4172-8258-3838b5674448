DELETE FROM parameter;
DELETE FROM email_to;
DELETE FROM email_cc;
DELETE FROM email_bcc;
DELETE FROM email_action;
DELETE FROM email_recipient;
DELETE FROM marketplace_action_parameter;
DELETE FROM marketplace_action;
DELETE FROM webhook_action;
DELETE FROM reassign_action;
DELETE FROM create_task_action;
DELETE FROM edit_property_action;
DELETE FROM trigger_workflow_action;
DELETE FROM assign_to_action;
DELETE FROM workflow_executed_event;
DELETE FROM workflow_condition;
DELETE FROM workflow_trigger;
DELETE FROM execution_log;
DELETE FROM workflow;
DELETE FROM users;

INSERT INTO users(id, tenant_id, name)VALUES
(114, 190, 'Steve');

INSERT INTO workflow
(id, name, description, entity_type,
tenant_id, created_by, created_at, updated_by, updated_at )
OVERRIDING SYSTEM VALUE VALUES
(904, 'Workflow 4', 'Workflow 4', 'TASK', 190, 114, now(), 114, now());

INSERT INTO workflow_trigger (id, trigger_type, trigger_frequency, workflow_id)
OVERRIDING SYSTEM VALUE VALUES
(104, 'EVENT', 'CREATED', 904);

INSERT INTO workflow_condition (id, type,workflow_id)
OVERRIDING SYSTEM VALUE VALUES
(204, 'FOR_ALL', 904);

INSERT INTO webhook_action (id, workflow_id, name, description, method, authorization_type, request_url) VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',904, 'webhook put', 'webhook desc', 'PUT', 'NONE', 'http://localhost:9090/3e0d9676-ad3c-4cf2-a449-ca334e43b845');

INSERT INTO parameter (id,name,entity, attribute, webhook_action_id,is_standard) OVERRIDING SYSTEM VALUE VALUES
(2030, 'customPicklist','ASSOCIATED_COMPANY', 'myPicklist3', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',false),
(2031, 'id','ASSOCIATED_COMPANY', 'id', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2032, 'name','ASSOCIATED_COMPANY', 'name', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2033, 'annualRevenue','ASSOCIATED_COMPANY', 'annualRevenue', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2034, 'country','ASSOCIATED_COMPANY', 'country', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2037, 'address','ASSOCIATED_COMPANY', 'address', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2038, 'facebook','ASSOCIATED_COMPANY', 'facebook', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2039, 'phoneNumbers','ASSOCIATED_COMPANY', 'phoneNumbers', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2040, 'dnd','ASSOCIATED_COMPANY', 'dnd', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2041, 'emails','ASSOCIATED_COMPANY', 'emails', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2042, 'website','ASSOCIATED_COMPANY', 'website', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2043, 'industry','ASSOCIATED_COMPANY', 'industry', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2044, 'businessType','ASSOCIATED_COMPANY', 'businessType', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2045, 'numberOfEmployees','ASSOCIATED_COMPANY', 'numberOfEmployees', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2047, 'timezone','ASSOCIATED_COMPANY', 'timezone', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2048, 'city','ASSOCIATED_COMPANY', 'city', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2049, 'state','ASSOCIATED_COMPANY', 'state', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2050, 'zipcode','ASSOCIATED_COMPANY', 'zipcode', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2051, 'createdAt','ASSOCIATED_COMPANY', 'createdAt', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2052, 'updatedAt','ASSOCIATED_COMPANY', 'updatedAt', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2053, 'createdBy','ASSOCIATED_COMPANY', 'createdBy', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2054, 'updatedBy','ASSOCIATED_COMPANY', 'updatedBy', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2055, 'importedBy','ASSOCIATED_COMPANY', 'importedBy', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2056, 'createdViaId','ASSOCIATED_COMPANY', 'createdViaId', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2057, 'createdViaName','ASSOCIATED_COMPANY', 'createdViaName', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true),
(2058, 'updatedViaType','ASSOCIATED_COMPANY', 'updatedViaType', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',true);

INSERT INTO workflow_executed_event(id, workflow_id, last_triggered_at, trigger_count)
OVERRIDING SYSTEM VALUE VALUES
(404,904, null, 0);