package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_COMPANY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import com.kylas.sales.workflow.common.dto.ActionDetail.WebhookAction.AuthorizationType;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.client.CompanyResponse;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpMethod;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
public class CompanyParameterBuilderTest {

  private static final String JWT_TOKEN = "jwt-token";

  @Mock
  private ConfigService configService;
  @Mock
  private UserService userService;
  @InjectMocks
  private CompanyParameterBuilder companyParameterBuilder;

  @Test
  public void givenCompanyWithCustomField_shouldBuildParameter() {
    // Given
    String fieldName = "customPicklist";
    String fieldValue = "Custom Value";

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, fieldValue);

    CompanyResponse companyResponse = new CompanyResponse();
    companyResponse.setCustomFieldValues(customFields);

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter("companyCustom", ASSOCIATED_COMPANY, fieldName, false));

    WebhookAction webhookAction = new WebhookAction(
        "Test Webhook",
        "Test Description",
        HttpMethod.POST,
        AuthorizationType.NONE,
        "http://test.com",
        parameters,
        null
    );

    // When
    Map<ParamKey, List<Object>> result = companyParameterBuilder.build(webhookAction, companyResponse, JWT_TOKEN);

    // Then
    assertThat(result).hasSize(1);
    assertThat(result.get(new ParamKey("companyCustom", fieldName))).containsExactly(fieldValue);
  }

  @Test
  public void givenCompanyWithMultiPicklistField_shouldBuildParameter() {
    // Given
    String fieldName = "multiPicklistField";
    List<Map<String, Object>> multiPicklistValues = List.of(
        Map.of("id", 1L, "name", "Value 1"),
        Map.of("id", 2L, "name", "Value 2")
    );

    Map<String, Object> customFields = new HashMap<>();
    customFields.put(fieldName, multiPicklistValues);
    CompanyResponse companyResponse = new CompanyResponse();
    companyResponse.setCustomFieldValues(customFields);

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter("companyMultiPicklist", ASSOCIATED_COMPANY, fieldName, false));

    WebhookAction webhookAction = new WebhookAction(
        "Test Webhook",
        "Test Description",
        HttpMethod.POST,
        AuthorizationType.NONE,
        "http://test.com",
        parameters,
        null
    );

    // When
    Map<ParamKey, List<Object>> result = companyParameterBuilder.build(webhookAction, companyResponse, JWT_TOKEN);

    // Then
    assertThat(result).hasSize(1);
    assertThat(result.get(new ParamKey("companyMultiPicklist", fieldName)))
        .containsExactly("Value 1", "Value 2");
  }

  @Test
  public void givenCompanyWithAnnualRevenue_shouldFormatMoneyValue() {
    // Given
    given(configService.getCurrency(anyList(), anyString()))
        .willReturn(Flux.just(new IdName(1L, "USD")));

    CompanyResponse companyResponse = new CompanyResponse();
    companyResponse.setAnnualRevenue(new com.kylas.sales.workflow.domain.processor.deal.Money(1L, 10000.0));

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter("revenue", ASSOCIATED_COMPANY, "annualRevenue", false));

    WebhookAction webhookAction = new WebhookAction(
        "Test Webhook",
        "Test Description",
        HttpMethod.POST,
        AuthorizationType.NONE,
        "http://test.com",
        parameters,
        null
    );

    // When
    Map<ParamKey, List<Object>> result = companyParameterBuilder.build(webhookAction, companyResponse, JWT_TOKEN);

    // Then
    assertThat(result).hasSize(1);
    assertThat((String) result.get(new ParamKey("revenue", "annualRevenue")).get(0))
        .contains("USD 10000.0");
  }

  @Test
  public void givenCompanyWithUserReferences_shouldResolveUserDetails() {
    // Given
    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter("ownedBy", ASSOCIATED_COMPANY, "ownedBy", true));
    parameters.add(new Parameter("createdBy", ASSOCIATED_COMPANY, "createdBy", true));
    parameters.add(new Parameter("updatedBy", ASSOCIATED_COMPANY, "updatedBy", true));

    WebhookAction webhookAction = new WebhookAction(
        "Test Webhook",
        "Test Description",
        HttpMethod.POST,
        AuthorizationType.NONE,
        "http://test.com",
        parameters,
        null
    );

    // When
    CompanyResponse companyResponse = new CompanyResponse();
    companyResponse.setOwnedBy(new IdName(12L, "John Doe"));
    companyResponse.setCreatedBy(new IdName(12L, "Jane Doe"));
    companyResponse.setUpdatedBy(new IdName(12L, "Jane Doe"));
    Map<ParamKey, List<Object>> result = companyParameterBuilder.build(webhookAction, companyResponse, JWT_TOKEN);

    // Then
    assertThat(result).hasSize(3);
    assertThat(result.get(new ParamKey("ownedBy", "ownedBy"))).isNotEmpty();
    assertThat(result.get(new ParamKey("createdBy", "createdBy"))).isNotEmpty();
    assertThat(result.get(new ParamKey("updatedBy", "updatedBy"))).isNotEmpty();
  }

}
