package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;


import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import com.kylas.sales.workflow.common.dto.ActionDetail.WebhookAction.AuthorizationType;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.processor.deal.DealDetail;
import com.kylas.sales.workflow.domain.service.client.CompanyResponse;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;

@ExtendWith(MockitoExtension.class)
public class AssociatedEntityParameterBuilderTest {

  String jwtString = "jwt-string";

  @InjectMocks
  private AssociatedEntityParameterBuilder associatedEntityParameterBuilder;

  @Mock
  private LeadParameterBuilder leadParameterBuilder;
  @Mock
  private DealParameterBuilder dealParameterBuilder;
  @Mock
  private ContactParameterBuilder contactParameterBuilder;
  @Mock
  private CompanyParameterBuilder companyParameterBuilder;

  @Test
  public void givenEmptyAssociatedEntitiesOnCallOrMeeting_shouldBuildAssociatedEntityParamWithNullValues() {
    //given
    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter("Deal_name", WebhookEntity.ASSOCIATED_DEAL, "name",true));
    parameters.add(new Parameter("Contact_Name", WebhookEntity.ASSOCIATED_CONTACT, "firstName",true));
    parameters.add(new Parameter("Lead_Name", WebhookEntity.ASSOCIATED_LEAD, "firstName",true));
    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    Map<ParamKey, List<Object>> build = associatedEntityParameterBuilder.getAssociatedEntity(Collections.emptyMap(),jwtString, webhookAction);
    //then
    assertThat(build.get(new ParamKey("associatedLeads", "associatedLeadsParam")).get(0)).isEqualTo(Arrays.asList());
    assertThat(build.get(new ParamKey("associatedDeals", "associatedDealsParam")).get(0)).isEqualTo(Arrays.asList());
    assertThat(build.get(new ParamKey("associatedContacts", "associatedContactsParam")).get(0)).isEqualTo(Arrays.asList());
  }

  @Test
  public void givenEmptyAssociatedLeadsOnCallOrMeeting_WithAssociatedLeadParameterOnWebhook_shouldBuildAssociatedLeadWithEmptyObject() {
    //given
    String webhookRequestParamName2 = "Name";

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter(webhookRequestParamName2, WebhookEntity.ASSOCIATED_LEAD, "firstName",true));

    DealDetail dealDetail = new DealDetail();
    dealDetail.setId(123L);
    dealDetail.setName("Deal 99");

    ContactDetail contactDetail = new ContactDetail();
    contactDetail.setId(124l);
    contactDetail.setFirstName("Contact_name");

    Map<EntityType, List<EntityDetail>> associatedEntities = new HashMap<>();
    associatedEntities.put(EntityType.DEAL, List.of(dealDetail));
    associatedEntities.put(EntityType.CONTACT, List.of(contactDetail));

    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    Map<ParamKey, List<Object>> build = associatedEntityParameterBuilder.getAssociatedEntity(associatedEntities, jwtString, webhookAction);
    //then
    assertThat(build.get(new ParamKey("associatedLeads", "associatedLeadsParam")).get(0)).isEqualTo(Arrays.asList());
  }

  @Test
  public void givenAssociatedDealWithEmptyResponseOnCallOrMeeting_andWithAssociatedDealParameterOnWebhook_shouldBuildAssociatedDealWithEmptyObject() {
    //given
    String param2 = "Deal Name";

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter(param2, WebhookEntity.ASSOCIATED_DEAL, "name", true));
    List<EntityDetail> entityDetails = new ArrayList<>();
    var associatedEntities = Map.of(EntityType.DEAL, entityDetails);
    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    Map<ParamKey, List<Object>> build = associatedEntityParameterBuilder.getAssociatedEntity(associatedEntities, jwtString, webhookAction);
    //then
    assertThat(build.get(new ParamKey("associatedDeals", "associatedDealsParam")).get(0)).isEqualTo(Arrays.asList());
  }

  @Test
  public void givenAssociatedDealOnCallOrMeeting_WithoutAssociatedDealParameterOnWebhook_shouldNotBuildAssociatedDealObject() {
    //given
    String param2 = "Name";

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter(param2, WebhookEntity.ASSOCIATED_LEAD, "firstName", true));
    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    Map<ParamKey, List<Object>> build = associatedEntityParameterBuilder.getAssociatedEntity(Collections.emptyMap(), jwtString, webhookAction);
    //then
    assertThat(build.get(new ParamKey("associatedDeals", "associatedDealsParam"))).isNull();
  }

  @Test
  public void givenAssociatedDealOnCallOrMeeting_withAssociatedDealParameterOnWebhook_shouldCallDealParameterBuilder() {
    //given
    String webhookRequestParamName2 = "Name";

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter(webhookRequestParamName2, WebhookEntity.ASSOCIATED_DEAL, "name",true));

    DealDetail dealDetail = new DealDetail();
    dealDetail.setId(123L);
    dealDetail.setName("Deal 99");

    ContactDetail contactDetail = new ContactDetail();
    contactDetail.setId(124l);
    contactDetail.setFirstName("Contact_name");

    Map<EntityType, List<EntityDetail>> associatedEntities = new HashMap<>();
    associatedEntities.put(EntityType.DEAL, List.of(dealDetail));

    given(dealParameterBuilder.canBuild(any())).willReturn(true);

    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    associatedEntityParameterBuilder.getAssociatedEntity(associatedEntities, jwtString, webhookAction);
    //then
    verify(dealParameterBuilder, times(1)).build(any(WebhookAction.class), any(DealDetail.class), any());
  }

  @Test
  public void givenAssociatedDealOnCallOrMeeting_withoutAssociatedDealParameterOnWebhook_verifyNoInteractionOnDealParameterBuilder() {
    //given
    String webhookRequestParamName2 = "Name";

    List<Parameter> parameters = new ArrayList<>();
    parameters.add(new Parameter(webhookRequestParamName2, WebhookEntity.ASSOCIATED_LEAD, "firstName",true));

    DealDetail dealDetail = new DealDetail();
    dealDetail.setId(123L);
    dealDetail.setName("Deal 99");

    Map<EntityType, List<EntityDetail>> associatedEntities = new HashMap<>();
    associatedEntities.put(EntityType.DEAL, List.of(dealDetail));


    WebhookAction webhookAction = new WebhookAction("Webhook", "Webhook", HttpMethod.POST, AuthorizationType.NONE, "http://localhost:9090",
        parameters, null);
    //when
    associatedEntityParameterBuilder.getAssociatedEntity(associatedEntities, jwtString, webhookAction);
    //then
    verifyNoInteractions(dealParameterBuilder);
  }

  @Test
  public void givenCompanyAssociatedEntities_shouldBuildAssociatedEntities() {
    // Given
    // Setup parameters for different entity types
    Parameter companyNameParam = new Parameter("Company Name", WebhookEntity.ASSOCIATED_COMPANY, "name", true);

    List<Parameter> parameters = Arrays.asList(companyNameParam);

    // Setup test data
    CompanyResponse companyDetail = new CompanyResponse();
    companyDetail.setId(126L);
    companyDetail.setName("Test Company");

    Map<EntityType, List<EntityDetail>> associatedEntities = new HashMap<>();
    associatedEntities.put(EntityType.COMPANY, List.of(companyDetail));

    // Mock parameter builders
    given(companyParameterBuilder.canBuild(any())).willReturn(true);

    Map<ParamKey, List<Object>> mockCompanyParams = new HashMap<>();
    ParamKey paramKey = new ParamKey("name", "name");
    mockCompanyParams.put(paramKey, List.of("Test Company"));
    given(companyParameterBuilder.build(any(WebhookAction.class), any(CompanyResponse.class), any()))
        .willReturn(mockCompanyParams);

    WebhookAction webhookAction = new WebhookAction(
        "Test Webhook",
        "Test Webhook",
        HttpMethod.POST,
        AuthorizationType.NONE,
        "http://test.com",
        parameters,
        null
    );

    // When
    Map<ParamKey, List<Object>> result = associatedEntityParameterBuilder.getAssociatedEntity(
        associatedEntities, jwtString, webhookAction);

    // Then
    // Verify all parameter builders were called
   verify(companyParameterBuilder, times(1)).build(any(WebhookAction.class), any(CompanyResponse.class), any());

    // Verify the result map contains all expected entries
    assertThat(result).hasSize(1);

    List<Object> companies = result.get(new ParamKey("associatedCompanies", "associatedCompaniesParam"));
    assertThat(companies).isNotNull();
    assertThat(companies.get(0).toString()).isEqualTo("[{name=Test Company}]");
  }
}