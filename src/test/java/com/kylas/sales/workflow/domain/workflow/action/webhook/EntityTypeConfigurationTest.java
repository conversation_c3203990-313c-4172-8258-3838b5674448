package com.kylas.sales.workflow.domain.workflow.action.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.MarketPlaceTriggerDetailRepository;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTrigger;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerDetail;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerVariable;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceServiceImpl;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.Attribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory;
import com.kylas.sales.workflow.security.AuthService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class EntityTypeConfigurationTest {

  @InjectMocks
  private EntityTypeConfiguration entityTypeConfiguration;
  @Mock
  private AttributeFactory attributeFactory;
  @Mock
  private MarketPlaceTriggerDetailRepository marketPlaceTriggerDetailRepository;
  @Mock
  private MarketplaceServiceImpl marketplaceService;
  @Mock
  private AuthService authService;
  @Mock
  private UserService userService;
  @Mock
  private ExchangeFunction exchangeFunction;
  @Mock
  private CryptoService cryptoService;
  @Mock
  private ObjectMapper objectMapper;

  @BeforeEach
  void init() {
    entityTypeConfiguration = new EntityTypeConfiguration(attributeFactory,marketPlaceTriggerDetailRepository,marketplaceService,authService);
  }

  @Test
  public void webhookConfigLead_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name",true), new Attribute("lastName", "Last Name",true));
    var standardLeadAttributes = List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true));
    var allLeadAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var leadAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));

    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"LEAD")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getAllEntityAttributes("lead")).willReturn(Mono.just(allLeadAttributes));
    given(attributeFactory.getStandardLeadAttributes()).willReturn(Mono.just(standardLeadAttributes));
    given(attributeFactory.getLeadAttributes(allLeadAttributes,standardLeadAttributes)).willReturn(leadAttributes);
    given(attributeFactory.getEntitiesLead()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.LEAD, Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(7);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Lead", "Lead Owner", "Created By", "Updated By", "Tenant", "Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "LEAD", "LEAD_OWNER", "CREATED_BY", "UPDATED_BY", "TENANT","MARKETPLACE_TRIGGER");

    var leadConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Lead")).findFirst();
    assertThat(leadConfig).isPresent();
    assertThat(leadConfig.get().getFields()).isNotEmpty();
    assertThat(leadConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "pipeline", "landmark","road");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var userConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Lead Owner")).findFirst();
    assertThat(userConfig).isPresent();
    assertThat(userConfig.get().getFields()).isNotEmpty();
    assertThat(userConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("firstName", "lastName");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id","accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");
  }

  @Test
  public void webhookConfigCallLog_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name",true), new Attribute("lastName", "Last Name",true));
    var standardCallLogAttributes = List.of(new Attribute("outcome", "Outcome",true), new Attribute("callType", "CallType",true));
    var allCallLogAttributes=List.of(new FieldAttribute("custom1", "Custom1",false,"TEXT_FIELD"), new FieldAttribute("custom2", "Custom2",false,"TEXT_FIELD"));
    var callLogAttributes=List.of(new Attribute("deviceId", "Device Id",true), new Attribute("outcome", "Outcome",true), new Attribute("callType", "CallType",true),new Attribute("custom1", "Custom1",false), new Attribute("custom2", "Custom2",false));
    var standardLeadAttributes = List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true));
    var allLeadAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var leadAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allDealFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var standardContactAttributes = List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true));
    var allContactAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"));
    var contactAttributes=List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true),new Attribute("landmark", "Landmark",false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"CALL_LOG")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getAllEntityAttributes("callLog")).willReturn(Mono.just(allCallLogAttributes));
    given(attributeFactory.getStandardCallLogAttributes()).willReturn(Mono.just(standardCallLogAttributes));
    given(attributeFactory.getCallLogAttributes(allCallLogAttributes,standardCallLogAttributes)).willReturn(callLogAttributes);
    given(attributeFactory.getAllEntityAttributes("lead")).willReturn(Mono.just(allLeadAttributes));
    given(attributeFactory.getStandardLeadAttributes()).willReturn(Mono.just(standardLeadAttributes));
    given(attributeFactory.getLeadAttributes(allLeadAttributes, standardLeadAttributes)).willReturn(leadAttributes);
    given(attributeFactory.getStandardDealAttributes()).willReturn(Mono.just(standardDealAttributes));
    given(attributeFactory.getAllEntityAttributes("deal")).willReturn(Mono.just(allDealFieldAttributes));
    given(attributeFactory.getDealAttributes(allDealFieldAttributes, standardDealAttributes)).willReturn(allDealAttributes);
    given(attributeFactory.getStandardContactAttributes()).willReturn(Mono.just(standardContactAttributes));
    given(attributeFactory.getAllEntityAttributes("contact")).willReturn(Mono.just(allContactAttributes));
    given(attributeFactory.getContactAttributes(allContactAttributes, standardContactAttributes)).willReturn(contactAttributes);
    given(attributeFactory.getEntitiesCallLog()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.CALL_LOG,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(10);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter","Call Log","Logged By","Created By","Updated By","Tenant", "Associated Lead", "Associated Deal", "Associated Contact","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM","CALL_LOG","LOGGED_BY","CREATED_BY","UPDATED_BY","TENANT","ASSOCIATED_LEAD", "ASSOCIATED_DEAL", "ASSOCIATED_CONTACT","MARKETPLACE_TRIGGER");

    var callConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Call Log")).findFirst();
    assertThat(callConfig).isPresent();
    assertThat(callConfig.get().getFields()).isNotEmpty();
    assertThat(callConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("deviceId", "outcome", "callType", "custom1","custom2");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var userConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Logged By")).findFirst();
    assertThat(userConfig).isPresent();
    assertThat(userConfig.get().getFields()).isNotEmpty();
    assertThat(userConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("firstName", "lastName");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id","accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");

    var leadConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Lead")).findFirst();
    assertThat(leadConfig).isPresent();
    assertThat(leadConfig.get().getFields()).isNotEmpty();
    assertThat(leadConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "pipeline", "landmark","road");

    var dealConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Deal")).findFirst();
    assertThat(dealConfig).isPresent();
    assertThat(dealConfig.get().getFields()).isNotEmpty();
    assertThat(dealConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

    var contactConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Contact")).findFirst();
    assertThat(contactConfig).isPresent();
    assertThat(contactConfig.get().getFields()).isNotEmpty();
    assertThat(contactConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "firstName", "landmark");
  }

  @Test
  public void webhookConfigContact_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name", true), new Attribute("lastName", "Last Name", true));
    var standardContactAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allContactAttributes = List.of(new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var contactAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"CONTACT")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getStandardContactAttributes()).willReturn(Mono.just(standardContactAttributes));
    given(attributeFactory.getAllEntityAttributes("contact")).willReturn(Mono.just(allContactAttributes));
    given(attributeFactory.getContactAttributes(allContactAttributes, standardContactAttributes)).willReturn(contactAttributes);
    given(attributeFactory.getEntitiesContact()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.CONTACT,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(7);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Contact", "Contact Owner", "Created By", "Updated By", "Tenant","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "CONTACT", "CONTACT_OWNER", "CREATED_BY", "UPDATED_BY", "TENANT","MARKETPLACE_TRIGGER");

    var contactConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Contact")).findFirst();
    assertThat(contactConfig).isPresent();
    assertThat(contactConfig.get().getFields()).isNotEmpty();
    assertThat(contactConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address","landmark","road");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id","accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");
  }

  @Test
  public void webhookConfigDeal_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name", true), new Attribute("lastName", "Last Name", true));
    var standardDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allDealFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"DEAL")).willReturn(marketPlaceTriggerDetail);
    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getStandardDealAttributes()).willReturn(Mono.just(standardDealAttributes));
    given(attributeFactory.getAllEntityAttributes("deal")).willReturn(Mono.just(allDealFieldAttributes));
    given(attributeFactory.getDealAttributes(allDealFieldAttributes, standardDealAttributes)).willReturn(allDealAttributes);
    given(attributeFactory.getEntitiesDeal()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.DEAL,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(7);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Deal", "Deal Owner", "Created By", "Updated By", "Tenant","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "DEAL", "DEAL_OWNER", "CREATED_BY", "UPDATED_BY", "TENANT","MARKETPLACE_TRIGGER");

    var dealConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Deal")).findFirst();
    assertThat(dealConfig).isPresent();
    assertThat(dealConfig.get().getFields()).isNotEmpty();
    assertThat(dealConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");
  }

  @Test
  public void webhookConfigTask_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name", true), new Attribute("lastName", "Last Name", true));
    var standardTaskAttributes = List.of(new Attribute("id", "Id", true), new Attribute("name", "Task Name", true), new Attribute("description", "Description", true));
    var allTaskAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var taskAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardLeadAttributes = List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true));
    var allLeadAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var leadAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allDealFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var standardContactAttributes = List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true));
    var allContactAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"));
    var contactAttributes=List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true),new Attribute("landmark", "Landmark",false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    var standardCompanyAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allCompanyFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allCompanyAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));

    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"TASK")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getAllEntityAttributes("task")).willReturn(Mono.just(allTaskAttributes));
    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getTaskAttributes(allTaskAttributes,standardTaskAttributes)).willReturn(taskAttributes);
    given(attributeFactory.getStandardTaskAttributes()).willReturn(Mono.just(standardTaskAttributes));
    given(attributeFactory.getAllEntityAttributes("lead")).willReturn(Mono.just(allLeadAttributes));
    given(attributeFactory.getStandardLeadAttributes()).willReturn(Mono.just(standardLeadAttributes));
    given(attributeFactory.getLeadAttributes(allLeadAttributes, standardLeadAttributes)).willReturn(leadAttributes);
    given(attributeFactory.getStandardDealAttributes()).willReturn(Mono.just(standardDealAttributes));
    given(attributeFactory.getAllEntityAttributes("deal")).willReturn(Mono.just(allDealFieldAttributes));
    given(attributeFactory.getDealAttributes(allDealFieldAttributes, standardDealAttributes)).willReturn(allDealAttributes);
    given(attributeFactory.getStandardContactAttributes()).willReturn(Mono.just(standardContactAttributes));
    given(attributeFactory.getAllEntityAttributes("contact")).willReturn(Mono.just(allContactAttributes));
    given(attributeFactory.getContactAttributes(allContactAttributes, standardContactAttributes)).willReturn(contactAttributes);
    given(attributeFactory.getStandardCompanyAttributes()).willReturn(Mono.just(standardCompanyAttributes));
    given(attributeFactory.getAllEntityAttributes("company")).willReturn(Mono.just(allCompanyFieldAttributes));
    given(attributeFactory.getCompanyAttributes(allCompanyFieldAttributes, standardCompanyAttributes)).willReturn(allCompanyAttributes);
    given(attributeFactory.getEntitiesTask()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.TASK,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(11);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Task", "Task Assignee", "Created By", "Updated By", "Tenant",  "Associated Lead", "Associated Deal", "Associated Contact","Associated Company","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "TASK", "TASK_ASSIGNEE", "CREATED_BY", "UPDATED_BY", "TENANT",  "ASSOCIATED_LEAD", "ASSOCIATED_DEAL", "ASSOCIATED_CONTACT","ASSOCIATED_COMPANY","MARKETPLACE_TRIGGER");

    var taskConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Task")).findFirst();
    assertThat(taskConfig).isPresent();
    assertThat(taskConfig.get().getFields()).isNotEmpty();
    assertThat(taskConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id","pipeline", "landmark", "road");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var createdByConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Created By")).findFirst();
    assertThat(createdByConfig).isPresent();
    assertThat(createdByConfig.get().getFields()).isNotEmpty();
    assertThat(createdByConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("firstName", "lastName");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");

    var leadConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Lead")).findFirst();
    assertThat(leadConfig).isPresent();
    assertThat(leadConfig.get().getFields()).isNotEmpty();
    assertThat(leadConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "pipeline", "landmark","road");

    var dealConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Deal")).findFirst();
    assertThat(dealConfig).isPresent();
    assertThat(dealConfig.get().getFields()).isNotEmpty();
    assertThat(dealConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

    var contactConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Contact")).findFirst();
    assertThat(contactConfig).isPresent();
    assertThat(contactConfig.get().getFields()).isNotEmpty();
    assertThat(contactConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "firstName", "landmark");

    var companyConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Company")).findFirst();
    assertThat(companyConfig).isPresent();
    assertThat(companyConfig.get().getFields()).isNotEmpty();
    assertThat(companyConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

  }

  @Test
  public void webhookConfigMeeting_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name", true), new Attribute("lastName", "Last Name", true));
    var standardMeetingAttributes = List.of(new Attribute("id", "Id", true), new Attribute("title", "Title", true), new Attribute("description", "Description", true));
    var allMeetingAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var meetingAttributes=List.of(new Attribute("id", "Id",true), new Attribute("medium", "Medium",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardLeadAttributes = List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true));
    var allLeadAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var leadAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allDealFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var standardContactAttributes = List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true));
    var allContactAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"));
    var contactAttributes=List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true),new Attribute("landmark", "Landmark",false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"MEETING")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getAllEntityAttributes("meeting")).willReturn(Mono.just(allMeetingAttributes));
    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getMeetingAttributes(allMeetingAttributes,standardMeetingAttributes)).willReturn(meetingAttributes);
    given(attributeFactory.getStandardMeetingAttributes()).willReturn(Mono.just(standardMeetingAttributes));
    given(attributeFactory.getAllEntityAttributes("lead")).willReturn(Mono.just(allLeadAttributes));
    given(attributeFactory.getStandardLeadAttributes()).willReturn(Mono.just(standardLeadAttributes));
    given(attributeFactory.getLeadAttributes(allLeadAttributes, standardLeadAttributes)).willReturn(leadAttributes);
    given(attributeFactory.getStandardDealAttributes()).willReturn(Mono.just(standardDealAttributes));
    given(attributeFactory.getAllEntityAttributes("deal")).willReturn(Mono.just(allDealFieldAttributes));
    given(attributeFactory.getDealAttributes(allDealFieldAttributes, standardDealAttributes)).willReturn(allDealAttributes);
    given(attributeFactory.getStandardContactAttributes()).willReturn(Mono.just(standardContactAttributes));
    given(attributeFactory.getAllEntityAttributes("contact")).willReturn(Mono.just(allContactAttributes));
    given(attributeFactory.getContactAttributes(allContactAttributes, standardContactAttributes)).willReturn(contactAttributes);
    given(attributeFactory.getEntitiesMeeting()).willCallRealMethod();
    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.MEETING,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(11);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Meeting", "Meeting Owner", "Organizer", "Created By", "Updated By", "Tenant", "Associated Lead",
            "Associated Deal", "Associated Contact","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "MEETING", "MEETING_OWNER", "ORGANIZER", "CREATED_BY", "UPDATED_BY", "TENANT", "ASSOCIATED_LEAD",
            "ASSOCIATED_DEAL", "ASSOCIATED_CONTACT","MARKETPLACE_TRIGGER");

    var meetingConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Meeting")).findFirst();
    assertThat(meetingConfig).isPresent();
    assertThat(meetingConfig.get().getFields()).isNotEmpty();
    assertThat(meetingConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id","medium", "landmark", "road");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");

    var createdByConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Created By")).findFirst();
    assertThat(createdByConfig).isPresent();
    assertThat(createdByConfig.get().getFields()).isNotEmpty();
    assertThat(createdByConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("firstName", "lastName");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");

    var leadConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Lead")).findFirst();
    assertThat(leadConfig).isPresent();
    assertThat(leadConfig.get().getFields()).isNotEmpty();
    assertThat(leadConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "pipeline", "landmark","road");

    var dealConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Deal")).findFirst();
    assertThat(dealConfig).isPresent();
    assertThat(dealConfig.get().getFields()).isNotEmpty();
    assertThat(dealConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

    var contactConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Contact")).findFirst();
    assertThat(contactConfig).isPresent();
    assertThat(contactConfig.get().getFields()).isNotEmpty();
    assertThat(contactConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "firstName", "landmark");
  }

  @Test
  public void webhookConfigEmail_shouldReturnConfiguration() {
    //given
    var userAttributes = List.of(new Attribute("firstName", "First Name", true), new Attribute("lastName", "Last Name", true));
    var standardEmailAttributes = List.of(new Attribute("id", "Id", true), new Attribute("subject", "Subject", true));
    var standardLeadAttributes = List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true));
    var allLeadAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"), new FieldAttribute("road", "Road",false,"customField"));
    var leadAttributes=List.of(new Attribute("id", "Id",true), new Attribute("pipeline", "Pipeline",true),new Attribute("landmark", "Landmark",false), new Attribute("road", "Road",false));
    var standardDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true));
    var allDealFieldAttributes = List.of(new FieldAttribute("id", "Id", true,"TEXT_FIELD"), new FieldAttribute("address", "Address", true,"TEXT_FIELD"),
        new FieldAttribute("landmark", "Landmark", false,"TEXT_FIELD"), new FieldAttribute("road", "Road", false,"TEXT_FIELD"));
    var allDealAttributes = List.of(new Attribute("id", "Id", true), new Attribute("address", "Address", true),
        new Attribute("landmark", "Landmark", false), new Attribute("road", "Road", false));
    var standardContactAttributes = List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true));
    var allContactAttributes=List.of(new FieldAttribute("landmark", "Landmark",false,"customField"));
    var contactAttributes=List.of(new Attribute("id", "Id",true), new Attribute("firstName", "firstName",true),new Attribute("landmark", "Landmark",false));
    var marketplaceAttributes=List.of(new Attribute("mpMyName", "My Name",false), new Attribute("mpMyAddress", "My Address",false));
    UUID uuid = UUID.fromString("1135589a-746c-4daf-a710-3bdb1be7c2d8");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail();
    List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables = List.of(new MarketPlaceTriggerVariable("My Name", "TEXT_FIELD", "mpMyName"),
        new MarketPlaceTriggerVariable("My Address", "TEXT_FIELD", "mpMyAddress"));
    marketPlaceTriggerDetail.setTriggerVariables(marketPlaceTriggerVariables);
    given(attributeFactory.getMarketPlaceAttributes(any())).willReturn(marketplaceAttributes);
    given(marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,"EMAIL")).willReturn(marketPlaceTriggerDetail);

    given(attributeFactory.getUserAttributes()).willReturn(Mono.just(userAttributes));
    given(attributeFactory.getStandardEmailAttributes()).willReturn(standardEmailAttributes);
    given(attributeFactory.getAllEntityAttributes("lead")).willReturn(Mono.just(allLeadAttributes));
    given(attributeFactory.getStandardLeadAttributes()).willReturn(Mono.just(standardLeadAttributes));
    given(attributeFactory.getLeadAttributes(allLeadAttributes, standardLeadAttributes)).willReturn(leadAttributes);
    given(attributeFactory.getStandardDealAttributes()).willReturn(Mono.just(standardDealAttributes));
    given(attributeFactory.getAllEntityAttributes("deal")).willReturn(Mono.just(allDealFieldAttributes));
    given(attributeFactory.getDealAttributes(allDealFieldAttributes, standardDealAttributes)).willReturn(allDealAttributes);
    given(attributeFactory.getStandardContactAttributes()).willReturn(Mono.just(standardContactAttributes));
    given(attributeFactory.getAllEntityAttributes("contact")).willReturn(Mono.just(allContactAttributes));
    given(attributeFactory.getContactAttributes(allContactAttributes, standardContactAttributes)).willReturn(contactAttributes);
    given(attributeFactory.getEntitiesEmail()).willCallRealMethod();

    //when
    List<EntityConfig> configurations = entityTypeConfiguration.getConfigurations(EntityType.EMAIL,Optional.of(uuid.toString())).collectList().block();

    //then
    assertThat(configurations).isNotNull().hasSize(7);
    assertThat(configurations.stream().map(EntityConfig::getEntityDisplayName))
        .containsExactly("Custom Parameter", "Email", "Tenant", "Associated Lead", "Associated Deal", "Associated Contact","Marketplace Trigger");

    assertThat(configurations.stream().map(EntityConfig::getEntity))
        .containsExactly("CUSTOM", "EMAIL", "TENANT", "ASSOCIATED_LEAD", "ASSOCIATED_DEAL", "ASSOCIATED_CONTACT","MARKETPLACE_TRIGGER");

    var emailConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Email")).findFirst();
    assertThat(emailConfig).isPresent();
    assertThat(emailConfig.get().getFields()).isNotEmpty();
    assertThat(emailConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "subject");

    var tenantConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Tenant")).findFirst();
    assertThat(tenantConfig).isPresent();
    assertThat(tenantConfig.get().getFields()).isNotEmpty();
    assertThat(tenantConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "accountName", "industry", "address", "city", "state", "country", "zipcode",
            "language", "currency", "timezone", "companyName", "website");

    var marketplaceConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Marketplace Trigger")).findFirst();
    assertThat(marketplaceConfig).isPresent();
    assertThat(marketplaceConfig.get().getFields()).isNotEmpty();
    assertThat(marketplaceConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("mpMyName","mpMyAddress");


    var leadConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Lead")).findFirst();
    assertThat(leadConfig).isPresent();
    assertThat(leadConfig.get().getFields()).isNotEmpty();
    assertThat(leadConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "pipeline", "landmark","road");

    var dealConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Deal")).findFirst();
    assertThat(dealConfig).isPresent();
    assertThat(dealConfig.get().getFields()).isNotEmpty();
    assertThat(dealConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "address", "landmark", "road");

    var contactConfig = configurations.stream()
        .filter(config -> config.getEntityDisplayName().equals("Associated Contact")).findFirst();
    assertThat(contactConfig).isPresent();
    assertThat(contactConfig.get().getFields()).isNotEmpty();
    assertThat(contactConfig.get().getFields().stream().map(Attribute::getName))
        .containsExactly("id", "firstName", "landmark");
  }
}