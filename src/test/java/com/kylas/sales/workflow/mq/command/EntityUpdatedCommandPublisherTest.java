package com.kylas.sales.workflow.mq.command;

import static com.kylas.sales.workflow.domain.workflow.EntityType.CALL_LOG;
import static com.kylas.sales.workflow.mq.RabbitMqConfig.WORKFLOW_EXCHANGE;

import com.kylas.sales.workflow.config.TestDatabaseInitializer;
import com.kylas.sales.workflow.config.TestMqSetup;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail.CallType;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail.OutcomeType;
import com.kylas.sales.workflow.mq.event.EntityAction;
import com.kylas.sales.workflow.mq.event.Metadata;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ContextConfiguration;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestMqSetup.class, TestDatabaseInitializer.class})
class EntityUpdatedCommandPublisherTest {
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private EntityUpdatedCommandPublisher entityUpdatedCommandPublisher;
  private static final String QUEUE = "q.enqueue.scheduled.jobs.workflow";

  private final MockMqListener mockMqListener = new MockMqListener();

  @Test
  public void givenEntityUpdatedCommand_withCallLogUpdateAndShouldExpectTrue_shouldPublishAndSendMessageInHeader()
      throws InterruptedException, IOException, JSONException {
    // given
    var container = initializeRabbitMqListener(QUEUE, WORKFLOW_EXCHANGE, "workflow.callLog.update");

    // when
    Metadata metadata = new Metadata(99L, 500L, CALL_LOG, null, null, EntityAction.CREATED);
    CallLogDetail callLogDetail = new CallLogDetail();
    callLogDetail.setCallType(CallType.OUTGOING);
    callLogDetail.setOutcome(OutcomeType.IN_PROGRESS);
    callLogDetail.setDuration(123123);
    entityUpdatedCommandPublisher.execute(metadata, callLogDetail, true);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        "{\"entity\":{\"outcome\":\"in_progress\",\"callType\":\"outgoing\",\"duration\":123123},\"metadata\":{\"eventId\":null,\"tenantId\":99,\"userId\":500,\"entityType\":\"CALL_LOG\",\"workflowId\":null,\"executedWorkflows\":[],\"entityAction\":\"CREATED\",\"executeWorkflow\":true,\"entityId\":0,\"workflowName\":null}}",
        new String(mockMqListener.actualMessage.getBody()), true);

    Assertions.assertThat(Optional.ofNullable(mockMqListener.actualMessage.getMessageProperties().getHeader("replyToExchange")).get())
        .isEqualTo("ex.workflow");
    Assertions.assertThat(Optional.ofNullable(mockMqListener.actualMessage.getMessageProperties().getHeader("replyToEvent")).get())
        .isEqualTo("workflow.callLog.update.reply");
    container.stop();
  }

  @Test
  public void givenEntityUpdatedCommand_withCallLogUpdateAndShouldExpectReplyFalse_shouldPublishAndNotSendMessageInHeader()
      throws InterruptedException, JSONException {
    // given
    var container = initializeRabbitMqListener(QUEUE, WORKFLOW_EXCHANGE, "workflow.callLog.update");

    // when
    Metadata metadata = new Metadata(99L, 500L, CALL_LOG, null, null, EntityAction.CREATED);
    CallLogDetail callLogDetail = new CallLogDetail();
    callLogDetail.setCallType(CallType.OUTGOING);
    callLogDetail.setOutcome(OutcomeType.IN_PROGRESS);
    callLogDetail.setDuration(123123);
    entityUpdatedCommandPublisher.execute(metadata, callLogDetail, false);

    // then
    mockMqListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        "{\"entity\":{\"outcome\":\"in_progress\",\"callType\":\"outgoing\",\"duration\":123123},\"metadata\":{\"eventId\":null,\"tenantId\":99,\"userId\":500,\"entityType\":\"CALL_LOG\",\"workflowId\":null,\"executedWorkflows\":[],\"entityAction\":\"CREATED\",\"executeWorkflow\":true,\"entityId\":0,\"workflowName\":null}}",
        new String(mockMqListener.actualMessage.getBody()), true);

    Assertions.assertThat(Optional.ofNullable(mockMqListener.actualMessage.getMessageProperties().getHeader("replyToExchange")))
        .isEmpty();
    Assertions.assertThat(Optional.ofNullable(mockMqListener.actualMessage.getMessageProperties().getHeader("replyToEvent")))
        .isEmpty();
    container.stop();
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    Message actualMessage;

    public void receiveMessage(Message message) {
      this.actualMessage = message;
    }
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queueName, String exchangeName, String eventName) {
    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);
    rabbitAdmin.declareBinding(
        BindingBuilder.bind(queue)
            .to(new TopicExchange(exchangeName))
            .with(eventName));
    var listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");
    listenerAdapter.setMessageConverter(null);
    var container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }
}