package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Currency {

  private final long id;
  private final String code;

  @JsonCreator
  public Currency(@JsonProperty("id") long id, @JsonProperty("code") String code) {
    this.id = id;
    this.code = code;
  }
}
