package com.kylas.sales.workflow.domain.workflow.action;


import com.kylas.sales.workflow.common.dto.condition.FieldCategory;
import com.kylas.sales.workflow.layout.api.response.list.FieldType;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EditPropertyFieldMapping implements Serializable {
  private String fieldName;
  private FieldType fieldType;
  private FieldCategory fieldCategory;
  private String variable;
  private String ifMissing;

  public EditPropertyFieldMapping(String fieldName, FieldType fieldType, FieldCategory fieldCategory) {
    this.fieldName = fieldName;
    this.fieldType = fieldType;
    this.fieldCategory = fieldCategory;
  }

  public EditPropertyFieldMapping(String fieldName, FieldType fieldType, FieldCategory fieldCategory, String variable) {
    this.fieldName = fieldName;
    this.fieldType = fieldType;
    this.fieldCategory = fieldCategory;
    this.variable = variable;
  }
}
