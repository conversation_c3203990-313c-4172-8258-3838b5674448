package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.EntityType.COMPANY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CompanyAttribute.ANNUAL_REVENUE;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.client.CompanyResponse;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CompanyParameterBuilder extends ParameterBuilder {

  protected CompanyParameterBuilder(UserService userService,
      ConfigService configService) {
    super(userService, configService);
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      return getNestedProperty(entity, parameter.fetchPathToField());
    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    return null;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return COMPANY.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    CompanyResponse company = (CompanyResponse) entityDetail;
    return getCurrencyIfRequired(webhookAction, jwtToken, company.getAnnualRevenue())
        .map(tuple ->
            webhookAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = company;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  EntityType entityType = parameter.getEntity().getType();
                  if (entityType.equals(COMPANY) && parameter.getAttribute().equals(ANNUAL_REVENUE.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedAnnualRevenueMoneyText(company, tuple));
                  }
                  else if(entityType.equals(COMPANY) &&  ObjectUtils.isNotEmpty(company.getCustomFieldValues()) && (company.getCustomFieldValues().get(parameter.getAttribute())) instanceof List){
                    List<Map<String,Object>> values= (List<Map<String, Object>>) (company.getCustomFieldValues().get(parameter.getAttribute()));
                    List<Object> valueNames =values.stream().map(value->(String)value.get("name")).collect(toList());
                    return new SimpleEntry<>(paramKey, valueNames);
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entity, String authToken) {
    return Map.of();
  }

  private List<Object> getFormattedAnnualRevenueMoneyText(CompanyResponse company, IdName t5) {
    try {
      return List.of(t5.getName() + " " + company.getAnnualRevenue().getValue());
    } catch (NullPointerException ignored) {
    }
    return List.of("");
  }

}
