package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.Actionable;
import java.io.Serializable;
import java.util.HashMap;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@JsonInclude(Include.NON_NULL)
public class LeadConversion implements Serializable, Actionable {

  private ConversionRequest deal;
  private ConversionRequest contact;
  private ConversionRequest company;

  @Getter
  public static class ConversionRequest {

    private final ConversionMode mode;
    private final HashMap<String, Object> details;

    public ConversionRequest(
        @JsonProperty("mode") ConversionMode mode,
        @JsonProperty("details") HashMap<String, Object> details) {
      this.mode = mode;
      this.details = details;
    }
  }

  public enum ConversionMode {
    CREATE
  }

  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.lead.convert";
  }
}
