package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CONTACT_OWNER;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CREATED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.UPDATED_BY;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MarketplaceEntity;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ContactParameterBuilder extends ParameterBuilder {

  ConfigService configService;

  @Autowired
  public ContactParameterBuilder(UserService userService, ConfigService configService) {
    super(userService, configService);
    this.configService = configService;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return EntityType.CONTACT.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    ContactDetail contact = (ContactDetail) entityDetail;
    return Mono
        .zip(
            getUserIfRequired(webhookAction, jwtToken, CONTACT_OWNER, contact.getOwnerId()),
            getUserIfRequired(webhookAction, jwtToken, CREATED_BY, contact.getCreatedBy()),
            getUserIfRequired(webhookAction, jwtToken, UPDATED_BY, contact.getUpdatedBy()),
            getTenantIfRequired(webhookAction, jwtToken)
        ).map(tuple ->
            webhookAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = contact;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  if (parameter.getEntity().equals(CONTACT_OWNER)) {
                    entity = UserDetails.from(tuple.getT1());
                  } else if (parameter.getEntity().equals(CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT2());
                  } else if (parameter.getEntity().equals(UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT3());
                  } else if (parameter.getEntity().equals(TENANT)) {
                    entity = tuple.getT4();
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entityDetail, String jwtToken) {
    ContactDetail contact = (ContactDetail) entityDetail;
    return Mono
        .zip(
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.CONTACT_OWNER, contact.getOwnerId()),
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.CREATED_BY, contact.getCreatedBy()),
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.UPDATED_BY, contact.getUpdatedBy()),
            getTenantIfRequired(marketplaceAction, jwtToken)
        ).map(tuple ->
            marketplaceAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = contact;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  if (parameter.getEntity().equals(MarketplaceEntity.CONTACT_OWNER)) {
                    entity = UserDetails.from(tuple.getT1());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT2());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT3());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.TENANT)) {
                    entity = tuple.getT4();
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
      try {
        if (!parameter.isStandard() && entity instanceof EntityDetail) {
          ContactDetail entityDetail = (ContactDetail) entity;
          actualValue = getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
          if (ObjectUtils.isNotEmpty(actualValue) && entityDetail.getIdNameStore().containsKey(parameter.getAttribute())) {
            return entityDetail.getIdNameStore().getOrDefault(parameter.getAttribute(), new HashMap<>())
                .getOrDefault(Long.valueOf(actualValue), actualValue);
          }
          return actualValue;
        }
        actualValue = getNestedProperty(entity, parameter.fetchPathToField());
      } catch (NestedNullException ignored) {
      } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
        log.error("Exception occurred while getting actual value for {}", parameter.getName());
      }
      return actualValue;
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard() && entity instanceof EntityDetail) {
        ContactDetail contact = (ContactDetail) entity;
        actualValue = getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        if (ObjectUtils.isNotEmpty(actualValue) && contact.getIdNameStore().containsKey(parameter.getAttribute())) {
          return contact.getIdNameStore().getOrDefault(parameter.getAttribute(), new HashMap<>())
              .getOrDefault(Long.valueOf(actualValue), actualValue);
        }
      }
      var fetchPathToFieldOptional = parameter.fetchPathToField();
      if(fetchPathToFieldOptional.isPresent()) {
        return getNestedProperty(entity,fetchPathToFieldOptional.get());
      }
      return actualValue;
    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }
}
