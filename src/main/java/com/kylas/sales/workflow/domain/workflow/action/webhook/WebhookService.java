package com.kylas.sales.workflow.domain.workflow.action.webhook;

import static com.kylas.sales.workflow.common.dto.ActionDetail.WebhookAction.AuthorizationType.NONE;
import static java.util.Collections.emptyMap;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.springframework.http.HttpMethod.GET;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.config.WebClientBuilderFactory;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.exception.WorkflowExecutionException;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.action.webhook.parameter.ParamKey;
import com.kylas.sales.workflow.domain.workflow.action.webhook.parameter.ParameterBuilder;
import com.kylas.sales.workflow.domain.workflow.executionLogs.WebhookLogPayload;
import com.kylas.sales.workflow.error.ErrorCode;
import com.kylas.sales.workflow.security.AuthService;
import java.io.IOException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ClientHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Flux;
import reactor.util.function.Tuples;

@Service
@Slf4j
public class WebhookService {

  private static final Consumer<HttpHeaders> NO_HEADER_CONSUMER = headers -> {
  };

  private final EntityTypeConfiguration entityTypeConfiguration;
  private final AuthService authService;
  private final WebClientBuilderFactory webClientBuilderFactory;
  private final CryptoService cryptoService;
  private final ObjectMapper objectMapper;
  private final List<ParameterBuilder> parameterBuilders;
  private final ExecutionLogFacade executionLogFacade;

  public static final List<String> attributesList = List.of("phoneNumbers", "emails", "associatedContacts", "companyEmployees", "products", "relation",
      "relatedTo","participants", "openedAt", "attachments", "clickedAt", "receivedBy");

  @Autowired
  public WebhookService(EntityTypeConfiguration entityTypeConfiguration, AuthService authService,
      WebClientBuilderFactory webClientBuilderFactory, CryptoService cryptoService, ObjectMapper objectMapper,
      List<ParameterBuilder> parameterBuilders, ExecutionLogFacade executionLogFacade) {
    this.entityTypeConfiguration = entityTypeConfiguration;
    this.authService = authService;
    this.webClientBuilderFactory = webClientBuilderFactory;
    this.cryptoService = cryptoService;
    this.objectMapper = objectMapper;
    this.parameterBuilders = parameterBuilders;
    this.executionLogFacade = executionLogFacade;
  }

  public void execute(WebhookAction webhookAction, EntityDetail entity, EntityType entityType) {
    log.info("Executing webhook action with name {} & Id {}", webhookAction.getName(), webhookAction.getId());
    log.info("====Webhook action=entity:{}=====", entity);
    String authToken = authService.getAuthenticationToken();
    var requestParameters = parameterBuilders.stream()
        .filter(parameterBuilder -> parameterBuilder.canBuild(entityType))
        .findFirst()
        .map(parameterBuilder -> parameterBuilder.build(webhookAction, entity, authToken))
        .orElse(null);

    var uri = UriComponentsBuilder
        .fromUriString(webhookAction.getRequestUrl())
        .queryParams(buildQueryParams(webhookAction, requestParameters))
        .build()
        .toUri();
    log.info("Prepared uri is {}", uri);

    Map<String, Object> requestBodyParamsMap = getRequestBodyMap(webhookAction.getMethod(), requestParameters);
    ExecutionLog executionLog = createExecutionLog(webhookAction, uri, requestBodyParamsMap, entity);

    webClientBuilderFactory.getWebClientBuilder()
        .build()
        .method(webhookAction.getMethod())
        .uri(uri)
        .body(buildRequestBody(webhookAction.getMethod(), requestBodyParamsMap))
        .headers(buildAuthorizationHeader(webhookAction))
        .exchange()
        .flatMap(clientResponse -> {
          return clientResponse.bodyToMono(String.class)
              .defaultIfEmpty("")
              .map(body -> Tuples.of(clientResponse.statusCode(), body));
        })
        .doOnError(throwable -> {
          log.error("Error in executing webhook action, error: {}", throwable.getMessage(), throwable);
          if (executionLog != null) {
            executionLogFacade.markAsFailed(executionLog.getId(), 500, throwable.getMessage());
          }
        })
        .subscribe(tuples -> {
          HttpStatus statusCode = tuples.getT1();
          String responseBody = tuples.getT2();
          log.debug("Received webhook response {} webhook actionId {}", responseBody, webhookAction.getId());
          if (executionLog != null) {
            if(!statusCode.is2xxSuccessful()) {
              log.error("Webhook actionId {} errorCode {}",webhookAction.getId(),statusCode.value());
              executionLogFacade.markAsFailed(executionLog.getId(), statusCode.value(), responseBody);
            } else {
              executionLogFacade.markAsSuccess(executionLog.getId(), statusCode.value());
            }
          }
        });
    log.info("Executed webhook action with name {} & Id {}", webhookAction.getName(), webhookAction.getId());
  }

  public Flux<EntityConfig> getConfigurations(EntityType entityType, Optional<String> triggerId) {
    return entityTypeConfiguration.getConfigurations(entityType,triggerId);
  }

  private BodyInserter<?, ? super ClientHttpRequest> buildRequestBody(HttpMethod method, Map<String, Object> parameters) {
    if (method.equals(GET)) {
      return BodyInserters.empty();
    }
    return BodyInserters.fromValue(parameters);
  }

  private Map<String, Object> getRequestBodyMap(HttpMethod method, Map<ParamKey, List<Object>> parameters) {
    if (method.equals(GET)) {
      return Collections.emptyMap();
    }
    return parameters.entrySet().stream()
        .filter(entry -> isNotEmpty(entry.getValue()))
        .collect(toMap(entry -> entry.getKey().getName(),
            entry -> !attributesList.contains(entry.getKey().getAttributeName()) && entry.getValue().size() == 1 ? entry.getValue().get(0)
                : entry.getValue()));
  }

  private LinkedMultiValueMap<String, String> buildQueryParams(WebhookAction webhookAction, Map<ParamKey, List<Object>> requestParameters) {

    if (requestParameters == null) {
      return new LinkedMultiValueMap<>(emptyMap());
    }

    Map<String, List<String>> localRequestParameters = requestParameters.entrySet().stream()
        .collect(Collectors
            .toMap(param -> param.getKey().getName(), param -> param.getValue().stream().map(String::valueOf).collect(Collectors.toList())));

    return new LinkedMultiValueMap<>(
        webhookAction.getMethod().equals(GET) ? localRequestParameters : emptyMap());
  }

  private Consumer<HttpHeaders> buildAuthorizationHeader(WebhookAction action) {
    if (action.getAuthorizationType().equals(NONE)) {
      return NO_HEADER_CONSUMER;
    }
    AuthorizationParameter auth;
    try {
      auth = objectMapper.readValue(
          Base64.getDecoder().decode(cryptoService.decrypt(action.getAuthorizationParameter())),
          AuthorizationParameter.class);
    } catch (IOException e) {
      log.error("Exception while setting authorization header for webhook action {}", action.getId(), e);
      throw new WorkflowExecutionException(ErrorCode.INVALID_PARAMETER);
    }

    return httpHeaders -> {
      switch (action.getAuthorizationType()) {
        case API_KEY:
          httpHeaders.add(auth.getKeyName(), auth.getValue());
          break;
        case BASIC_AUTH:
          httpHeaders.setBasicAuth(auth.getUsername(), auth.getPassword());
          break;
        case BEARER_TOKEN:
          httpHeaders.setBearerAuth(auth.getToken());
      }
    };
  }

  private ExecutionLog createExecutionLog(WebhookAction webhookAction, URI uri, Map<String, Object> requestBodyParamsMap, EntityDetail entity) {
    try {
      WebhookLogPayload webhookLogPayload = new WebhookLogPayload(
          webhookAction.getName(), webhookAction.getDescription(),
          uri.toString(), webhookAction.getMethod(), webhookAction.getAuthorizationType(),
          requestBodyParamsMap
      );
      ObjectMapper localMapper = objectMapper.copy();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
      sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
      localMapper.setDateFormat(sdf);
      Map actionPayload = localMapper.convertValue(webhookLogPayload, Map.class);
      return executionLogFacade.createExecutionLog(webhookAction, entity, actionPayload);
    } catch (Exception e) {
      log.error("Error in creating execution log payload for webhook action, actionId: {}, message: {}",
          webhookAction.getId(), e.getMessage(), e);
    }
    return null;
  }
}
