package com.kylas.sales.workflow.domain.service;

import static java.util.Objects.isNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.common.dto.condition.IdNameField;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.exception.InvalidConditionException;
import com.kylas.sales.workflow.domain.exception.InvalidWorkflowPropertyException;
import com.kylas.sales.workflow.domain.processor.Pipeline;
import com.kylas.sales.workflow.domain.processor.deal.Money;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Participants;
import com.kylas.sales.workflow.domain.service.client.SearchService;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.user.UserFacade;
import com.kylas.sales.workflow.error.ErrorCode;
import com.kylas.sales.workflow.error.ErrorResource;
import java.lang.reflect.Array;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ValueResolver {

  private final PipelineService pipelineService;
  private final ProductService productService;
  private final ObjectMapper objectMapper;
  private final UserService userService;
  private final CompanyService companyService;
  private final SearchService searchService;
  private final UserFacade userFacade;

  @Autowired
  public ValueResolver(PipelineService pipelineService, ProductService productService, ObjectMapper objectMapper,
      UserService userService, CompanyService companyService, SearchService searchService, UserFacade userFacade) {
    this.pipelineService = pipelineService;
    this.productService = productService;
    this.objectMapper = objectMapper;
    this.userService = userService;
    this.companyService = companyService;
    this.searchService = searchService;
    this.userFacade = userFacade;
  }

  public Mono<IdName> resolveNamesOfIdNameFieldsExceptUserFields(String fieldName, Object value, String authentication) {

    if (isNull(value)) {
      return Mono.empty();
    }

    IdNameField field = IdNameField.getFieldByName(fieldName);
    try {
      var idName = objectMapper.readValue(serialize(value), IdName.class);
      switch (field) {
        case PIPELINE:
          return pipelineService.getPipeline(idName.getId(), authentication);
        case PIPELINE_STAGE:
          return pipelineService.getPipelineStage(idName.getId(), authentication);
        case PRODUCT:
          return productService.getProductNameById(idName.getId(), authentication);
        case COMPANY:
          return companyService.getCompanyNameById(idName.getId(), authentication);
      }
    } catch (JsonProcessingException e) {
      log.error("error in parsing json", e);
    }
    throw new InvalidActionException();
  }

  public Mono<List<IdName>> resolveNamesForIdNameFields(String fieldName, Object value, String authentication) {
    if (isNull(value)) {
      return Mono.empty();
    }
    try {
      List<IdName> idNames = objectMapper.readValue(objectMapper.writeValueAsString(value),
          new TypeReference<>() {
            @Override
            public Type getType() {
              return super.getType();
            }
          });
      IdNameField field = IdNameField.getFieldByName(fieldName);
      List<Long> ids = idNames
          .stream()
          .map(IdName::getId)
          .collect(Collectors.toList());
      switch (field) {
        case PIPELINE:
          return pipelineService.getPipelinesSummaries(ids, authentication);
        case PIPELINE_STAGE:
          return pipelineService.getPipelineStagesSummaries(ids, authentication);
        case COMPANY:
          return searchService.getCompanySummaries(ids, authentication);
      }
    } catch (JsonProcessingException e) {
      log.error("error in parsing json", e);
      throw new InvalidConditionException();
    }
    throw new InvalidConditionException();
  }

  public Mono<IdName> getUser(Object user, String authenticationToken) {
    if (isNull(user)) {
      return Mono.empty();
    }
    try {
      var userIdName = objectMapper.readValue(serialize(user), IdName.class);
      return userService.getUserDetails(userIdName.getId(), authenticationToken)
          .map(userResponse -> new IdName(userResponse.getId(), userResponse.getName()));
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting userId from {}", user);
      throw new InvalidActionException();
    }
  }

  public Mono<List<IdName>> getUsers(Object value, String authenticationToken) {
    try {
      List<IdName> users = objectMapper.readValue(objectMapper.writeValueAsString(value),
          new TypeReference<>() {
            @Override
            public Type getType() {
              return super.getType();
            }
          });
      List<Long> userIds = users.stream()
          .map(IdName::getId)
          .collect(Collectors.toList());
      return searchService.getUsersSummaries(userIds, authenticationToken);
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting userId from {}", value);
      throw new InvalidActionException();
    }
  }

  public IdName getIdNameFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), IdName.class);
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting IdName from {}", value);
      throw new IllegalArgumentException();
    }
  }

  public String serialize(Object value) {
    try {
      return objectMapper.writeValueAsString(value);
    } catch (JsonProcessingException e) {
      log.error("Exception while serializing value {}", value);
      throw new IllegalArgumentException();
    }
  }

  public List getListFrom(String value) {
    try {
      return objectMapper.readValue(value, List.class);
    } catch (JsonProcessingException e) {
      log.error("Exception while serializing value {}", value);
      throw new IllegalArgumentException();
    }
  }

  public List<IdName> getListOfIdNameFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), new TypeReference<>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
    } catch (JsonProcessingException e) {
      log.error("Exception while serializing value {}", value);
      throw new IllegalArgumentException();
    }
  }

  public Pipeline getPipelineFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), Pipeline.class);
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting IdName from {}", value);
      throw new IllegalArgumentException();
    }
  }

  public Money getCurrencyIdValueFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), Money.class);
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting currencyIdValue from {}", value);
      throw new IllegalArgumentException();
    }
  }

  public HashMap getCurrencyIdValueMapFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), HashMap.class);
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting currencyIdValue from {}", value);
      throw new IllegalArgumentException();
    }
  }

  public List<Participants> getMeetingParticipantsFrom(Object value) {
    try {
      return objectMapper.readValue(serialize(value), new TypeReference<>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
    } catch (JsonProcessingException e) {
      log.error("Exception while extracting value for meeting participants, value {}", value);
      return Collections.emptyList();
    }
  }

  public Mono<String> getUserName(Long userId, String authenticationToken) {
    return userService.getUserDetails(userId, authenticationToken).map(User::getName);
  }

  public Mono<String> getTeamName(Long teamId, String authenticationToken) {
    return userService.getTeamDetails(Collections.singletonList(teamId), authenticationToken)
        .flatMapMany(Flux::fromIterable)
        .filter(idName -> idName.getId().equals(teamId))
        .map(IdName::getName)
        .next()
        .switchIfEmpty(Mono.error(new InvalidWorkflowPropertyException(ErrorCode.TEAM_NOT_EXIST)));
  }

  public List<Long> getListOfIds(Object obj) {
    return objectMapper.convertValue(obj, new TypeReference<>() {
      @Override
      public Type getType() {
        return super.getType();
      }
    });
  }

  public Map<String, Object> getEntityData(Object entity) {
    Map<String, Object> entityData = new HashMap<>();
    try {
      String json = objectMapper.writeValueAsString(entity);
      entityData = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
      });
    } catch (JsonProcessingException e) {
      return Collections.emptyMap();
    }
    return  entityData;
  }

  public String getOrUpdateTenantUserTimezone(Long tenantId, String authenticationToken) {
    try {
      Optional<User> userOptional = userFacade.tryGetTenantUser(tenantId);
      if (userOptional.isPresent() && ObjectUtils.isNotEmpty(userOptional.get().getTimezone())) {
        User user = userOptional.get();
        log.info("Tenant user timezone is present in db, userId: {}, tenantId: {}, timezone: {}",
            user.getId(), user.getTenantId(), user.getTimezone());
        return user.getTimezone();
      }

      log.info("Initiating get tenant user details api call for tenantId: {}", tenantId);
      return userService.getTenantCreator(tenantId, authenticationToken)
          .map(userDetails -> userFacade.getExistingOrCreateNewUser(userDetails, tenantId))
          .map(user -> {
            log.info("Updated tenant user with timezone, userId: {}, tenantId: {}, timezone: {}", user.getId(), user.getTenantId(), user.getTimezone());
            return user.getTimezone();
          }).block();
    } catch (Exception e) {
      log.error("Unable to get timezone of tenant user tenantId: {}, error: {}", tenantId, e.getMessage(), e);
      return null;
    }
  }

  public String getOrUpdateUserTimezone(Long userId, Long tenantId, String authenticationToken) {
    Optional<User> userOptional = userFacade.tryGetUserByIdAndTenantId(userId, tenantId);
    if (userOptional.isPresent() && ObjectUtils.isNotEmpty(userOptional.get().getTimezone())) {
      User user = userOptional.get();
      log.info("User timezone is present in db, userId: {}, tenantId: {}, timezone: {}",
          user.getId(), user.getTenantId(), user.getTimezone());
      return user.getTimezone();
    }

    log.info("Initiating get user details api call for userId: {}, tenantId: {}", userId, tenantId);
    return userService.getUserDetails(userId, authenticationToken)
        .map(userDetails -> userFacade.getExistingOrCreateNewUser(userDetails, tenantId))
        .map(user -> {
          log.info("Updated user with timezone, userId: {}, tenantId: {}, timezone: {}", user.getId(), user.getTenantId(), user.getTimezone());
          return user.getTimezone();
        })
        .block();
  }

  public List<Long> getOrUpdateUserReportingManagers(Long userId, Long tenantId, String authenticationToken) {
    Optional<User> userOptional = userFacade.tryGetUserByIdAndTenantId(userId, tenantId);
    if (userOptional.isPresent() && userOptional.get().getReportingManagers() != null) {
      User user = userOptional.get();
      log.info("User's reporting managers are present in db, userId: {}, tenantId: {}, reportingManagers: {}",
          userId, tenantId, user.getReportingManagers());
      return user.getReportingManagers();
    }

    log.info("User's reporting managers are not present in db, fetch user details and update managers, userId: {}, tenantId: {}",
        userId, tenantId);
    return userService.getUserDetails(userId, authenticationToken)
        .map(userDetails -> userFacade.getExistingOrCreateNewUser(userDetails, tenantId))
        .map(user -> {
          log.info("Returning updated user with reporting managers, userId: {}, tenantId: {}, reportingManagers: {}", user.getId(), user.getTenantId(),
              user.getReportingManagers());
          return user.getReportingManagers();
        })
        .block();
  }
}
