package com.kylas.sales.workflow.domain.workflow.action.share;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.SHARE;
import static java.util.Objects.isNull;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;

import javax.persistence.*;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class ShareAction extends AbstractWorkflowAction implements WorkflowAction {

  @Enumerated(value = EnumType.STRING)
  private EntityType toType;
  private Long toId;
  private String name;
  @Convert( converter= TextToJsonConverter.class)
  private Action permissions;

  public ShareAction(String name, EntityType toType, Long toId, Action permissions) {
    this.name = name;
    this.toType = toType;
    this.toId = toId;
    this.permissions = permissions;
  }

  @Override
  public ShareAction update(ActionResponse action) {
    var payload = (ActionDetail.ShareAction) action.getPayload();
    if (isNull(payload.getToType()) || isNull(payload.getPermissions())) {
      throw new InvalidActionException();
    }
    this.setName(payload.getName());
    this.setToType(payload.getToType());
    this.setToId(payload.getToIdName() != null ? payload.getToIdName().getId(): null);
    this.setPermissions(payload.getPermissions());
    return this;
  }

  @Override
  public ActionType getType() {
    return SHARE;
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    var payload = (ActionDetail.ShareAction) actionResponse.getPayload();
    if (isNull(payload.getToType()) || isNull(payload.getPermissions())) {
      throw new InvalidActionException();
    }
    return new ShareAction(payload.getName(), payload.getToType(), payload.getToIdName() == null ? null: payload.getToIdName().getId(), payload.getPermissions());
  }

  public static ActionResponse toActionResponse(ShareAction action) {
    return new ActionResponse(action.getId(), SHARE, new ActionDetail.ShareAction(action.getName(), action.getToType(), action.getToId() == null ? null : new IdName(action.getToId(), null),action.getPermissions()));
  }
}
