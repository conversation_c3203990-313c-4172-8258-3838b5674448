package com.kylas.sales.workflow.domain.workflow.action.generateCallSummary;

import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.processor.callLog.GenerateCallSummaryDetail;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.mq.command.EntityUpdatedCommandPublisher;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GenerateCallSummaryService {
  private final EntityUpdatedCommandPublisher entityUpdatedCommandPublisher;
  private final ExecutionLogFacade executionLogFacade;

  public GenerateCallSummaryService(EntityUpdatedCommandPublisher entityUpdatedCommandPublisher, ExecutionLogFacade executionLogFacade) {
    this.entityUpdatedCommandPublisher = entityUpdatedCommandPublisher;
    this.executionLogFacade = executionLogFacade;
  }

  public void processGenerateCallSummaryAction(Metadata metadata, EntityEvent event, GenerateCallSummaryAction generateCallSummaryAction) {
    HashMap<String, Object> generateCallSummaryDetails = new HashMap<>();
    generateCallSummaryDetails.put("prompt", generateCallSummaryAction.getPrompt());

    ExecutionLog executionLog = executionLogFacade.createExecutionLog(generateCallSummaryAction, event.getEntity(), generateCallSummaryDetails);
    metadata.setEventId(executionLog.getId());

    entityUpdatedCommandPublisher
        .execute(metadata, new GenerateCallSummaryDetail(event.getEntityId(), generateCallSummaryAction.getPrompt()), true);
  }
}
