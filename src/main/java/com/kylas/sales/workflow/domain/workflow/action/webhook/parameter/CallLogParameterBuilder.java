package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_CONTACT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_DEAL;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_LEAD;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CREATED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.LOGGED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.UPDATED_BY;
import static java.util.Collections.emptyList;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.springframework.http.HttpMethod.GET;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CallLogAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MarketplaceEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class CallLogParameterBuilder extends ParameterBuilder {

  private final AssociatedEntityParameterBuilder associatedEntityParameterBuilder;

  @Autowired
  public CallLogParameterBuilder(UserService userService, ConfigService configService,
      AssociatedEntityParameterBuilder associatedEntityParameterBuilder) {
    super(userService, configService);
    this.associatedEntityParameterBuilder = associatedEntityParameterBuilder;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return EntityType.CALL_LOG.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    CallLogDetail callLog = (CallLogDetail) entityDetail;

    var buildObject =  Mono
        .zip(
            getUserIfRequired(webhookAction, jwtToken, LOGGED_BY, callLog.getOwner()),
            getUserIfRequired(webhookAction, jwtToken, CREATED_BY, callLog.getCreatedBy()),
            getUserIfRequired(webhookAction, jwtToken, UPDATED_BY, callLog.getUpdatedBy()),
            getTenantIfRequired(webhookAction, jwtToken)
        ).map(tuple ->
            webhookAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = callLog;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  if (parameter.getEntity().equals(LOGGED_BY)) {
                    entity = UserDetails.from(tuple.getT1());
                  } else if (parameter.getEntity().equals(CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT2());
                  } else if (parameter.getEntity().equals(UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT3());
                  } else if (parameter.getEntity().equals(TENANT)) {
                    entity = tuple.getT4();
                  } else if (AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity())){
                    return new SimpleEntry<>(new ParamKey("",""),emptyList());
                  } else if (WebhookEntity.CALL_LOG.equals(parameter.getEntity()) && parameter.getAttribute().equalsIgnoreCase(CallLogAttribute.RELATED_TO.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedRelations(webhookAction.getMethod(), callLog));
                  } else if (WebhookEntity.CALL_LOG.equals(parameter.getEntity()) && parameter.getAttribute().equalsIgnoreCase(CallLogAttribute.CUSTOMER_EMOTION.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedCustomerEmotion(webhookAction.getMethod(), callLog));
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();

    if (webhookAction.getParameters().stream().anyMatch(parameter -> AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity()))
        && !webhookAction.getMethod().equals(HttpMethod.GET)) {
      Map<ParamKey, List<Object>> associatedEntity = associatedEntityParameterBuilder.getAssociatedEntity(callLog.getAssociatedEntities(), jwtToken, webhookAction);
      buildObject.putAll(associatedEntity);
    }
    return buildObject;
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      return getNestedProperty(entity, parameter.fetchPathToField());

    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entityDetail, String authToken) {
    CallLogDetail callLog = (CallLogDetail) entityDetail;
    return Mono
        .zip(
            getUserIfRequired(marketplaceAction, authToken, MarketplaceEntity.LOGGED_BY, callLog.getOwner()),
            getUserIfRequired(marketplaceAction, authToken, MarketplaceEntity.CREATED_BY, callLog.getCreatedBy()),
            getUserIfRequired(marketplaceAction, authToken, MarketplaceEntity.UPDATED_BY, callLog.getUpdatedBy()),
            getTenantIfRequired(marketplaceAction, authToken)
        ).map(tuple ->
            marketplaceAction.getParameters().stream()
                .map(marketplaceActionParameter -> {
                  Object entity = callLog;
                  ParamKey paramKey = new ParamKey(marketplaceActionParameter.getName(), marketplaceActionParameter.getAttribute());
                  if (marketplaceActionParameter.getEntity().equals(MarketplaceEntity.LOGGED_BY)) {
                    entity = UserDetails.from(tuple.getT1());
                  } else if (marketplaceActionParameter.getEntity().equals(MarketplaceEntity.CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT2());
                  } else if (marketplaceActionParameter.getEntity().equals(MarketplaceEntity.UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT3());
                  } else if (marketplaceActionParameter.getEntity().equals(MarketplaceEntity.TENANT)) {
                    entity = tuple.getT4();
                  } else if (marketplaceActionParameter.getAttribute().equalsIgnoreCase(CallLogAttribute.RELATED_TO.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedRelations(marketplaceAction.getMethod(), callLog));
                  } else if (WebhookEntity.CALL_LOG.equals(marketplaceActionParameter.getEntity()) && marketplaceActionParameter.getAttribute().equalsIgnoreCase(CallLogAttribute.CUSTOMER_EMOTION.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedCustomerEmotion(marketplaceAction.getMethod(), callLog));
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(marketplaceActionParameter, entity, authToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      var fetchPathToFieldOptional = parameter.fetchPathToField();
      if(fetchPathToFieldOptional.isPresent()) {
        return getNestedProperty(entity,fetchPathToFieldOptional.get());
      }

    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }

  private List<Object> getFormattedRelations(HttpMethod method, CallLogDetail callLog) {
    if (ObjectUtils.isEmpty(callLog) || ObjectUtils.isEmpty(callLog.getRelatedTo())) {
      return null;
    }
    if (method == GET) {
      return callLog.getRelatedToIds(callLog.getRelatedTo());
    }
    return List.of(callLog.getRelatedTo());
  }

  private List<Object> getFormattedCustomerEmotion(HttpMethod method, CallLogDetail callLog) {
    if (ObjectUtils.isEmpty(callLog) || ObjectUtils.isEmpty(callLog.getRelatedTo())) {
      return null;
    }
    if (method == GET) {
      return callLog.getCustomerEmotions(callLog.getCustomerEmotion());
    }
    return List.of(callLog.getCustomerEmotion());
  }
}


