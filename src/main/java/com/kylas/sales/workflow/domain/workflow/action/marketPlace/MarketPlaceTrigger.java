package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class MarketPlaceTrigger implements Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private Long tenantId;
  private String entity;
  @ManyToOne
  @JoinColumn(name = "market_place_trigger_detail_id")
  private MarketPlaceTriggerDetail marketPlaceTriggerDetail;
  @OneToOne
  @JoinColumn(name = "workflow_id")
  private Workflow workflow;

  @Column(name="market_place_trigger_detail_id", updatable=false, insertable=false)
  public UUID marketPlaceTriggerDetailId;

  private MarketPlaceTrigger(Long tenantId, String entity) {
    this.tenantId=tenantId;
    this.entity=entity;
  }

  public MarketPlaceTrigger(Long tenantId, String entity, MarketPlaceTriggerDetail marketPlaceTriggerDetail) {
    this.tenantId=tenantId;
    this.entity=entity;
    this.marketPlaceTriggerDetail=marketPlaceTriggerDetail;
  }

  public static MarketPlaceTrigger createNew(MarketPlaceTriggerDetail marketPlaceTriggerDto,long tenantId) {
    if (marketPlaceTriggerDto == null) {
      return null;
    }
    MarketPlaceTrigger marketPlaceTrigger = new MarketPlaceTrigger(tenantId, marketPlaceTriggerDto.getEntity());
    marketPlaceTrigger.setMarketPlaceTriggerDetail(marketPlaceTriggerDto);
    return marketPlaceTrigger;
  }

  public MarketPlaceTrigger update(MarketPlaceTriggerDetail marketPlaceTriggerDetail) {
    if (marketPlaceTriggerDetail == null) {
      return null;
    }
    this.marketPlaceTriggerDetail= marketPlaceTriggerDetail;
    return this;
  }
}