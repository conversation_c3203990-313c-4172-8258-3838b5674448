package com.kylas.sales.workflow.domain.workflow.action;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TimeInterval implements Serializable {
  private Integer days;
  private Integer hours;
  private Integer minutes;

  public Date addDaysToDate(Date date){
    if (this.days == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.plusDays(this.days);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date addHoursToDate(Date date) {
    if (this.hours == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.plusHours(this.hours);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date addMinutesToDate(Date date) {
    if (this.minutes == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.plusMinutes(this.minutes);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date substractDaysFromDate(Date date){
    if (this.days == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.minusDays(this.days);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date substractHoursFromDate(Date date) {
    if (this.hours == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.minusHours(this.hours);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date substractMinutesFromDate(Date date) {
    if (this.minutes == null || date == null){
      return date;
    }
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    localDateTime = localDateTime.minusMinutes(this.minutes);
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public Date addTimeIntervalToDate(Date date){
    date = addDaysToDate(date);
    date = addHoursToDate(date);
    date = addMinutesToDate(date);
    return date;
  }

  public Date substractTimeIntervalFromDate(Date date){
    date = substractDaysFromDate(date);
    date = substractHoursFromDate(date);
    date = substractMinutesFromDate(date);
    return date;
  }
}
