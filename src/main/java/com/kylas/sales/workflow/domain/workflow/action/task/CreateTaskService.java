package com.kylas.sales.workflow.domain.workflow.action.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.FieldMetadataCacheService;
import com.kylas.sales.workflow.domain.exception.InvalidEntityException;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.processor.deal.DealDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.processor.task.AssignedToType;
import com.kylas.sales.workflow.domain.processor.task.TaskRelation;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.mq.CreateTaskEventPublisher;
import com.kylas.sales.workflow.mq.event.CreateTaskEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CreateTaskService {

  private final CreateTaskEventPublisher createTaskEventPublisher;
  private final ExecutionLogFacade executionLogFacade;
  private final ObjectMapper objectMapper;
  private final FieldMetadataCacheService fieldMetadataCacheService;
  private static final String ENTITY_ID = "entityId";
  private static final String ASSIGNED_TO_ID = "assignedToId";

  @Autowired
  public CreateTaskService(CreateTaskEventPublisher createTaskEventPublisher, ExecutionLogFacade executionLogFacade, ObjectMapper objectMapper,
      FieldMetadataCacheService fieldMetadataCacheService) {
    this.createTaskEventPublisher = createTaskEventPublisher;
    this.executionLogFacade = executionLogFacade;
    this.objectMapper = objectMapper;
    this.fieldMetadataCacheService = fieldMetadataCacheService;
  }

  public void processCreateTaskAction(CreateTaskAction createTaskAction, EntityType entityType, EntityDetail entityDetail, Metadata metadata) {

    Date updatedDueDate = getDueDate(createTaskAction.getDueDate());

    Map<String, Object> entityDetails = getEntityDetails(entityType, entityDetail, createTaskAction.getAssignedTo());
    long entityId = (Long) entityDetails.get(ENTITY_ID);
    long assignedToId = (Long) entityDetails.get(ASSIGNED_TO_ID);
    String name = (String) entityDetails.get("name");

    CreateTaskEvent createTaskEvent = new CreateTaskEvent(createTaskAction.getName(), createTaskAction.getDescription(),
        createTaskAction.getPriority(), createTaskAction.getReminder(), createTaskAction.getTaskType(), createTaskAction.getStatus(), assignedToId,
        Set.of(new TaskRelation(entityType, entityId)), updatedDueDate, createTaskAction.getCustomFields(), metadata);

    try {
      HashMap<String, Object> allValues = new HashMap<>();
      getValuesMap(createTaskEvent, allValues);
      Map<String, Object> fieldsMetadata = fieldMetadataCacheService.getFieldsMetadata(metadata.getTenantId(), EntityType.TASK);

      log.info("Fields metadata for tenantId: {}, entityType: {} is {}", metadata.getTenantId(), EntityType.TASK, fieldsMetadata);
      for (Entry<String, Object> stringObjectEntry : allValues.entrySet()) {
        executionLogFacade.createExecutionLog(allValues, stringObjectEntry.getKey(), stringObjectEntry.getValue(), fieldsMetadata);
      }
      log.info("All values for task are {}", allValues);
      HashMap updatedPayload = objectMapper.readValue(
          objectMapper.writeValueAsString(getUpdatedPayload(createTaskAction, allValues, createTaskEvent, name, entityDetails)), HashMap.class);
      ExecutionLog executionLog = executionLogFacade.createExecutionLog(createTaskAction, entityDetail, updatedPayload);
      createTaskEvent.getMetadata().setEventId(executionLog.getId());
    } catch (Exception e) {
      log.error("Error while processing create task action for entity {} with entityId {}", entityType, entityId, e);
    }

    log.info("publishing create task event for entity {} with entityId {}", entityType, entityId);
    createTaskEventPublisher.publishCreateTaskEvent(createTaskEvent);
  }

  private HashMap<String, Object> getUpdatedPayload(CreateTaskAction createTaskAction, HashMap<String, Object> allValues,
      CreateTaskEvent createTaskEvent, String name, Map<String, Object> entityDetails) {

    AssignedTo assignedTo = createTaskAction.getAssignedTo();
    if (assignedTo.getType().equals(AssignedToType.USER)) {
      Map<String, Object> map = objectMapper.convertValue(assignedTo, Map.class);
      map.remove("type");
      allValues.put("assignedTo", map);
    } else {
      if (AssignedToType.OWNER.equals(assignedTo.getType())) {
        IdName ownedBy = (IdName) entityDetails.get("ownedBy");
        allValues.put("assignedTo", Map.of("id", ownedBy.getId(), "name", ownedBy.getName()));
      } else {
        IdName createdBy = (IdName) entityDetails.get("createdBy");
        allValues.put("assignedTo", Map.of("id", createdBy.getId(), "name", createdBy.getName()));
      }
    }

    List<Map<String, Object>> relationList = new ArrayList<>();

    for (TaskRelation relation : createTaskEvent.getRelation()) {
      Map<String, Object> relationMap = objectMapper.convertValue(relation, Map.class);
      relationMap.put("name", name);
      relationList.add(relationMap);
    }

    allValues.put("relation", relationList);

    return allValues;
  }


  private void getValuesMap(CreateTaskEvent createTaskEvent, HashMap<String, Object> allValues) {
    Map<String, Object> map = objectMapper.convertValue(createTaskEvent, Map.class);
    map.remove("metadata");
    allValues.putAll(map);
    allValues.putAll((Map<? extends String, ?>) map.getOrDefault("customFieldValues", Collections.emptyMap()));
    allValues.remove("customFieldValues");
  }

  private Date getDueDate(DueDate dueDate) {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.DAY_OF_MONTH, dueDate.getDays());
    calendar.add(Calendar.HOUR_OF_DAY, dueDate.getHours());
    return calendar.getTime();
  }

  private Map<String, Object> getEntityDetails(EntityType entityType, EntityDetail entityDetail, AssignedTo assignedTo) {
    switch (entityType) {
      case LEAD:
        LeadDetail leadDetail = (LeadDetail) entityDetail;
        long assignedToLeadId = assignedTo.getType().equals(AssignedToType.USER) ? assignedTo.getId()
            : getAssignedToFromIdName(assignedTo, leadDetail.getOwnerId(), leadDetail.getCreatedBy());
        String name = leadDetail.getFirstName() + " " + leadDetail.getLastName();
        return getAssignedToMap(leadDetail.getId(), assignedToLeadId, name, leadDetail.getOwnerId(), leadDetail.getCreatedBy());

      case CONTACT:
        ContactDetail contactDetail = (ContactDetail) entityDetail;
        long assignedToContactId = assignedTo.getType().equals(AssignedToType.USER) ? assignedTo.getId()
            : getAssignedToFromIdName(assignedTo, contactDetail.getOwnerId(), contactDetail.getCreatedBy());
        String contactName = contactDetail.getFirstName() + " " + contactDetail.getLastName();
        return getAssignedToMap(contactDetail.getId(), assignedToContactId, contactName, contactDetail.getOwnerId(), contactDetail.getCreatedBy());

      case DEAL:
        DealDetail dealDetail = (DealDetail) entityDetail;
        long assignedToDealId = assignedTo.getType().equals(AssignedToType.USER) ? assignedTo.getId()
            : getAssignedToFromIdName(assignedTo, dealDetail.getOwnedBy(), dealDetail.getCreatedBy());
        return getAssignedToMap(dealDetail.getId(), assignedToDealId, dealDetail.getName(), dealDetail.getOwnedBy(), dealDetail.getCreatedBy());
      default:
        throw new InvalidEntityException();
    }
  }

  private Long getAssignedToFromIdName(AssignedTo assignedTo, IdName ownerId, IdName createdBy) {
    return assignedTo.getType().equals(AssignedToType.OWNER) ? ownerId.getId() : createdBy.getId();
  }

  private Map<String, Object> getAssignedToMap(long entityId, long assignedToId, String name, IdName ownedBy, IdName createdBy) {
    Map<String, Object> detailsMap = new HashMap<>();
    detailsMap.put(ENTITY_ID, entityId);
    detailsMap.put(ASSIGNED_TO_ID, assignedToId);
    detailsMap.put("name", name);
    detailsMap.put("ownedBy", ownedBy);
    detailsMap.put("createdBy", createdBy);
    return detailsMap;
  }
}
