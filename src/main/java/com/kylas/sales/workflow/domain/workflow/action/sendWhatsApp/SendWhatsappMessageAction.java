package com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class SendWhatsappMessageAction extends AbstractWorkflowAction implements WorkflowAction {

  @NotNull
  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "id", column = @Column(name = "connected_account_id")),
      @AttributeOverride(name = "name", column = @Column(name = "connected_account_name"))
  })
  private ConnectedAccount connectedAccount;


  @OneToMany(fetch = FetchType.EAGER, mappedBy = "sendWhatsappMessageAction", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<SendWhatsappMessageType> to = new HashSet<>();

  @NotNull
  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "id", column = @Column(name = "template_id")),
      @AttributeOverride(name = "name", column = @Column(name = "template_name"))
  })
  private Template template;

  public SendWhatsappMessageAction(ConnectedAccount connectedAccount, Set<SendWhatsappMessageType> to, Template template) {
    to.forEach(whatsappMessageType -> whatsappMessageType.setSendWhatsappMessageAction(this));
    this.connectedAccount = connectedAccount;
    this.to = to;
    this.template = template;
  }

  public static AbstractWorkflowAction createNew(ActionResponse action) {
    return SendWhatsappMessageMapper.fromActionResponse(action);
  }

  public static ActionResponse toActionResponse(SendWhatsappMessageAction workflowAction) {
    return new ActionResponse(workflowAction.getId(), workflowAction.getType(),
        SendWhatsappMessageMapper.fromSendWhatsappMessageAction(workflowAction));
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse action) {
    var payload = (ActionDetail.SendWhatsappMessageAction) action.getPayload();
    this.setConnectedAccount(payload.getConnectedAccount());
    payload.getTo().forEach(whatsappMessageType -> whatsappMessageType.setSendWhatsappMessageAction(this));
    this.setTo(payload.getTo());
    this.setTemplate(payload.getTemplate());
    return this;
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public ActionType getType() {
    return ActionType.SEND_WHATSAPP_MESSAGE;
  }

  @Component
  private static class SendWhatsappMessageMapper {

    private static ActionDetail.SendWhatsappMessageAction fromSendWhatsappMessageAction(SendWhatsappMessageAction whatsappMessageAction) {
      return new ActionDetail.SendWhatsappMessageAction(whatsappMessageAction.connectedAccount, whatsappMessageAction.to,
          whatsappMessageAction.template);
    }

    private static SendWhatsappMessageAction fromActionResponse(ActionResponse actionResponse) {
      ActionDetail.SendWhatsappMessageAction sendWhatsappMessageAction = (ActionDetail.SendWhatsappMessageAction) actionResponse.getPayload();
      return new SendWhatsappMessageAction(sendWhatsappMessageAction.getConnectedAccount(), sendWhatsappMessageAction.getTo(),
          sendWhatsappMessageAction.getTemplate());
    }
  }
}
