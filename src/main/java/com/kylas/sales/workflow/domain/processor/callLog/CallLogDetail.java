package com.kylas.sales.workflow.domain.processor.callLog;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.kylas.sales.workflow.domain.processor.Actionable;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.EntityResponse;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CallLogDetail extends EntityResponse implements Serializable, EntityDetail, Actionable {
  private Long id;
  private Long tenantId;
  private OutcomeType outcome;
  private Date startTime;
  private String phoneNumber;
  private CallType callType;
  private Integer duration;
  private Boolean isManual;
  private String originator;
  private String receiver;
  private IdName createdBy;
  private IdName updatedBy;
  private IdName owner;
  private RelatedTo[] relatedTo;
  private CallRecording callRecording;
  private Map<String, Object> customFieldValues;
  private Date createdAt;
  private Date updatedAt;
  private String deviceId;
  private Integer callbackDuration;
  private String ivrNumber;
  private IdName callDisposition;
  private List<IdName> customerEmotion;
  private String overallSentiment;
  private String callSummary;
  @JsonIgnore
  private Map<EntityType, List<EntityDetail>> associatedEntities;
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;


  public CallLogDetail(
     @JsonProperty("id") Long id,
     @JsonProperty("tenantId") Long tenantId,
     @JsonProperty("outcome") String outcome,
     @JsonProperty("startTime") Date startTime,
     @JsonProperty("phoneNumber") String phoneNumber,
     @JsonProperty("callType") String callType,
     @JsonProperty("duration") Integer duration,
     @JsonProperty("isManual") Boolean isManual,
     @JsonProperty("originator") String originator,
     @JsonProperty("receiver") String receiver,
     @JsonProperty("createdBy") IdName createdBy,
     @JsonProperty("updatedBy") IdName updatedBy,
     @JsonProperty("owner") IdName owner,
     @JsonProperty("relatedTo") RelatedTo[] relatedTo,
     @JsonProperty("callRecording") CallRecording callRecording,
     @JsonProperty("createdAt") Date createdAt,
     @JsonProperty("updatedAt") Date updatedAt,
     @JsonProperty("deviceId") String deviceId,
     @JsonProperty("callbackDuration") Integer callbackDuration,
     @JsonProperty("customFieldValues") Map<String, Object> customFieldValues,
     @JsonProperty("ivrNumber") String ivrNumber,
     @JsonProperty("callDisposition") IdName callDisposition,
     @JsonProperty("customerEmotion") List<IdName> customerEmotion,
     @JsonProperty("overallSentiment") String overallSentiment,
     @JsonProperty("callSummary") String callSummary
  ) {
    this.id = id;
    this.tenantId = tenantId;
    this.outcome = OutcomeType.valueOf(outcome.toUpperCase());
    this.startTime = startTime;
    this.phoneNumber = phoneNumber;
    this.callType = CallType.valueOf(callType.toUpperCase());
    this.duration = duration;
    this.isManual = isManual;
    this.originator = originator;
    this.receiver = receiver;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.owner = owner;
    this.relatedTo = relatedTo;
    this.callRecording = callRecording;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.deviceId = deviceId;
    this.callbackDuration = callbackDuration;
    this.customFieldValues = customFieldValues;
    this.ivrNumber = ivrNumber;
    this.callDisposition = callDisposition;
    this.customerEmotion = customerEmotion;
    this.overallSentiment = overallSentiment;
    this.callSummary = callSummary;
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public IdName getOwner() {
    return this.owner;
  }

  @Override
  @JsonIgnore
  public EntityType getEntityType() {
    return EntityType.CALL_LOG;
  }

  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    this.associatedEntities = associatedEntities;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return owner;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return null;
  }

  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.callLog.update";
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    return this;
  }

  public Map<String, List<Long>> getRelatedToByEntityGroup(RelatedTo[] relatedTo) {
    return Arrays.stream(relatedTo)
        .collect(Collectors.groupingBy(relatedTo1 -> relatedTo1.getEntityType(), Collectors.mapping(relatedTo1 -> relatedTo1.id,Collectors.toList())));
  }

  public enum CallType {
    INCOMING, OUTGOING;

    @JsonValue
    public String toLowerCase() {
      return toString().toLowerCase();
    }
  }

  public enum OutcomeType {
    CONNECTED, REJECTED, BUSY, NO_ANSWER, MISSED_CALL, IN_PROGRESS;

    @JsonValue
    public String toLowerCase() {
      return toString().toLowerCase();
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  static class RelatedTo {
    private final Long id;
    private final EntityType entityType;
    private final String name;

    RelatedTo(
        @JsonProperty("id") Long id,
        @JsonProperty("entity") String entityType,
        @JsonProperty("name") String name) {
      this.id = id;
      this.entityType = EntityType.valueOf(entityType.toUpperCase());
      this.name = name;
    }

    @JsonGetter("entity")
    public String getEntityType(){
      return this.entityType.name();
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CallRecording {
    private final Long id;
    private final String fileName;
    private final Long fileSize;
    private final String url;

    @JsonCreator
    public CallRecording(
        @JsonProperty("id") Long id,
        @JsonProperty("fileName") String fileName,
        @JsonProperty("fileSize") Long fileSize,
        @JsonProperty("url") String url) {
      this.id = id;
      this.fileName = fileName;
      this.fileSize = fileSize;
      this.url = url;
    }
  }

  public List<Object> getRelatedToIds(RelatedTo[] relatedTo) {
    return Arrays.stream(relatedTo)
        .map(RelatedTo::getId)
        .collect(Collectors.toList());
  }

  public List<Object> getCustomerEmotions(List<IdName> customerEmotion) {
    return customerEmotion.stream()
        .map(IdName::getName)
        .collect(Collectors.toList());
  }

}
