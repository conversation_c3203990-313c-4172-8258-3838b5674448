package com.kylas.sales.workflow.domain.workflow.action.marketPlace;


import com.kylas.sales.workflow.config.WebConfig;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Service
@Slf4j
public class MarketplaceServiceImpl {

  private final String clientBasePath;

  @Autowired
  public MarketplaceServiceImpl(@Value("${client.marketplace.basePath}") String clientBasePath) {
    this.clientBasePath=clientBasePath;
  }


  public Mono<MarketPlaceTriggerDto> fetchTriggers(UUID triggerId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder ->
            uriBuilder.path("/v1/marketplace/triggers/" + triggerId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(MarketPlaceTriggerDto.class);
  }

}
