package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import static java.util.Collections.emptyMap;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.springframework.http.HttpMethod.GET;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.api.WorkflowService;
import com.kylas.sales.workflow.config.WebClientBuilderFactory;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.MarketPlaceTriggerDetailRepository;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.action.webhook.parameter.ParamKey;
import com.kylas.sales.workflow.domain.workflow.action.webhook.parameter.ParameterBuilder;
import com.kylas.sales.workflow.domain.workflow.executionLogs.MarketplaceLogPayload;
import com.kylas.sales.workflow.mq.event.MarketplaceAppActionEvent;
import com.kylas.sales.workflow.mq.event.MarketplaceDisableUninstalledAppActionEvent;
import com.kylas.sales.workflow.mq.event.MarketplaceInstalledAppEvent;
import com.kylas.sales.workflow.security.AuthService;
import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ClientHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.util.function.Tuples;


@Service
@Slf4j
public class MarketplaceService {

  private final AuthService authService;
  private final WebClientBuilderFactory webClientBuilderFactory;
  private final List<ParameterBuilder> parameterBuilders;
  private final WorkflowService workflowService;
  private final ExecutionLogFacade executionLogFacade;
  private final ObjectMapper objectMapper;

  @Autowired
  public MarketplaceService(AuthService authService, List<ParameterBuilder> parameterBuilders, WorkflowService workflowService,
      WebClientBuilderFactory webClientBuilderFactory, MarketPlaceTriggerDetailRepository marketPlaceTriggerRepository,
      ExecutionLogFacade executionLogFacade, ObjectMapper objectMapper) {
    this.authService = authService;
    this.webClientBuilderFactory = webClientBuilderFactory;
    this.parameterBuilders = parameterBuilders;
    this.workflowService = workflowService;
    this.executionLogFacade = executionLogFacade;
    this.objectMapper = objectMapper;
  }

  private final List<String> attributesList = List.of("phoneNumbers", "emails", "associatedContacts", "companyEmployees", "products");

  public void execute(MarketplaceAction marketplaceAction, EntityDetail entity, EntityType entityType) {
    log.info("Executing marketplace action with name {} & Id {}", marketplaceAction.getName(), marketplaceAction.getId());
    log.info("====marketplace action=entity:{}=====", entity);
    String authToken = authService.getAuthenticationToken();
    var requestParameters = parameterBuilders.stream()
        .filter(parameterBuilder -> parameterBuilder.canBuild(entityType))
        .findFirst()
        .map(parameterBuilder -> parameterBuilder.build(marketplaceAction, entity, authToken))
        .orElse(null);

    var uri = UriComponentsBuilder
        .fromUriString(marketplaceAction.getRequestUrl())
        .queryParams(buildQueryParams(marketplaceAction, requestParameters))
        .build()
        .toUri();
    log.info("Prepared uri is {}", uri);

    Map<String, Object> requestBodyParamsMap = getRequestBodyMap(marketplaceAction.getMethod(), requestParameters);

    ExecutionLog executionLog = createExecutionLog(marketplaceAction, uri, requestBodyParamsMap, entity);
    webClientBuilderFactory.getWebClientBuilder()
        .build()
        .method(marketplaceAction.getMethod())
        .uri(uri)
        .body(buildRequestBody(marketplaceAction.getMethod(), requestBodyParamsMap))
        .exchange()
        .flatMap(clientResponse -> {
          return clientResponse.bodyToMono(String.class)
              .defaultIfEmpty("")
              .map(body -> Tuples.of(clientResponse.statusCode(), body));
        })
        .doOnError(throwable -> {
          log.error("Error in executing marketplace action, error: {}", throwable.getMessage(), throwable);
          if (executionLog != null) {
            executionLogFacade.markAsFailed(executionLog.getId(), 500, throwable.getMessage());
          }
        })
        .subscribe(tuples -> {
          HttpStatus statusCode = tuples.getT1();
          String responseBody = tuples.getT2();
          log.debug("Received marketplace action response {}", responseBody);
          if (executionLog != null) {
            if(!statusCode.is2xxSuccessful()) {
              log.error("Error in execution marketplace action, actionId {} errorCode {}", marketplaceAction.getId(),statusCode.value());
              executionLogFacade.markAsFailed(executionLog.getId(), statusCode.value(), responseBody);
            } else {
              executionLogFacade.markAsSuccess(executionLog.getId(), statusCode.value());
            }
          }
        });

    log.info("Executed marketplace action with name {} & Id {}", marketplaceAction.getName(), marketplaceAction.getId());
  }

  private BodyInserter<?, ? super ClientHttpRequest> buildRequestBody(HttpMethod method, Map<String, Object> parameters) {
    if (method.equals(GET)) {
      return BodyInserters.empty();
    }
    return BodyInserters.fromValue(parameters);
  }

  private Map<String, Object> getRequestBodyMap(HttpMethod method, Map<ParamKey, List<Object>> parameters) {
    if (method.equals(GET)) {
      return Collections.emptyMap();
    }
    return parameters.entrySet().stream()
        .filter(entry -> isNotEmpty(entry.getValue()))
        .collect(toMap(entry -> entry.getKey().getName(),
            entry -> !attributesList.contains(entry.getKey().getAttributeName()) && entry.getValue().size() == 1 ? entry.getValue().get(0)
                : entry.getValue()));
  }

  private LinkedMultiValueMap<String, String> buildQueryParams(MarketplaceAction marketplaceAction, Map<ParamKey, List<Object>> requestParameters) {

    if (requestParameters == null) {
      return new LinkedMultiValueMap<>(emptyMap());
    }

    Map<String, List<String>> localRequestParameters = requestParameters.entrySet().stream()
        .collect(Collectors
            .toMap(param -> param.getKey().getName(), param -> param.getValue().stream().map(String::valueOf).collect(Collectors.toList())));

    return new LinkedMultiValueMap<>(
        marketplaceAction.getMethod().equals(GET) ? localRequestParameters : emptyMap());
  }

  public void processEnableActions(MarketplaceAppActionEvent marketplaceAppActionEvent) {
    workflowService.activeMarketplaceAction(marketplaceAppActionEvent.getAppActionId(),
        marketplaceAppActionEvent.getAppId(), marketplaceAppActionEvent.getTenantId());
  }
  public void processDisableActions(MarketplaceAppActionEvent marketplaceAppActionEvent) {
    workflowService.inactiveMarketplaceAction(marketplaceAppActionEvent.getAppActionId(),
        marketplaceAppActionEvent.getAppId(), marketplaceAppActionEvent.getTenantId());
  }

  public void processActionsForDisableUninstalledApp(MarketplaceDisableUninstalledAppActionEvent marketplaceAppActionEvent) {
    workflowService.disableOrUninstalledApp(marketplaceAppActionEvent.getApplications());
  }

  public void processActionsForInstalledApp(MarketplaceInstalledAppEvent marketplaceAppActionEvent) {
    workflowService.installedApp(marketplaceAppActionEvent.getAppId(),marketplaceAppActionEvent.getTenantId());
  }

  private ExecutionLog createExecutionLog(MarketplaceAction marketplaceAction, URI uri,
      Map<String, Object> requestBodyParamsMap, EntityDetail entity) {
    try {
      MarketplaceLogPayload logPayload = new MarketplaceLogPayload(marketplaceAction.getName(),
          marketplaceAction.getDescription(), uri.toString(), marketplaceAction.getMethod(), requestBodyParamsMap);
      Map actionPayload = objectMapper.convertValue(logPayload, Map.class);
      return executionLogFacade.createExecutionLog(marketplaceAction, entity, actionPayload);
    } catch (Exception e) {
      log.error("Error in creating execution log payload for marketplace action, actionId: {}, message: {}",
          marketplaceAction.getId(), e.getMessage(), e);
    }
    return null;
  }

}
