package com.kylas.sales.workflow.domain.workflow.delayed.action;

import static java.util.Arrays.asList;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.exception.InvalidWorkflowPropertyException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.error.ErrorCode;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.io.Serializable;
import java.util.List;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Entity
@Getter
@Setter
@NoArgsConstructor
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@Slf4j
public class Schedule implements Serializable {

  static List<String> PAST_DATE_FIELDS =  asList("createdAt", "updatedAt", "actualClosureDate", "convertedAt", "latestActivityOn");

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne
  @JoinColumn(name = "workflow_id")
  private Workflow workflow;

  @Embedded
  @AttributeOverrides(
          value = {
                  @AttributeOverride(name = "trigger", column = @Column(name = "perform_trigger")),
                  @AttributeOverride(name = "time", column = @Column(name = "perform_time")),
                  @AttributeOverride(name = "unit", column = @Column(name = "perform_unit"))
          })
  private Perform perform;

  @NotNull
  private String dateField;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private Timing atTime;

  @Enumerated(value = EnumType.STRING)
  @NotNull
  private Frequency repeat;

  @Embedded
  @AttributeOverrides(
          value = {
                  @AttributeOverride(name = "type", column = @Column(name = "recurrence_type")),
                  @AttributeOverride(name = "value", column = @Column(name = "recurrence_value"))
          })
  @NotNull
  private Recurrence recurrence;

  public Schedule(Perform perform, String dateField, Timing atTime, Frequency repeat, Recurrence recurrence) {
    this.perform = perform;
    this.dateField = dateField;
    this.atTime = atTime;
    this.repeat = repeat;
    this.recurrence = recurrence;
  }

  public Schedule(Long id, Perform perform, String dateField, Timing atTime, Frequency repeat, Recurrence recurrence, Workflow workflow) {
    this.id = id;
    this.perform = perform;
    this.dateField = dateField;
    this.atTime = atTime;
    this.repeat = repeat;
    this.recurrence = recurrence;
    this.workflow = workflow;
  }

  @Getter
  @ToString
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Timing implements Serializable {
    private final Integer hh;
    private final Integer mm;

    @JsonCreator
    public Timing(
          @JsonProperty("hh") Integer hh,
          @JsonProperty("mm")  Integer mm
    ) {
      this.hh = hh;
      this.mm = mm;
    }
  }

  public enum Frequency {
    NO_REPEAT, DAILY, WEEKLY, MONTHLY, YEARLY
  }

  public static Schedule createNew(com.kylas.sales.workflow.api.request.Schedule schedule){
    if(schedule == null) {
      return null;
    }
    Schedule newSchedule = new Schedule(schedule.getPerform(), schedule.getDateField(), schedule.getAt(), schedule.getRepeat(), schedule.getRecurrence());
    validateProperties(newSchedule);
    return newSchedule;
  }

  public Schedule update(com.kylas.sales.workflow.api.request.Schedule schedule){
    if(schedule == null){
      return null;
    }
    var updatedSchedule = new Schedule(this.id, schedule.getPerform(), schedule.getDateField(), schedule.getAt(), schedule.getRepeat(), schedule.getRecurrence(), this.workflow);
    validateProperties(updatedSchedule);
    return updatedSchedule;
  }

  private static void validateProperties(Schedule schedule){
     boolean invalidSchedule = schedule.getPerform().getTrigger().equals(Perform.TriggerType.BEFORE) &&
             PAST_DATE_FIELDS.contains(schedule.getDateField());

     invalidSchedule = invalidSchedule || (asList(Perform.TimeUnit.MINUTES, Perform.TimeUnit.HOURS).contains(schedule.getPerform().getUnit())
             && schedule.getAtTime() != null);

     if(invalidSchedule){
       log.error("Invalid schedule {}", schedule);
       throw new InvalidWorkflowPropertyException(ErrorCode.INVALID_WORKFLOW_SCHEDULE);
     }

  }

  public boolean isRecurring(){
    return !this.getRepeat().equals(Frequency.NO_REPEAT);
  }
}
