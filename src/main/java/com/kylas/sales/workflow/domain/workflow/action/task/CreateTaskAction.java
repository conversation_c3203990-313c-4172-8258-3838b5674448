package com.kylas.sales.workflow.domain.workflow.action.task;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.CREATE_TASK;
import static org.apache.commons.lang3.StringUtils.isBlank;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionDetail.CreateTaskAction.ReminderType;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.CustomFieldJsonUserType;
import com.kylas.sales.workflow.domain.exception.InvalidWorkflowRequestException;
import com.kylas.sales.workflow.domain.processor.task.AssignedToType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import java.util.Map;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.springframework.stereotype.Component;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
@TypeDef(name = "CustomFieldJsonUserType", typeClass = CustomFieldJsonUserType.class)
public class CreateTaskAction extends AbstractWorkflowAction implements WorkflowAction {

  @NotBlank
  private String name;
  private String description;
  private Long priority;
  @Enumerated(EnumType.STRING)
  private ReminderType reminder;
  @Column(name = "type")
  private Long taskType;
  private long status;
  @Type(type = "CustomFieldJsonUserType")
  @Column(columnDefinition = "jsonb")
  private Map<String,Object> customFields;

  @Embedded
  @NotNull
  @Valid
  @AttributeOverrides(
      value = {
          @AttributeOverride(
              name = "type",
              column = @Column(name = "assigned_to_type")),
          @AttributeOverride(name = "id", column = @Column(name = "assigned_to_id")),
          @AttributeOverride(name = "name", column = @Column(name = "assigned_to_name"))
      })
  private AssignedTo assignedTo;

  @Embedded
  @NotNull
  @Valid
  @AttributeOverrides(
      value = {
          @AttributeOverride(
              name = "days",
              column = @Column(name = "due_days")),
          @AttributeOverride(name = "hours", column = @Column(name = "due_hours"))
      })
  private DueDate dueDate;

  public CreateTaskAction(@NotBlank String name, String description, Long priority, ReminderType reminder, Long taskType, long status,
      AssignedTo assignedTo,
      @NotNull DueDate dueDate, Map<String, Object> customFieldValues) {
    this.name = name;
    this.description = description;
    this.priority = priority;
    this.reminder = reminder;
    this.taskType = taskType;
    this.status = status;
    this.assignedTo = assignedTo;
    this.dueDate = dueDate;
    this.customFields =customFieldValues;
  }

  public static AbstractWorkflowAction createNew(ActionResponse action) {
    return CreateTaskActionMapper.fromActionResponse(action);
  }

  public static ActionResponse toActionResponse(CreateTaskAction workflowAction) {
    return new ActionResponse(workflowAction.getId(), workflowAction.getType(), CreateTaskActionMapper.fromWorkflowAction(workflowAction));
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse action) {
    var payload = (ActionDetail.CreateTaskAction) action.getPayload();
    CreateTaskActionMapper.validate(payload);
    this.setName(payload.getName());
    this.setDescription(payload.getDescription());
    this.setPriority(payload.getPriority());
    this.setReminder(payload.getReminder());
    this.setTaskType(payload.getType());
    this.setStatus(payload.getStatus());
    this.setAssignedTo(payload.getAssignedTo());
    this.setDueDate(payload.getDueDate());
    this.setCustomFields(payload.getCustomFieldValues());
    return this;
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public ActionType getType() {
    return CREATE_TASK;
  }

  @Component
  private static class CreateTaskActionMapper {

    private static CreateTaskAction fromActionResponse(ActionResponse actionResponse) {
      ActionDetail.CreateTaskAction payload = (ActionDetail.CreateTaskAction) actionResponse.getPayload();
      validate(payload);
      return new CreateTaskAction(payload.getName(), payload.getDescription(), payload.getPriority(),
          payload.getReminder(), payload.getType(), payload.getStatus(), payload.getAssignedTo(), payload.getDueDate(),payload.getCustomFieldValues() );
    }

    private static ActionDetail.CreateTaskAction fromWorkflowAction(CreateTaskAction workflowAction) {

      return new ActionDetail.CreateTaskAction(workflowAction.getName(), workflowAction.getDescription(), workflowAction.getPriority(),
          workflowAction.getReminder(), workflowAction.getTaskType(), workflowAction.getStatus(), workflowAction.getAssignedTo(),
          workflowAction.getDueDate(),workflowAction.getCustomFields());
    }

    private static void validate(ActionDetail.CreateTaskAction payload) {
      if (isBlank(payload.getName()) || isAllNotNull(payload) || isAssignedToTypeOrIdNull(payload.getAssignedTo())) {
        throw new InvalidWorkflowRequestException();
      }
    }

    private static boolean isAllNotNull(ActionDetail.CreateTaskAction payload) {
      return !ObjectUtils.allNotNull(payload.getStatus(), payload.getAssignedTo(), payload.getDueDate());
    }

    private static boolean isAssignedToTypeOrIdNull(AssignedTo assignedTo) {
      return assignedTo.getType() == null || (assignedTo.getType().equals(AssignedToType.USER) && assignedTo.getId() == null);
    }
  }
}
