package com.kylas.sales.workflow.domain.processor;

import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Organizer;
import java.util.List;
import lombok.Getter;

@Getter
public class EmailActionDetail {

  private final String name;
  private final IdName createdBy;
  private final IdName updatedBy;
  private final IdName ownedBy;
  private final IdName assigneeTo;
  private final Email[] emails;
  private final List<IdName> associatedContacts;
  private Organizer organizer;


  public EmailActionDetail(String name, IdName createdBy, IdName updatedBy, IdName ownedBy, IdName assigneeTo, Email[] emails,
      List<IdName> associatedContacts, Organizer organizer) {
    this.name = name;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.ownedBy = ownedBy;
    this.assigneeTo = assigneeTo;
    this.emails = emails;
    this.associatedContacts = associatedContacts;
    this.organizer = organizer;
  }
}
