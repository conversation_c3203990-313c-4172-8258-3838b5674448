package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.service.client.JsonRule;
import com.kylas.sales.workflow.domain.service.client.SearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service("PRODUCTIVITY")
@Slf4j
public class ProductivityService implements Searchable{
  private final String clientBasePath;

  @Autowired
  public ProductivityService(@Value("${client.productivity.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  @Override
  public Mono<SearchResponse> getEntityDetails(String entity, Long id, String authenticationToken, Class entityClass) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .post()
        .uri(uriBuilder -> uriBuilder.path("/v1/tasks/search")
            .queryParam("sort", "updatedAt,desc")
            .queryParam("page", 0)
            .queryParam("size", 1)
            .build()
        )
        .bodyValue(JsonRule.getTaskFilterForId(id))
        .accept(MediaType.APPLICATION_JSON)
        .retrieve().bodyToMono(entityClass);
  }
}
