package com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.SendWhatsappMessageActionRepository;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.processor.deal.DealDetail;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.mq.event.ContactEvent;
import com.kylas.sales.workflow.mq.event.DealEvent;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.LeadEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import com.kylas.sales.workflow.mq.event.WhatsappMessageActionEvent;
import com.kylas.sales.workflow.mq.event.WhatsappMessageActionEvent.MessageDetail;
import com.kylas.sales.workflow.mq.event.WhatsappMessageActionEvent.WhatsappMessageType;
import com.kylas.sales.workflow.mq.event.WhatsappMessageActionEventPublisher;
import java.util.HashMap;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SendWhatsappMessageService {

  private final WhatsappMessageActionEventPublisher whatsappMessageActionEventPublisher;
  private final SendWhatsappMessageActionRepository sendWhatsappMessageActionRepository;
  private final ExecutionLogFacade executionLogFacade;
  private final ObjectMapper objectMapper;

  @Autowired
  public SendWhatsappMessageService(WhatsappMessageActionEventPublisher whatsappMessageActionEventPublisher,
      SendWhatsappMessageActionRepository sendWhatsappMessageActionRepository, ExecutionLogFacade executionLogFacade, ObjectMapper objectMapper) {
    this.whatsappMessageActionEventPublisher = whatsappMessageActionEventPublisher;
    this.sendWhatsappMessageActionRepository = sendWhatsappMessageActionRepository;
    this.executionLogFacade = executionLogFacade;
    this.objectMapper = objectMapper;
  }

  public void processSendWhatsappAction(SendWhatsappMessageAction sendWhatsappMessageAction, EntityEvent event, Metadata metadata) {
    log.info("Send whatsappAction for entityType {} entityId {} action {} workflowId {} and actionId {}", event.getEntity(), event.getEntityId(),
        sendWhatsappMessageAction.getType(), sendWhatsappMessageAction.getWorkflow().getId(), sendWhatsappMessageAction.getId());

    Set<SendWhatsappMessageType> sendWhatsappMessageTypes = sendWhatsappMessageAction.getTo();
    if (ObjectUtils.isEmpty(sendWhatsappMessageTypes) || ObjectUtils.isEmpty(sendWhatsappMessageTypes)) {
      log.info("No send type specified for Send whatsappAction for entityType {} entityId {} action {} workflowId {} and actionId {}",
          event.getEntity(), event.getEntityId(), sendWhatsappMessageAction.getType(), sendWhatsappMessageAction.getWorkflow().getId(),
          sendWhatsappMessageAction.getId());
      return;
    }
    Set<WhatsappMessageType> whatsappMessageTypes = sendWhatsappMessageTypes.stream()
        .map(whatsappMessageType -> new WhatsappMessageType(whatsappMessageType.getName(), whatsappMessageType.getType()))
        .collect(Collectors.toSet());
    MessageDetail messageDetail = new MessageDetail(sendWhatsappMessageAction.getTemplate().getId(), whatsappMessageTypes);

    if (event instanceof LeadEvent) {
      LeadDetail leadDetail = (LeadDetail) event.getEntity();

      if (leadDetail != null && ObjectUtils.isNotEmpty(leadDetail.getPhoneNumbers())) {
        WhatsappMessageActionEvent whatsappMessageActionEvent = new WhatsappMessageActionEvent(metadata, leadDetail, messageDetail);

        log.info("Publishing send WhatsApp message action event for entityId {} and entityName {} with workflowId {}", leadDetail.getId(),
            leadDetail.getName(), sendWhatsappMessageAction.getWorkflow().getId());
        ExecutionLog executionLog = executionLogFacade.createExecutionLog(sendWhatsappMessageAction, leadDetail,
            getActionPayload(sendWhatsappMessageAction.getConnectedAccount(), sendWhatsappMessageAction.getTemplate()));
        whatsappMessageActionEvent.getMetadata().setEventId(executionLog.getId());
        whatsappMessageActionEventPublisher.publishSendWhatsappMessageActionEvent(whatsappMessageActionEvent);
        return;

      }

      if (leadDetail != null && ObjectUtils.isEmpty(leadDetail.getPhoneNumbers())) {

        log.info("No phones present for Send whatsappAction for entityType {} entityId {} action {} workflowId {} and actionId {}", event.getEntity(),
            event.getEntityId(), sendWhatsappMessageAction.getType(), sendWhatsappMessageAction.getWorkflow().getId(),
            sendWhatsappMessageAction.getId());
        return;
      }
    }

    if (event instanceof ContactEvent) {
      ContactDetail contactDetail = (ContactDetail) event.getEntity();

      if (contactDetail != null && ObjectUtils.isNotEmpty(contactDetail.getPhoneNumbers())) {
        WhatsappMessageActionEvent whatsappMessageActionEvent = new WhatsappMessageActionEvent(metadata, contactDetail, messageDetail);

        log.info("Publishing send WhatsApp message action event for entityId {} and entityName {} with workflowId {}", contactDetail.getId(),
            contactDetail.getName(), sendWhatsappMessageAction.getWorkflow().getId());
        ExecutionLog executionLog = executionLogFacade.createExecutionLog(sendWhatsappMessageAction, contactDetail,
            getActionPayload(sendWhatsappMessageAction.getConnectedAccount(), sendWhatsappMessageAction.getTemplate()));
        whatsappMessageActionEvent.getMetadata().setEventId(executionLog.getId());
        whatsappMessageActionEventPublisher.publishSendWhatsappMessageActionEvent(whatsappMessageActionEvent);
      }

      if (contactDetail != null && ObjectUtils.isEmpty(contactDetail.getPhoneNumbers())) {

        log.info("No phones present for Send whatsappAction for entityType {} entityId {} action {} workflowId {} and actionId {}", event.getEntity(),
            event.getEntityId(), sendWhatsappMessageAction.getType(), sendWhatsappMessageAction.getWorkflow().getId(),
            sendWhatsappMessageAction.getId());
      }

    }

    if (event instanceof DealEvent) {
      DealDetail dealDetail = (DealDetail) event.getEntity();

      if (dealDetail != null && ObjectUtils.isNotEmpty(dealDetail.getAssociatedContacts())) {
        WhatsappMessageActionEvent whatsappMessageActionEvent = new WhatsappMessageActionEvent(metadata, dealDetail, messageDetail);

        log.info("Publishing send WhatsApp message action event for entityId {} and entityName {} with workflowId {}", dealDetail.getId(),
            dealDetail.getName(), sendWhatsappMessageAction.getWorkflow().getId());
        ExecutionLog executionLog = executionLogFacade.createExecutionLog(sendWhatsappMessageAction, dealDetail,
            getActionPayload(sendWhatsappMessageAction.getConnectedAccount(), sendWhatsappMessageAction.getTemplate()));
        whatsappMessageActionEvent.getMetadata().setEventId(executionLog.getId());
        whatsappMessageActionEventPublisher.publishSendWhatsappMessageActionEvent(whatsappMessageActionEvent);
        return;

      }

      if (dealDetail != null && ObjectUtils.isEmpty(dealDetail.getAssociatedContacts())) {
        log.info("No contacts present on deal for Send whatsappAction for entityId {} action {} workflowId {} and actionId {}",
            event.getEntityId(), sendWhatsappMessageAction.getType(), sendWhatsappMessageAction.getWorkflow().getId(),
            sendWhatsappMessageAction.getId());
        return;
      }
    }
  }

  private HashMap<String, Object> getActionPayload(ConnectedAccount connectedAccount, Template template) {
    HashMap<String, Object> allValues = new HashMap<>();
    HashMap<String, Object> connectedAccountMap = objectMapper.convertValue(connectedAccount, HashMap.class);
    allValues.put("connectedAccount", connectedAccountMap);
    HashMap<String, Object> templateMap = objectMapper.convertValue(template, HashMap.class);
    allValues.put("whatsappTemplate", templateMap);
    return allValues;
  }

  public void updateTemplateName(Long id, String name) {
    sendWhatsappMessageActionRepository.updateTemplateNameByTemplateId(id, name);
  }
}
