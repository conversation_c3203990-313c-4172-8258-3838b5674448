package com.kylas.sales.workflow.domain.service.client;

import static java.util.Arrays.asList;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import lombok.Getter;
import lombok.ToString;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class JsonRule {

  private final ArrayList<Rule> rules = new ArrayList<>();
  private final String condition;
  private final boolean valid;

  @JsonCreator
  public JsonRule(@JsonProperty("rules") List<Rule> rules) {
    if (rules != null) {
      this.rules.addAll(rules);
    }
    condition = "AND";
    valid = true;
  }

  @Getter
  @ToString
  public static class Rule {

    private final String operator;
    private final String field;
    private final String type;
    private final Object value;
    private final String id;

    @Json<PERSON>reator
    public Rule(
        @JsonProperty("operator") String operator,
        @JsonProperty("field") String field,
        @JsonProperty("type") String fieldType,
        @JsonProperty("value") Object value) {
      this.operator = operator;
      this.field = field;
      this.type = fieldType;
      this.value = value;
      this.id = field;
    }
  }

  public static HashMap<String, Object> getFilterForId(long id){
    JsonRule jsonRule = new JsonRule(asList(new Rule("equal", "id", "double", id)));
    return new HashMap<>() {{
      put("fields", null);
      put("jsonRule", jsonRule);
    }};
  }

  public static HashMap<String, Object> getTaskFilterForId(long id) {
    JsonRule jsonRule = new JsonRule(asList(new Rule("equal", "id", "double", id)));
    return new HashMap<>() {{
      put("fields", Collections.emptyList());
      put("jsonRule", jsonRule);
    }};
  }

  public static HashMap<String, Object> getFilterForIds(List<Long> ids) {
    JsonRule jsonRule = new JsonRule((asList(new Rule("in","id","double", ids))));
    return new HashMap<>() {{
      put("fields", Collections.emptyList());
      put("jsonRule", jsonRule);
    }};
  }

}