package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class PipelineService {

  private final String clientBasePath;

  @Autowired
  public PipelineService(@Value("${client.search.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Mono<IdName> getPipeline(Long pipelineId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/pipeline").queryParam("id", pipelineId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(IdName.class);
  }

  public Mono<List<IdName>> getPipelinesSummaries(List<Long> pipelineIds, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/pipelines").queryParam("id", pipelineIds).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class)
        .collectList();
  }

  public Mono<IdName> getPipelineStage(Long pipelineStageId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/pipeline-stage").queryParam("id", pipelineStageId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(IdName.class);
  }

  public Mono<List<IdName>> getPipelineStagesSummaries(List<Long> pipelineStageIds, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/pipeline-stages").queryParam("id", pipelineStageIds).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class)
        .collectList();
  }
}
