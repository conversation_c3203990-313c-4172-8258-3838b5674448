package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CompanyAttribute.ANNUAL_REVENUE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.ACTUAL_VALUE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.ESTIMATED_VALUE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.LeadAttribute.COMPANY_PHONES;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.LeadAttribute.EMAILS;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.LeadAttribute.PHONE_NUMBERS;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.LeadAttribute.REQUIREMENT_PRODUCTS;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.DEAL;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.common.dto.Tenant;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail.CallRecording;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.processor.deal.Money;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.processor.lead.Product;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.client.CompanyResponse;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CallLogAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CompanyAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.ContactAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MarketplaceEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.UserAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class ParameterBuilder {

  private final UserService userService;
  private final ConfigService configService;

  protected ParameterBuilder(UserService userService,ConfigService configService) {
    this.userService = userService;
    this.configService=configService;
  }
  final Mono<User> getUserIfRequired(WebhookAction action, String authToken, WebhookEntity entity, IdName user) {
    return
        action.getParameters().stream().anyMatch(parameter -> parameter.getEntity().equals(entity))
            ? userService.getUserDetails(user.getId(), authToken)
            : Mono.just(new User());
  }

  final Mono<User> getUserIfRequired(MarketplaceAction action, String authToken, MarketplaceEntity entity, IdName user) {
    return
        action.getParameters().stream().anyMatch(parameter -> parameter.getEntity().equals(entity))
            ? userService.getUserDetails(user.getId(), authToken)
            : Mono.just(new User());
  }

  final Mono<Tenant> getTenantIfRequired(WebhookAction action, String token) {
    return
        action.getParameters().stream().anyMatch(parameter -> parameter.getEntity().equals(TENANT))
            ? userService.getTenantDetails(token)
            : Mono.just(new Tenant());
  }

  final Mono<Tenant> getTenantIfRequired(MarketplaceAction action, String token) {
    return
        action.getParameters().stream().anyMatch(parameter -> parameter.getEntity().equals(MarketplaceEntity.TENANT))
            ? userService.getTenantDetails(token)
            : Mono.just(new Tenant());
  }

  final Flux<IdName> getCurrencyIfRequired(Set<Long> currencyIds, String token) {
    if (ObjectUtils.isEmpty(currencyIds)) {
      return Flux.just(new IdName(null, null));
    }
    return configService.getCurrency(new ArrayList<>(currencyIds), token);
  }

  final Mono<IdName> getCurrencyIfRequired(WebhookAction action, String token, Money money) {
    if (isNull(money) || isNull(money.getCurrencyId())) {
      return Mono.just(new IdName(null,null));
    }
    Flux<IdName> currencyFlux = action.getParameters().stream().anyMatch(
        parameter -> (parameter.getEntity().equals(DEAL) || parameter.getEntity().equals(WebhookEntity.ASSOCIATED_COMPANY))
            && (parameter.getAttribute().equals(ACTUAL_VALUE.getName()) || parameter.getAttribute().equals(ESTIMATED_VALUE.getName()) || parameter.getAttribute().equals(ANNUAL_REVENUE.getName())))
        ? configService.getCurrency(List.of(money.getCurrencyId()), token)
        : Flux.just(new IdName(null,null));
    return currencyFlux.next();
  }

  final Mono<IdName> getCurrencyIfRequired(MarketplaceAction action, String token, Money money) {
    if (isNull(money) || isNull(money.getCurrencyId())) {
      return Mono.just(new IdName(null,null));
    }
    Flux<IdName> currencyFlux = action.getParameters().stream().anyMatch(
        parameter -> parameter.getEntity().equals(MarketplaceEntity.DEAL)
            && (parameter.getAttribute().equals(ACTUAL_VALUE.getName()) || parameter.getAttribute().equals(ESTIMATED_VALUE.getName())))
        ? configService.getCurrency(List.of(money.getCurrencyId()), token)
        : Flux.just(new IdName(null,null));
    return currencyFlux.next();
  }

  final List<Object> getParameterValue(Parameter parameter, Object entity, String jwtToken) {
    EntityType type = parameter.getEntity().getType();
    String attribute = parameter.getAttribute();

    switch (type) {
      case CUSTOM:
        return List.of(parameter.getAttribute());

      case LEAD:
        if (attribute.equalsIgnoreCase(EMAILS.getName())) {
          return isNull(((LeadDetail) entity).getEmails()) ? emptyList()
              : stream(((LeadDetail) entity).getEmails())
                  .map(Email::getValue)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(PHONE_NUMBERS.getName())) {
          return isNull(((LeadDetail) entity).getPhoneNumbers()) ? emptyList()
              : stream(((LeadDetail) entity).getPhoneNumbers())
                  .map(this::buildPhoneNumber)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(COMPANY_PHONES.getName())) {
          return isNull(((LeadDetail) entity).getCompanyPhones()) ? emptyList()
              : stream(((LeadDetail) entity).getCompanyPhones())
                  .map(this::buildPhoneNumber)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(REQUIREMENT_PRODUCTS.getName())) {
          return isNull(((LeadDetail) entity).getProducts()) ? emptyList()
              : ((LeadDetail) entity).getProducts().stream()
                  .map(Product::getName)
                  .collect(toList());
        } else if(!parameter.isStandard() && entity instanceof EntityDetail && ((LeadDetail) entity).getCustomFieldValues() != null){
          if (((LeadDetail) entity).getCustomFieldValues().get(attribute) instanceof List){
            return actualValuesMultiValuePicklist(attribute,((LeadDetail) entity).getIdNameStore());
          }
        }
        break;

      case USER:
        if (attribute.equalsIgnoreCase(UserAttribute.PHONE_NUMBERS.getName())) {
          return isNull(((UserDetails) entity).getPhoneNumbers()) ? emptyList()
              : stream(((UserDetails) entity).getPhoneNumbers())
                  .map(this::buildPhoneNumber)
                  .collect(toList());
        }
        break;

      case CONTACT:
        if (attribute.equalsIgnoreCase(ContactAttribute.EMAILS.getName())) {
          return isNull(((ContactDetail) entity).getEmails()) ? emptyList()
              : stream(((ContactDetail) entity).getEmails())
                  .map(Email::getValue)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(ContactAttribute.PHONE_NUMBERS.getName())) {
          return isNull(((ContactDetail) entity).getPhoneNumbers()) ? emptyList()
              : stream(((ContactDetail) entity).getPhoneNumbers())
                  .map(this::buildPhoneNumber)
                  .collect(toList());
        } else if(!parameter.isStandard() && entity instanceof EntityDetail && ((ContactDetail) entity).getCustomFieldValues() != null){
          if (((ContactDetail) entity).getCustomFieldValues().get(attribute) instanceof List){
            return actualValuesMultiValuePicklist(attribute,((ContactDetail) entity).getIdNameStore());
          }
        }
        break;

      case CALL_LOG:
        if(attribute.equalsIgnoreCase(CallLogAttribute.CALL_RECORDING.getName())){
          CallRecording callRecording = ((CallLogDetail) entity).getCallRecording();
          return callRecording != null ? List.of(new ObjectMapper().convertValue(callRecording, Map.class)) : emptyList();
        }
        break;

      case MARKETPLACE_TRIGGER:
        Map<String, Object> marketPlaceTriggerValues = ((EntityDetail) entity).getMarketPlaceTriggerValues();
        if( marketPlaceTriggerValues.get(parameter.getAttribute()) instanceof List){
          List<Map<String, Object>> maps = (List<Map<String, Object>>) marketPlaceTriggerValues.get(parameter.getAttribute());
          List<Object> dialCode = maps.stream()
              .map(stringObjectMap -> stringObjectMap.get("dialCode"))
              .filter(Objects::nonNull)
              .collect(toList());
          if(ObjectUtils.isNotEmpty(dialCode)){
            return maps.stream().map(stringObjectMap -> stringObjectMap.get("dialCode").toString() +" " + stringObjectMap.get("value").toString())
                .collect(Collectors.toList());
          }
          return maps.stream().map(stringObjectMap -> stringObjectMap.get("value"))
              .collect(Collectors.toList());
        }
      Object object = marketPlaceTriggerValues.get(parameter.getAttribute());
      return object==null?List.of(""):Collections.singletonList(object);

      case COMPANY:
        if (attribute.equalsIgnoreCase(CompanyAttribute.EMAILS.getName())) {
          return ObjectUtils.isEmpty(((CompanyResponse) entity).getEmails()) ? emptyList()
              : ((CompanyResponse) entity).getEmails().stream()
                  .map(Email::getValue)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(CompanyAttribute.PHONE_NUMBERS.getName())) {
          return ObjectUtils.isEmpty(((CompanyResponse) entity).getPhoneNumbers()) ? emptyList()
              : ((CompanyResponse) entity).getPhoneNumbers().stream()
                  .map(this::buildPhoneNumber)
                  .collect(toList());
        }
        break;
    }
    var property = getPropertyValue(entity, parameter, jwtToken);
    return nonNull(property) ? List.of(property) : List.of("");
  }

  final List<Object> getParameterValue(MarketplaceActionParameter parameter, Object entity, String jwtToken) {
    EntityType type = parameter.getEntity().getType();
    String attribute = parameter.getAttribute();

    switch (type) {
      case CUSTOM:
        return List.of(parameter.getAttribute());

      case LEAD:
        if (attribute.equalsIgnoreCase(EMAILS.getName())) {
          return isNull(((LeadDetail) entity).getEmails()) ? emptyList()
              : stream(((LeadDetail) entity).getEmails())
                  .map(Email::getValue)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(PHONE_NUMBERS.getName())) {
          return isNull(((LeadDetail) entity).getPhoneNumbers()) ? emptyList()
              : stream(((LeadDetail) entity).getPhoneNumbers())
                  .map(this::getPhoneNumber)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(COMPANY_PHONES.getName())) {
          return isNull(((LeadDetail) entity).getCompanyPhones()) ? emptyList()
              : stream(((LeadDetail) entity).getCompanyPhones())
                  .map(this::getPhoneNumber)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(REQUIREMENT_PRODUCTS.getName())) {
          return isNull(((LeadDetail) entity).getProducts()) ? emptyList()
              : ((LeadDetail) entity).getProducts().stream()
                  .map(Product::getName)
                  .collect(toList());
        }
        else if(!parameter.isStandard() && entity instanceof EntityDetail && ((LeadDetail) entity).getCustomFieldValues() != null){
          if (((LeadDetail) entity).getCustomFieldValues().get(attribute) instanceof List){
            return actualValuesMultiValuePicklist(attribute,((LeadDetail) entity).getIdNameStore());
          }
        }
        break;

      case CONTACT:
        if (attribute.equalsIgnoreCase(ContactAttribute.EMAILS.getName())) {
          return isNull(((ContactDetail) entity).getEmails()) ? emptyList()
              : stream(((ContactDetail) entity).getEmails())
                  .map(Email::getValue)
                  .collect(toList());
        } else if (attribute.equalsIgnoreCase(ContactAttribute.PHONE_NUMBERS.getName())) {
          return isNull(((ContactDetail) entity).getPhoneNumbers()) ? emptyList()
              : stream(((ContactDetail) entity).getPhoneNumbers())
                  .map(this::getPhoneNumber)
                  .collect(toList());
        }
        else if(!parameter.isStandard() && entity instanceof EntityDetail && ((ContactDetail) entity).getCustomFieldValues() != null){
          if (((ContactDetail) entity).getCustomFieldValues().get(attribute) instanceof List){
            return actualValuesMultiValuePicklist(attribute,((ContactDetail) entity).getIdNameStore());
          }
        }
        break;

      case USER:
        if (attribute.equalsIgnoreCase(UserAttribute.PHONE_NUMBERS.getName())) {
          return isNull(((UserDetails) entity).getPhoneNumbers()) ? emptyList()
              : stream(((UserDetails) entity).getPhoneNumbers())
                  .map(this::getPhoneNumber)
                  .collect(toList());
        }
        break;

      case CALL_LOG:
        if(attribute.equalsIgnoreCase(CallLogAttribute.CALL_RECORDING.getName())){
          CallRecording callRecording = ((CallLogDetail) entity).getCallRecording();
          return callRecording != null ? List.of(new ObjectMapper().convertValue(callRecording, Map.class)) : emptyList();
        }
        break;

      case MARKETPLACE_TRIGGER:
        Map<String, Object> marketPlaceTriggerValues = ((EntityDetail) entity).getMarketPlaceTriggerValues();
        if( marketPlaceTriggerValues.get(parameter.getAttribute()) instanceof List){
          List<Map<String, Object>> maps = (List<Map<String, Object>>) marketPlaceTriggerValues.get(parameter.getAttribute());
          List<Object> dialCode = maps.stream()
              .map(stringObjectMap -> stringObjectMap.get("dialCode"))
              .filter(Objects::nonNull)
              .collect(toList());
          if(ObjectUtils.isNotEmpty(dialCode)){
            return (List<Object>) marketPlaceTriggerValues.get(parameter.getAttribute());
          }
          return maps.stream().map(stringObjectMap -> stringObjectMap.get("value"))
              .collect(Collectors.toList());
        }
        Object object = marketPlaceTriggerValues.get(parameter.getAttribute());
        return object==null?List.of(""):Collections.singletonList(object);
    }
    var property = getPropertyValue(entity, parameter, jwtToken);
    return nonNull(property) ? List.of(property) : List.of("");
  }

  private List<Object> actualValuesMultiValuePicklist(String attribute, Map<String, Map<Long, String>> idNameStore) {
    var listOfCustomValues = new ArrayList<>();
    if (idNameStore.containsKey(attribute)) {
      List<Object> customValues = idNameStore.get(attribute).values().stream().collect(toList());
      listOfCustomValues.add(customValues);
      return listOfCustomValues;
    }
    return Collections.emptyList();
  }

  protected abstract String getPropertyValue(Object entity, Parameter parameter, String jwtToken) ;

  protected abstract String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) ;

  private String buildPhoneNumber(PhoneNumber phoneNumber) {
    return String.format("%s %s", phoneNumber.getDialCode(), phoneNumber.getValue());
  }

  private PhoneNumber getPhoneNumber(PhoneNumber phoneNumber){
    return phoneNumber;
  }

  public abstract boolean canBuild(EntityType entityType);

  public abstract Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken);

  public abstract Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entity, String authToken);
}
