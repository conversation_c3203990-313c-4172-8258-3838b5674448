package com.kylas.sales.workflow.domain.workflow.action.convertLead;

import com.kylas.sales.workflow.domain.processor.deal.Money;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.LeadConversion;
import com.kylas.sales.workflow.domain.processor.lead.LeadConversion.ConversionMode;
import com.kylas.sales.workflow.domain.processor.lead.LeadConversion.ConversionRequest;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.processor.lead.Product;
import com.kylas.sales.workflow.domain.service.CompanyService;
import com.kylas.sales.workflow.domain.service.ConfigCacheService;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.DealService;
import com.kylas.sales.workflow.domain.service.ProductService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.client.ConversionMappingResponse;
import com.kylas.sales.workflow.domain.service.client.FieldMappingResponse;
import com.kylas.sales.workflow.domain.service.client.MappingResponse;
import com.kylas.sales.workflow.domain.service.client.ProductDetails;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.mq.command.EntityUpdatedCommandPublisher;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import com.kylas.sales.workflow.security.AuthService;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ConvertLeadService {

  private final EntityUpdatedCommandPublisher entityUpdatedCommandPublisher;
  private final AuthService authService;
  private final ConfigService configService;
  private final DealService dealService;
  private final ConversionConverter conversionConverter;
  private final UserService userService;
  private final ProductService productService;
  private final CompanyService companyService;
  private final ConfigCacheService configCacheService;

  @Autowired
  public ConvertLeadService(EntityUpdatedCommandPublisher entityUpdatedCommandPublisher, AuthService authService, ConfigService configService,
      DealService dealService, ConversionConverter conversionConverter, UserService userService, ProductService productService,
      CompanyService companyService, ConfigCacheService configCacheService) {
    this.entityUpdatedCommandPublisher = entityUpdatedCommandPublisher;
    this.authService = authService;
    this.configService = configService;
    this.dealService = dealService;
    this.conversionConverter = conversionConverter;
    this.userService = userService;
    this.productService = productService;
    this.companyService = companyService;
    this.configCacheService = configCacheService;
  }

  public void processConvertAction(ConvertLeadAction convertLeadAction, EntityEvent entityEvent, Metadata metadata) {
    log.info("Processing lead convert action for entity id {}", metadata.getEntityId());
    if (isConvertActionWithoutAnyEntity(convertLeadAction)) {
      publishEvent(metadata, new LeadConversion());
      return;
    }

    String authenticationToken = authService.getAuthenticationToken();

    Mono<ConversionMappingResponse> conversionMappingMono = configCacheService.getConversionMapping(metadata.getTenantId());
    Flux<FieldAttribute> lead = configService.getFieldAttributes("lead", authenticationToken);
    Flux<FieldAttribute> deal = Flux.empty();
    if (convertLeadAction.isDeal()) {
      deal = dealService.getFieldAttributes(authenticationToken);
    }
    Flux<FieldAttribute> company = Flux.empty();
    if (convertLeadAction.isCompany()) {
      company = companyService.getFieldAttributes(authenticationToken);
    }
    Flux<FieldAttribute> contact = Flux.empty();
    if (convertLeadAction.isContact()) {
      contact = configService.getFieldAttributes("contact", authenticationToken);
    }
    Mono<User> tenantCreatorMono = userService.getTenantCreator(metadata.getTenantId(), authenticationToken);
    Flux<IdName> currencies = Flux.empty();
    if (convertLeadAction.isDeal()) {
      currencies = configService.getAllCurrencies(authenticationToken);
    }

    Mono.zip(conversionMappingMono, lead.collectList(), deal.collectList(), tenantCreatorMono, currencies.collectList(),
            company.collectList(), contact.collectList())
        .flatMap(tuples -> {
          ConversionMappingResponse conversionMappingResponse = tuples.getT1();
          LeadDetail leadDetail = (LeadDetail) entityEvent.getEntity();

          Mono<List<ProductDetails>> leadProductsMono = Mono.just(Collections.emptyList());
          if (shouldFetchLeadProducts(conversionMappingResponse.getMapping(), leadDetail)) {
            List<Long> productIds = leadDetail.getProducts().stream().map(Product::getId).collect(Collectors.toList());
            leadProductsMono = productService.getProductsByIds(productIds, authenticationToken);
          }
          return Mono.zip(Mono.just(conversionMappingResponse), Mono.just(tuples.getT2()), Mono.just(tuples.getT3()), Mono.just(tuples.getT4()),
              leadProductsMono, Mono.just(tuples.getT5()), Mono.just(tuples.getT6()), Mono.just(tuples.getT7()));

        }).subscribe(tuples -> {
          ConversionMappingResponse conversionMappingResponse = tuples.getT1();
          List<FieldAttribute> leadAttributes = tuples.getT2();
          List<FieldAttribute> dealAttributes = tuples.getT3();
          Map<String, FieldAttribute> leadFieldsMap = leadAttributes.stream().collect(Collectors.toMap(FieldAttribute::getName, field -> field));
          Map<String, FieldAttribute> dealFieldsMap = dealAttributes.stream().collect(Collectors.toMap(FieldAttribute::getName, field -> field));
          User tenantCreator = tuples.getT4();
          List<ProductDetails> leadProducts = tuples.getT5();
          List<IdName> allCurrencies = tuples.getT6();
          List<FieldAttribute> companyAttributes = tuples.getT7();
          Map<String, FieldAttribute> companyFieldsMap = companyAttributes.stream().collect(Collectors.toMap(FieldAttribute::getName, field -> field));
          List<FieldAttribute> contactAttributes = tuples.getT8();
          Map<String, FieldAttribute> contactFieldsMap = contactAttributes.stream().collect(Collectors.toMap(FieldAttribute::getName, field -> field));
          LeadDetail leadDetail = (LeadDetail) entityEvent.getEntity();

          var dealPayload = new HashMap<String, Object>();
          var companyPayload = new HashMap<String, Object>();
          var contactPayload = new HashMap<String, Object>();

          for (MappingResponse mappingResponse : conversionMappingResponse.getMapping()) {
            FieldMappingResponse mappedLeadField = mappingResponse.getLeadField();
            FieldAttribute leadField = leadFieldsMap.get(mappedLeadField.getName());

            if (ObjectUtils.isEmpty(leadField)) {
              log.info("Unable to find the lead field {} from mapping", mappedLeadField.getName());
              continue;
            }

            if (ObjectUtils.isNotEmpty(mappingResponse.getDealFields()) && convertLeadAction.isDeal()) {
              List<FieldMappingResponse> mappedDealFields = mappingResponse.getDealFields();
              createPayload(mappedDealFields, leadField, leadDetail, dealPayload, dealFieldsMap, tenantCreator, leadProducts, allCurrencies,
                  EntityType.DEAL);
            }

            if (ObjectUtils.isNotEmpty(mappingResponse.getCompanyFields()) && convertLeadAction.isCompany()) {
              List<FieldMappingResponse> mappedCompanyFields = mappingResponse.getCompanyFields();
              createPayload(mappedCompanyFields, leadField, leadDetail, companyPayload, companyFieldsMap, tenantCreator, leadProducts, allCurrencies,
                  EntityType.COMPANY);
            }

            if (ObjectUtils.isNotEmpty(mappingResponse.getContactFields()) && convertLeadAction.isContact()) {
              List<FieldMappingResponse> mappedContactFields = mappingResponse.getContactFields();
              createPayload(mappedContactFields, leadField, leadDetail, contactPayload, contactFieldsMap, tenantCreator, leadProducts, allCurrencies,
                  EntityType.CONTACT);
            }
          }

          if (convertLeadAction.isDeal() && !dealPayload.containsKey("estimatedValue")) {
            Money mappedEstimatedValue = conversionConverter.getMappedEstimatedValue(leadDetail, allCurrencies, tenantCreator);
            if (ObjectUtils.isNotEmpty(mappedEstimatedValue)) {
              dealPayload.put("estimatedValue", mappedEstimatedValue);
            }
          }

          LeadConversion payload = getLeadConversionPayload(dealPayload, companyPayload, contactPayload);
          publishEvent(metadata, payload);
        });
  }

  private boolean isConvertActionWithoutAnyEntity(ConvertLeadAction convertLeadAction) {
    return !convertLeadAction.isDeal() && !convertLeadAction.isContact() && !convertLeadAction.isCompany();
  }

  private void publishEvent(Metadata metadata, LeadConversion leadConversion) {
    log.info("Publishing convert lead event for entityId {} with payload {}", metadata.getEntityId(), leadConversion.toString());
    entityUpdatedCommandPublisher.execute(metadata, leadConversion, false);
  }

  private void createPayload(List<FieldMappingResponse> mappedTargetEntityFields, FieldAttribute leadField, LeadDetail leadDetail,
      HashMap<String, Object> targetEntityPayload, Map<String, FieldAttribute> targetEntityFieldsMap, User tenantCreator,
      List<ProductDetails> leadProducts, List<IdName> allCurrencies, EntityType targetEntityType
  ) {
    for (FieldMappingResponse fieldMappingResponse : mappedTargetEntityFields) {
      FieldAttribute targetEntityField = targetEntityFieldsMap.get(fieldMappingResponse.getName());

      if (ObjectUtils.isEmpty(targetEntityField)) {
        log.info("Unable to find target entity field for field name {}, target entity {}", fieldMappingResponse.getName(), targetEntityType);
        continue;
      }

      if (!targetEntityField.isActive()) {
        log.info("{} Target entity field field name {} is not active", targetEntityType, fieldMappingResponse.getName());
        continue;
      }

      Object mappedValue;

      if (targetEntityField.getName().equalsIgnoreCase("products")) {
        mappedValue = conversionConverter.getMappedProductsAndEstimatedValue(leadProducts, allCurrencies, tenantCreator);
        targetEntityPayload.putAll((Map<String, Object>) mappedValue);
        continue;
      }

      if (List.of("ownerId", "ownedBy").contains(targetEntityField.getName())) {
        mappedValue = getMappedValueForOwnerField(fieldMappingResponse, tenantCreator, leadDetail, targetEntityType);
      } else {
        mappedValue = conversionConverter.getFieldValueForTargetEntityField(leadField, targetEntityField, leadDetail, tenantCreator,
            targetEntityType, Date.from(Instant.now()));
      }

      if (ObjectUtils.isEmpty(mappedValue)) {
        continue;
      }

      if (targetEntityField.isStandard()) {
        targetEntityPayload.put(targetEntityField.getName(), mappedValue);
      } else {
        targetEntityPayload.putIfAbsent("customFieldValues", new HashMap<String, Object>());
        HashMap<String, Object> customFieldValues = (HashMap<String, Object>) targetEntityPayload.get("customFieldValues");
        customFieldValues.put(targetEntityField.getName(), mappedValue);
      }

    }
  }

  private LeadConversion getLeadConversionPayload(HashMap<String, Object> dealPayload, HashMap<String, Object> companyPayload,
      HashMap<String, Object> contactPayload) {
    LeadConversion leadConversion = new LeadConversion();
    if (ObjectUtils.isNotEmpty(dealPayload)) {
      leadConversion.setDeal(new ConversionRequest(ConversionMode.CREATE, dealPayload));
    }
    if (ObjectUtils.isNotEmpty(companyPayload)) {
      leadConversion.setCompany(new ConversionRequest(ConversionMode.CREATE, companyPayload));
    }
    if (ObjectUtils.isNotEmpty(contactPayload)) {
      leadConversion.setContact(new ConversionRequest(ConversionMode.CREATE, contactPayload));
    }
    return leadConversion;
  }

  private boolean shouldFetchLeadProducts(List<MappingResponse> conversionMappings, LeadDetail leadDetail) {
    boolean isDealProductsFieldMapped = conversionMappings.stream()
        .anyMatch(mappingResponse -> mappingResponse.getLeadField().getName().equalsIgnoreCase("products")
            && ObjectUtils.isNotEmpty(mappingResponse.getDealFields()) &&
            mappingResponse.getDealFields().stream().anyMatch(dealField -> dealField.getName().equalsIgnoreCase("products")));

    return isDealProductsFieldMapped && ObjectUtils.isNotEmpty(leadDetail.getProducts());
  }

  private Object getMappedValueForOwnerField(FieldMappingResponse fieldMappingResponse, User tenantCreator,
      LeadDetail leadDetail, EntityType entityType) {
    IdName idName = conversionConverter
        .getMappedValueForTargetEntityOwnerField(fieldMappingResponse, tenantCreator, leadDetail);

    if (ObjectUtils.isNotEmpty(idName) && EntityType.CONTACT.equals(entityType)) {
      return idName.getId();
    }
    return idName;
  }

}
