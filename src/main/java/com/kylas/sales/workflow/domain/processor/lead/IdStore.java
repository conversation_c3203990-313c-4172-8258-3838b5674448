package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdStore {

  private final Object id;
  private final String name;

  @JsonCreator
  public IdStore(@JsonProperty("id") Object id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }
}
