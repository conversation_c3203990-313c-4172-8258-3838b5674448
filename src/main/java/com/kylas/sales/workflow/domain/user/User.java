package com.kylas.sales.workflow.domain.user;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import io.micrometer.core.instrument.util.StringUtils;
import java.io.Serializable;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Entity
@Getter
@Table(name = "users")
@NoArgsConstructor
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class User implements Serializable {

  private static final String WORKFLOW_PERMISSION_NAME = "workflow";
  @Id
  private long id;
  private String name;
  private long tenantId;
  private String timezone;
  private Boolean tenantUser;
  @Type(type = "jsonb")
  @Column(name = "reporting_managers", columnDefinition = "jsonb")
  private List<Long> reportingManagers;

  @Transient
  private Set<Permission> permissions = new HashSet<>();
  @Transient
  private String firstName;
  @Transient
  private String lastName;
  @Transient
  private String designation;
  @Transient
  private String department;
  @Transient
  private String language;
  @Transient
  private String currency;
  @Transient
  private String signature;
  @Transient
  private boolean active;
  private String email;
  @Transient
  private PhoneNumber[] phoneNumbers;
  @Transient
  private Long salutation;
  @Transient
  private Long createdBy;
  @Transient
  private Long updatedBy;
  @Transient
  private Metadata metaData;
  @Transient
  private String dateFormat;

  public User(long id, long tenantId, Set<Permission> permissions) {
    this.id = id;
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.name = null;
  }

  public User(long id, String name, long tenantId, Set<Permission> permissions, String email, String timezone) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.email = email;
    this.timezone = timezone;
  }

  public User(long id, String name, long tenantId, Set<Permission> permissions, String email, String timezone,
      Boolean tenantUser, List<Long> reportingManagers) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.email = email;
    this.timezone = timezone;
    this.tenantUser = tenantUser;
    this.reportingManagers = reportingManagers;
  }

  public User(long id, long tenantId, Set<Permission> permissions, String firstName, String lastName,
      String name, String department, String designation, String currency, String timezone,
      String dateFormat, String language, String signature, boolean active, String email,
      PhoneNumber[] phoneNumbers, long salutation, long createdBy, long updatedBy, Metadata metaData) {
    this.id = id;
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.firstName = firstName;
    this.lastName = lastName;
    this.department = department;
    this.designation = designation;
    this.timezone = timezone;
    this.currency = currency;
    this.language = language;
    this.signature = signature;
    this.metaData = metaData;
    this.active = active;
    this.email = email;
    this.phoneNumbers = phoneNumbers;
    this.salutation = salutation;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.dateFormat = dateFormat;
    this.name =
        StringUtils.isBlank(name)
            ? StringUtils.isBlank(firstName) ? lastName : firstName + " " + lastName
            : name;
  }

  @JsonCreator
  public User(
      @JsonProperty("id") long id,
      @JsonProperty("tenantId") long tenantId,
      @JsonProperty("permissions") Set<Permission> permissions,
      @JsonProperty("firstName") String firstName,
      @JsonProperty("lastName") String lastName,
      @JsonProperty("name") String name,
      @JsonProperty("department") String department,
      @JsonProperty("designation") String designation,
      @JsonProperty("currency") String currency,
      @JsonProperty("timezone") String timezone,
      @JsonProperty("dateFormat") String dateFormat,
      @JsonProperty("language") String language,
      @JsonProperty("signature") String signature,
      @JsonProperty("active") boolean active,
      @JsonProperty("email") String email,
      @JsonProperty("phoneNumbers") PhoneNumber[] phoneNumbers,
      @JsonProperty("salutation") long salutation,
      @JsonProperty("createdBy") long createdBy,
      @JsonProperty("updatedBy") long updatedBy,
      @JsonProperty("metaData") Metadata metaData,
      @JsonProperty("reportingManagers") List<IdName> reportingManagers) {
    this.id = id;
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.firstName = firstName;
    this.lastName = lastName;
    this.department = department;
    this.designation = designation;
    this.timezone = timezone;
    this.currency = currency;
    this.language = language;
    this.signature = signature;
    this.metaData = metaData;
    this.active = active;
    this.email = email;
    this.phoneNumbers = phoneNumbers;
    this.salutation = salutation;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.dateFormat = dateFormat;
    this.name =
        StringUtils.isBlank(name)
            ? StringUtils.isBlank(firstName) ? lastName : firstName + " " + lastName
            : name;
    this.reportingManagers = Optional.ofNullable(reportingManagers).orElseGet(Collections::emptyList).stream()
        .map(IdName::getId).collect(Collectors.toList());
  }

  public User withName(String name) {
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withName(String firstName, String lastName) {
    var name = StringUtils.isBlank(firstName) ? lastName : firstName + " " + lastName;
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withEmail(String email) {
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withTimezone(String timezone) {
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withPermissions(Set<Permission> permissions) {
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withTenantId(long tenantId) {
    return new User(id, name, tenantId, permissions, email, timezone, tenantUser, reportingManagers);
  }

  public User withTenantUser(boolean tenantUser) {
    this.tenantUser = tenantUser;
    return this;
  }

  public User withReportingManagers(List<Long> reportingManagers) {
    this.reportingManagers = reportingManagers;
    return this;
  }

  public boolean canCreateWorkflow() {
    return permissions.stream()
        .anyMatch(
            permission ->
                permission.getName().equalsIgnoreCase(WORKFLOW_PERMISSION_NAME)
                    && permission.getAction().canWrite());
  }

  public boolean canQueryHisWorkflow() {
    return permissions.stream()
        .anyMatch(
            permission ->
                permission.getName().equalsIgnoreCase(WORKFLOW_PERMISSION_NAME)
                    && permission.getAction().canRead());
  }

  public boolean canQueryAllWorkflow() {
    return permissions.stream()
        .anyMatch(
            permission ->
                permission.getName().equalsIgnoreCase(WORKFLOW_PERMISSION_NAME)
                    && permission.getAction().canReadAll());
  }

  public boolean canUpdateHisWorkflow() {
    return permissions.stream()
        .anyMatch(
            permission ->
                permission.getName().equalsIgnoreCase(WORKFLOW_PERMISSION_NAME)
                    && permission.getAction().canUpdate());
  }

  public boolean canUpdateAllWorkflow() {
    return permissions.stream()
        .anyMatch(
            permission ->
                permission.getName().equalsIgnoreCase(WORKFLOW_PERMISSION_NAME)
                    && permission.getAction().canUpdateAll());
  }

  public boolean isTenantUser() {
    return ObjectUtils.isEmpty(createdBy);
  }

  @Getter
  public static class Metadata {

    private final Map<String, Map<Long, String>> idNameStore;

    @JsonCreator
    public Metadata(@JsonProperty("idNameStore") Map<String, Map<Long, String>> idNameStore) {
      this.idNameStore = idNameStore;
    }
  }
}
