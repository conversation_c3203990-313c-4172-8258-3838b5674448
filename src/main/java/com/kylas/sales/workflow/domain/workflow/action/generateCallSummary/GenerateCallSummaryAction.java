package com.kylas.sales.workflow.domain.workflow.action.generateCallSummary;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.GENERATE_CALL_SUMMARY;
import static org.apache.commons.lang3.StringUtils.isBlank;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import javax.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class GenerateCallSummaryAction extends AbstractWorkflowAction implements WorkflowAction {
  private String prompt;

  public GenerateCallSummaryAction(String prompt) {
    this.prompt = prompt;
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    var payload = (ActionDetail.GenerateCallSummaryAction) actionResponse.getPayload();
    if (ObjectUtils.isEmpty(payload) || isBlank(payload.getPrompt())) {
      log.error("Invalid Generate Call Summary Action {}", actionResponse);
      throw new InvalidActionException();
    }
    return new GenerateCallSummaryAction(payload.getPrompt());
  }

  public static ActionResponse toActionResponse(GenerateCallSummaryAction action) {
    return new ActionResponse(action.getId(), GENERATE_CALL_SUMMARY, new ActionDetail.GenerateCallSummaryAction(action.getPrompt()));
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public GenerateCallSummaryAction update(ActionResponse actionResponse) {
    var payload = (ActionDetail.GenerateCallSummaryAction) actionResponse.getPayload();
    if (isBlank(payload.getPrompt())) {
      log.error("Invalid Generate Call Summary Action without prompt");
      throw new InvalidActionException();
    }
    this.setPrompt(payload.getPrompt());
    return this;
  }

  @Override
  public ActionType getType() {
    return ActionType.GENERATE_CALL_SUMMARY;
  }
}
