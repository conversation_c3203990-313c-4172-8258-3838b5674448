package com.kylas.sales.workflow.domain.workflow.action.marketPlace;


import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class MarketPlaceTriggerPayload {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private Long tenantId;
  private String entity;
  private UUID triggerId;
  private Date createdAt;
  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private Map<String,Object> requestPayload;

  private MarketPlaceTriggerPayload(Long tenantId, String entity, UUID triggerId, Map<String,Object> requestPayload){
    this.tenantId=tenantId;
    this.entity=entity;
    this.triggerId=triggerId;
    this.requestPayload=requestPayload;
    this.createdAt=new Date();
  }

  public static MarketPlaceTriggerPayload createNew(Long tenantId, String entity, UUID triggerId, Map<String,Object> requestPayload){
    return new MarketPlaceTriggerPayload(tenantId,entity,triggerId,requestPayload);
  }
}