package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.List;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.stream.Collectors;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class ContactSearchResponse implements SearchResponse {

  private final List<ContactResponse> content;
  private final Metadata metadata;

  @JsonCreator
  public ContactSearchResponse(@JsonProperty("content") List<ContactResponse> content, @JsonProperty("metaData") Metadata metadata) {
    this.content = content;
    this.metadata = metadata;
  }

  @Override
  public EntityResponse getContent() {
    return this.content.isEmpty() ? null : this.content.get(0);
  }

  @Override
  public Metadata getMetadata() {
    return this.metadata;
  }

  @Override
  public List<EntityResponse> getResponse() {
    return this.content.isEmpty() ? Collections.emptyList() : this.content.stream().collect(Collectors.toList());

  }

}