package com.kylas.sales.workflow.domain.processor;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.util.List;
import java.util.Map;

public interface EntityDetail {

  Long getId();

  IdName getOwner();

  EntityType getEntityType();

  void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities);
  void setMarketPlaceTriggerValues(Map<String,Object> marketPlaceTriggerValues);
  Map<String,Object> getMarketPlaceTriggerValues();

  Map<EntityType, List<EntityDetail>> getAssociatedEntities();

  @JsonIgnore
  IdName getUserForRelativeDateFilterTimezone();

  String getEntityName();
}
