package com.kylas.sales.workflow.domain.workflow.action.associatedActions;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail;
import com.kylas.sales.workflow.domain.processor.task.TaskDetail;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.AbstractEntityEvent;
import com.kylas.sales.workflow.mq.AssociatedEntityWorkflowPublisher;
import com.kylas.sales.workflow.mq.event.EntityAction;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import com.kylas.sales.workflow.security.AuthService;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TriggerWorkflowActionService {

  private final AssociatedEntityWorkflowPublisher associatedEntityWorkflowPublisher;
  private final AuthService authService;

  @Autowired
  public TriggerWorkflowActionService(AssociatedEntityWorkflowPublisher associatedEntityWorkflowPublisher, AuthService authService) {
    this.associatedEntityWorkflowPublisher = associatedEntityWorkflowPublisher;
    this.authService = authService;
  }

  public void executeTriggerWorkflowAction(TriggerWorkflowAction triggerWorkflowAction, EntityDetail entity) {
    Workflow associatedWorkflow = triggerWorkflowAction.getAssociatedWorkflow();
    EntityType entityType = associatedWorkflow.getEntityType();

    Map<EntityType, List<EntityDetail>> associatedEntities = entity.getAssociatedEntities();
    if (ObjectUtils.isEmpty(associatedEntities) || ObjectUtils.isEmpty(associatedEntities.get(entityType))) {
      return;
    }
    publishEventsForAssociatedEntities(associatedEntities.get(entityType), triggerWorkflowAction, associatedWorkflow);
  }

  private void publishEventsForAssociatedEntities(List<EntityDetail> entityDetails, TriggerWorkflowAction triggerWorkflowAction, Workflow associatedWorkflow) {
    for (EntityDetail entityDetail : entityDetails){
      EntityType entityType = associatedWorkflow.getEntityType();
      Metadata metadata = createMetadata(triggerWorkflowAction.getWorkflow(),associatedWorkflow, entityDetail);
      EntityEvent event = AbstractEntityEvent.getEntityEvent(entityType, entityDetail, metadata);
      log.info("Publish Associated {} workflow event for id {}", entityType, associatedWorkflow.getId());
      associatedEntityWorkflowPublisher.publishAssociatedEntityWorkflowExecuteEvent(event, associatedWorkflow.getId(), entityType);
    }
  }

  private Metadata createMetadata(Workflow workflow, Workflow associatedWorkflow, EntityDetail entityDetail) {
    return new Metadata(
        associatedWorkflow.getTenantId(),
        authService.getLoggedInUser().getId(),
        associatedWorkflow.getEntityType(),
        String.format("WF_%d", associatedWorkflow.getId()),
        new HashSet<>(),
        EntityAction.CREATED
    ).withAllWorkflowIds(new HashSet<>(){{ add(workflow.getId()); }})
        .withEntityId(entityDetail.getId()).withWorkflowName(associatedWorkflow.getName());
  }

}
