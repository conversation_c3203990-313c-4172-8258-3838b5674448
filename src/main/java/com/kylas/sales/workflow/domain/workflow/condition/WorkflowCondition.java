package com.kylas.sales.workflow.domain.workflow.condition;

import com.kylas.sales.workflow.common.dto.condition.WorkflowCondition.ConditionExpression;
import com.kylas.sales.workflow.domain.workflow.ConditionType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Entity
@Getter
@Setter
@Slf4j
public class WorkflowCondition extends AbstractWorkflowCondition implements Serializable {

  @Enumerated(value = EnumType.STRING)
  private ConditionType type;

  public WorkflowCondition(ConditionType type, ConditionExpression expression) {
    super(expression);
    this.type = type;
  }

  public WorkflowCondition(){ super(); }

  public WorkflowCondition(Long id, ConditionType type, ConditionExpression expression, Workflow workflow) {
    super(id, expression, workflow);
    this.type = type;
  }

  public WorkflowCondition update(ConditionType type, ConditionExpression expression) {
    return new WorkflowCondition(this.id, type, expression, this.workflow);
  }

}
