package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.common.dto.Tenant;
import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class UserService {

  private final String clientBasePath;

  @Autowired
  public UserService(@Value("${client.iam.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Mono<User> getUserDetails(long userId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/users").path("/" + userId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(User.class);
  }

  public Mono<Tenant> getTenantDetails(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/tenants").build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(Tenant.class);
  }

  public Mono<User> getTenantCreator(long tenantId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/tenants").path("/" + tenantId).path("/creator").build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(User.class);
  }

  public Mono<List<IdName>> getTeamDetails(List<Long> ids, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/teams/summary").queryParam("id", ids).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class)
        .collectList();
  }

  public Mono<Permission> getCurrentUserPermissionsForEntity(EntityType entity, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/users/me/permissions/"+entity.getEntityName()).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(Permission.class);
  }
}
