package com.kylas.sales.workflow.domain.processor.deal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.Actionable;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.Pipeline;
import com.kylas.sales.workflow.domain.processor.lead.ForecastingType;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.EntityResponse;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
public class DealDetail extends EntityResponse implements Serializable, Actionable, EntityDetail {

  private Long id;
  private String name;
  private IdName ownedBy;
  private Money estimatedValue;
  private Money actualValue;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date estimatedClosureOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date actualClosureDate;
  private List<IdName> associatedContacts;
  private IdName product;
  private Pipeline pipeline;
  private IdName pipelineStage;
  private String pipelineStageReason;
  private IdName company;
  private ForecastingType forecastingType;
  private IdName createdBy;
  private IdName updatedBy;
  private IdName importedBy;
  private Date createdAt;
  private Date updatedAt;
  private IdName source;
  private IdName campaign;
  private Map<String, Object> customFieldValues;
  private List<DealProduct> products;

  // Source Fields
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;

  // UTM fields
  private String subSource;
  private String utmSource;
  private String utmCampaign;
  private String utmMedium;
  private String utmContent;
  private String utmTerm;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date taskDueOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date meetingScheduledOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date latestActivityCreatedAt;
  private Boolean isNew;
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;
  private Double score;
  
  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.deal.update";
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    return this;
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return this.ownedBy;
  }

  @Override
  @JsonIgnore
  public EntityType getEntityType() {
    return EntityType.DEAL;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    return;
  }

  @Override
  public Map<EntityType, List<EntityDetail>> getAssociatedEntities() {
    return null;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return ownedBy;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return this.name;
  }

  public void setPipelineStage(IdName pipelineStage){
    if(pipelineStage != null){
      this.pipeline.setStage(pipelineStage);
    }
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }
}
