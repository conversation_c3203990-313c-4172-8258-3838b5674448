package com.kylas.sales.workflow.domain.service.client;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.Map;

public abstract class EntityResponse {
  public abstract EntityDetail createFromEntityResponse(Metadata metadata);

  protected IdName getIdName(Map<String, Map<Long, String>> idNameStore, Long id, String fieldName) {
    if (id != null && idNameStore.containsKey(fieldName)) {
      return new IdName(id, idNameStore.get(fieldName).get(id));
    }
    return new IdName(id, null);
  }
}
