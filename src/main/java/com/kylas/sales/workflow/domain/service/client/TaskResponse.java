package com.kylas.sales.workflow.domain.service.client;

import static java.util.Collections.emptyMap;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.task.RelationEvent;
import com.kylas.sales.workflow.domain.processor.task.TaskDetail;
import com.kylas.sales.workflow.domain.processor.task.TaskRelation;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class TaskResponse extends EntityResponse {

  private final Long id;
  private final String name;
  private final String description;
  private final Date dueDate;
  private final String reminder;
  private final Date remindAt;
  private final Date originalDueDate;
  private final Date completedAt;
  private final Date createdAt;
  private final Date updatedAt;
  private final Date cancelledAt;
  private final String createdViaId;
  private final String createdViaName;
  private final String createdViaType;
  private final String updatedViaId;
  private final String updatedViaName;
  private final String updatedViaType;
  private final Long createdBy;
  private final Long updatedBy;
  private final Long ownerId;
  private final Long type;
  private final Long status;
  private final Long priority;
  private final Long assignedTo;
  private final List<TaskRelation> relations;
  private Map<String, Map<String, String>> idNameStore = new HashMap<>();
  private Map<String, Object> customFieldValues;

  @JsonCreator
  public TaskResponse(
      @JsonProperty("id") Long id,
      @JsonProperty("name") String name,
      @JsonProperty("description") String description,
      @JsonProperty("dueDate") Date dueDate,
      @JsonProperty("reminder") String reminder,
      @JsonProperty("remindAt") Date remindAt,
      @JsonProperty("originalDueDate") Date originalDueDate,
      @JsonProperty("completedAt") Date completedAt,
      @JsonProperty("createdAt") Date createdAt,
      @JsonProperty("updatedAt") Date updatedAt,
      @JsonProperty("cancelledAt") Date cancelledAt,
      @JsonProperty("createdViaId") String createdViaId,
      @JsonProperty("createdViaName") String createdViaName,
      @JsonProperty("createdViaType") String createdViaType,
      @JsonProperty("updatedViaId") String updatedViaId,
      @JsonProperty("updatedViaName") String updatedViaName,
      @JsonProperty("updatedViaType") String updatedViaType,
      @JsonProperty("createdBy") Long createdBy,
      @JsonProperty("updatedBy") Long updatedBy,
      @JsonProperty("ownerId") Long ownerId,
      @JsonProperty("type") Long type,
      @JsonProperty("status") Long status,
      @JsonProperty("priority") Long priority,
      @JsonProperty("assignedTo") Long assignedTo,
      @JsonProperty("relation") List<TaskRelation> relations,
      @JsonProperty("idNameStore") Map<String, Map<String, String>> idNameStore,
      @JsonProperty("customFieldValues")Map<String, Object> customFieldValues
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.dueDate = dueDate;
    this.reminder = reminder;
    this.remindAt = remindAt;
    this.originalDueDate = originalDueDate;
    this.completedAt = completedAt;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.cancelledAt = cancelledAt;
    this.createdViaId = createdViaId;
    this.createdViaName = createdViaName;
    this.createdViaType = createdViaType;
    this.updatedViaId = updatedViaId;
    this.updatedViaName = updatedViaName;
    this.updatedViaType = updatedViaType;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.ownerId = ownerId;
    this.type = type;
    this.status = status;
    this.priority = priority;
    this.assignedTo = assignedTo;
    this.relations = relations;
    this.idNameStore=idNameStore;
    this.customFieldValues=customFieldValues;
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    var createdBy = getIdName(metadata.getIdNameStore(), this.getCreatedBy(), "createdBy");
    var updatedBy = getIdName(metadata.getIdNameStore(), this.getUpdatedBy(), "updatedBy");
    var ownerId = getIdName(metadata.getIdNameStore(), this.getOwnerId(), "ownerId");
    var priority = getIdName(metadata.getIdNameStore(), this.getPriority(), "priority");
    var type = getIdName(metadata.getIdNameStore(), this.getType(), "type");
    var status = getIdName(metadata.getIdNameStore(), this.getStatus(), "status");
    var assignedTo = getIdName(metadata.getIdNameStore(), this.getAssignedTo(), "assignedTo");
    var relations = this.getRelations().stream().map(relation -> new RelationEvent(relation.getTargetEntityType(), relation.getTargetEntityId()))
        .collect(Collectors.toList());

    return new TaskDetail(
        this.getId(),
        this.getName(),
        this.getDescription(),
        this.getDueDate(),
        this.getReminder(),
        this.getRemindAt(),
        this.getOriginalDueDate(),
        this.getCompletedAt(),
        this.getCreatedAt(),
        this.getUpdatedAt(),
        this.getCancelledAt(),
        this.getCreatedViaId(),
        this.getCreatedViaName(),
        this.getCreatedViaType(),
        this.getUpdatedViaId(),
        this.getUpdatedViaName(),
        this.getUpdatedViaType(),
        createdBy,
        updatedBy,
        ownerId,
        type,
        priority,
        assignedTo,
        status,
        relations,
        this.idNameStore,
        this.customFieldValues,
        emptyMap(),emptyMap()
    );
  }
}
