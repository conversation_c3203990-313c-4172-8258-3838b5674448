package com.kylas.sales.workflow.domain.service.client;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.contact.ContactDetail;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.GPSCoordinateEvent;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class ContactResponse extends EntityResponse {

  @JsonProperty private Long id;
  @JsonProperty private Long tenantId;
  @JsonProperty private Long ownerId;
  @JsonProperty private Long salutation;
  @JsonProperty private String firstName;
  @JsonProperty private String lastName;

  @JsonProperty private PhoneNumber[] phoneNumbers;
  @JsonProperty private Email[] emails;

  @JsonProperty private Map<String, Object> customFieldValues;

  @JsonProperty private boolean dnd;
  @JsonProperty private String timezone;
  @JsonProperty private String address;
  @JsonProperty private String city;
  @JsonProperty private String state;
  @JsonProperty private String zipcode;
  @JsonProperty private String country;

  @JsonProperty private String facebook;
  @JsonProperty private String twitter;
  @JsonProperty private String linkedin;


  //Professional
  @JsonProperty private Long company;
  @JsonProperty private String department;
  @JsonProperty private String designation;
  @JsonProperty private boolean stakeholder;

  //Internal
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  @JsonProperty
  private Date convertedAt;
  @JsonProperty
  private Long convertedBy;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  @JsonProperty
  private Date createdAt;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  @JsonProperty
  private Date updatedAt;
  @JsonProperty
  private Long createdBy;
  @JsonProperty
  private Long updatedBy;
  @JsonProperty
  private Long importedBy;
  @JsonProperty
  private Integer version;

  @JsonProperty
  private List<Long> associatedDeals;

  @JsonProperty
  private Map<String, Object> metaData;

  // Source Fields
  @JsonProperty
  private String createdViaId;
  @JsonProperty
  private String createdViaName;
  @JsonProperty
  private String createdViaType;
  @JsonProperty
  private String updatedViaId;
  @JsonProperty
  private String updatedViaName;
  @JsonProperty
  private String updatedViaType;
  @JsonProperty
  private Long campaign;
  @JsonProperty
  private Long source;
  @JsonProperty
  private String subSource;
  @JsonProperty
  private String utmSource;
  @JsonProperty
  private String utmCampaign;
  @JsonProperty
  private String utmMedium;
  @JsonProperty
  private String utmContent;
  @JsonProperty
  private String utmTerm;
  @JsonProperty
  private Double score;
  private Map<String, Map<Integer, String>> idNameStore = new HashMap<>();
  private GPSCoordinateEvent addressCoordinate;

  @Override
  public EntityDetail createFromEntityResponse(Metadata metaData) {
    var createdBy = getIdName(metaData.getIdNameStore(), this.getCreatedBy(), "createdBy");
    var updatedBy = getIdName(metaData.getIdNameStore(), this.getUpdatedBy(), "updatedBy");
    var convertedBy = getIdName(metaData.getIdNameStore(), this.getConvertedBy(), "convertedBy");
    var importedBy = getIdName(metaData.getIdNameStore(), this.getImportedBy(), "importedBy");
    var salutation = getIdName(metaData.getIdNameStore(), this.getSalutation(), "salutation");
    var ownerId = getIdName(metaData.getIdNameStore(), this.getOwnerId(), "ownerId");
    var company = getIdName(metaData.getIdNameStore(), this.getCompany(), "company");
    var campaign = getIdName(metaData.getIdNameStore(), this.getCampaign(), "campaign");
    var source = getIdName(metaData.getIdNameStore(), this.getSource(), "source");
    return new ContactDetail(this.getId(), this.getTenantId(), ownerId, this.getFirstName(), this.getLastName(), null, salutation,
        this.getAddress(), this.getCity(), this.getState(), this.getZipcode(), this.getCountry(), this.isDnd(), this.getTimezone(),
        this.getPhoneNumbers(), this.getEmails(), this.getFacebook(), this.getTwitter(), this.getLinkedin(), company,
        this.getDesignation(), this.getDepartment(), this.isStakeholder(), this.getConvertedAt(), convertedBy,
        this.getVersion(), this.getCreatedAt(), this.getUpdatedAt(), createdBy, updatedBy,importedBy, this.getCustomFieldValues(), this.getAssociatedDeals(),
        this.getCreatedViaId(), this.getCreatedViaName(), this.getCreatedViaType(), this.getUpdatedViaId(), this.getUpdatedViaName(),
        this.getUpdatedViaType(), campaign, source,this.getSubSource(), this.getUtmSource(), this.getUtmCampaign(), this.getUtmMedium(),
        this.getUtmContent(), this.getUtmTerm(), this.getScore(), metaData.getIdNameStore(), Collections.emptyMap(), this.getAddressCoordinate());
  }


}
