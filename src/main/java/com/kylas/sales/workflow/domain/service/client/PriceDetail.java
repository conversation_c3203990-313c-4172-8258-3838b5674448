package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceDetail {

  private final Currency currency;
  private final double value;

  @JsonCreator
  public PriceDetail(
      @JsonProperty("currency") Currency currency, @JsonProperty("value") double value) {
    this.currency = currency;
    this.value = value;
  }
}
