package com.kylas.sales.workflow.domain.workflow.action.email;

import com.kylas.sales.workflow.domain.EmailRecipientRepository;
import com.kylas.sales.workflow.domain.exception.InvalidEmailActionTypeException;
import com.kylas.sales.workflow.domain.processor.EmailActionDetail;
import com.kylas.sales.workflow.domain.processor.email.EmailEventPayload;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Organizer;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.ValueResolver;
import com.kylas.sales.workflow.domain.service.client.EntitySummary;
import com.kylas.sales.workflow.domain.service.client.SearchService;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.user.UserFacade;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.EmailActionEventPublisher;
import com.kylas.sales.workflow.mq.event.EmailActionEvent;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import com.kylas.sales.workflow.mq.event.UserEventV2;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.security.InternalAuthProvider;
import io.micrometer.core.instrument.util.StringUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EmailActionService {

  private static final String CONTACT_PLURAL = "contacts";

  private final List<EmailType> emailActionTypes = List
      .of(EmailType.USER, EmailType.CUSTOM_EMAIL, EmailType.TENANT_EMAIL);

  private final EmailActionEventPublisher emailActionEventPublisher;
  private final AuthService authService;
  private final EmailRecipientRepository emailRecipientRepository;
  private final UserFacade userFacade;
  private final UserService userService;
  private final SearchService searchService;
  private final InternalAuthProvider internalAuthProvider;
  private final ValueResolver valueResolver;

  @Autowired
  public EmailActionService(EmailActionEventPublisher emailActionEventPublisher,
      AuthService authService, EmailRecipientRepository emailRecipientRepository, UserFacade userFacade,
      UserService userService, SearchService searchService, InternalAuthProvider internalAuthProvider, ValueResolver valueResolver) {
    this.emailActionEventPublisher = emailActionEventPublisher;
    this.authService = authService;
    this.emailRecipientRepository = emailRecipientRepository;
    this.userFacade = userFacade;
    this.userService = userService;
    this.searchService = searchService;
    this.internalAuthProvider = internalAuthProvider;
    this.valueResolver = valueResolver;
  }

  public void processEmailAction(EmailAction emailAction, EntityEvent event, Metadata metadata) {
    log.info("==Email Action=event:{}= emailAction:{} =", event.getEntity(), emailAction);
    Workflow workflow = emailAction.getWorkflow();

    long entityId = event.getEntityId();
    EmailEventPayload emailFrom = fromEmailRecipient(emailAction.getFrom(), workflow, event);
    List<EmailEventPayload> to = getResolvedListOfEmailRecipients(emailAction.getTo(), workflow, event);
    List<EmailEventPayload> cc = getResolvedListOfEmailRecipients(emailAction.getCc(), workflow, event);
    List<EmailEventPayload> bcc = getResolvedListOfEmailRecipients(emailAction.getBcc(), workflow, event);
    EmailEventPayload relatedTo = getRelatedTo(event);

    boolean validateEmailIsPresent = validateEmailIsPresent(to, cc, bcc);
    if(!validateEmailIsPresent){
      log.info("Valid email TO, CC, BCC does not present for metdata {}", metadata);
      return;
    }
    if (ObjectUtils.isNotEmpty(emailFrom) && ObjectUtils.isNotEmpty(emailFrom.getId()) && validateEmailIsPresent) {
      EmailActionEvent emailActionEvent = new EmailActionEvent(emailFrom.getId(), to, cc, bcc, relatedTo, emailAction.getEmailTemplateId(),
          metadata.getUserId(), metadata.getTenantId(),emailAction.isTrackingEnabled(), metadata);
      log.info("publishing email action event for entityId {} ", entityId);
      emailActionEventPublisher.publishEmailActionEvent(emailActionEvent);
    }
  }

  private boolean validateEmailIsPresent(List<EmailEventPayload> to, List<EmailEventPayload> cc, List<EmailEventPayload> bcc) {

    Set<EmailEventPayload> allRecipients = new HashSet<>() {{
      addAll(to);
      addAll(cc);
      addAll(bcc);
    }};

    boolean isEmailsPresent = allRecipients
        .stream()
        .anyMatch(emailEventPayload -> ObjectUtils.isNotEmpty(emailEventPayload.getEmail()));

    if (!isEmailsPresent){
      log.info("Emails are not found in to, cc, bcc");
      return false;
    }
    return true;
  }

  private List<EmailEventPayload> getResolvedListOfEmailRecipients(Set<EmailRecipient> emailRecipients,
      Workflow workflow, EntityEvent event) {

    EmailActionDetail emailActionDetail = event.getEmailActionDetail();

    List<EmailEventPayload> emailRecipientsList = new ArrayList<>();

    if (emailRecipients == null || emailRecipients.isEmpty()) {
      return emailRecipientsList;
    }

    emailRecipients.stream()
        .filter(emailRecipient -> emailRecipient.getType().equals(EmailType.ALL_ASSOCIATED_CONTACTS))
        .forEach(
            emailRecipient -> emailRecipientsList
                .addAll(convertAllAssociatedContactsToRecipients(emailActionDetail, event.getMetadata())));

    emailRecipients.stream()
        .filter(emailRecipient -> emailRecipient.getType().equals(EmailType.ALL_AVAILABLE_EMAILS))
        .forEach(
            emailRecipient -> emailRecipientsList
                .addAll(convertAvailableEmailsToRecipients(emailRecipient, emailActionDetail, event.getEntityId())));

    emailRecipients.stream()
            .filter(emailRecipient -> isUsersReportingManagerEmailType(emailRecipient.getType()))
                .forEach(emailRecipient -> emailRecipientsList
                    .addAll(getUserReportingManagersAsRecipients(emailRecipient, emailActionDetail, event.getMetadata().getTenantId())));

    emailRecipients.stream()
        .filter(emailRecipient -> !emailRecipient.getType().equals(EmailType.ALL_AVAILABLE_EMAILS) && !emailRecipient.getType()
            .equals(EmailType.ALL_ASSOCIATED_CONTACTS) && !isUsersReportingManagerEmailType(emailRecipient.getType()))
        .forEach(emailRecipient -> {
          EmailEventPayload localEmailEventPayload = fromEmailRecipient(emailRecipient, workflow, event);
          if (localEmailEventPayload != null) {
            emailRecipientsList.add(localEmailEventPayload);
          }
        });

    HashSet<Long> uniqueUserIds = new HashSet<>();
    return emailRecipientsList.stream()
        .filter(emailEventPayload -> {
          if ("USER".equalsIgnoreCase(emailEventPayload.getEntity())) {
            return uniqueUserIds.add(emailEventPayload.getId());
          }
          return true;
        }).collect(Collectors.toList());
  }

  private boolean isUsersReportingManagerEmailType(EmailType emailType) {
    return List.of(EmailType.RECORD_OWNER_REPORTING_MANAGER, EmailType.RECORD_CREATED_BY_REPORTING_MANAGER,
        EmailType.RECORD_UPDATED_BY_REPORTING_MANAGER, EmailType.RECORD_ASSIGNEE_REPORTING_MANAGER).contains(emailType);
  }

  private EmailEventPayload fromEmailRecipient(EmailRecipient emailRecipient, Workflow workflow,
      EntityEvent event) {
    EmailActionDetail emailActionDetail = event.getEmailActionDetail();
    long tenantId = event.getMetadata().getTenantId();

    if (emailActionTypes.contains(emailRecipient.getType())) {
      return new EmailEventPayload(emailRecipient.getEntity(), emailRecipient.getEntityId(), emailRecipient.getName(), emailRecipient.getEmail());
    }

    switch (emailRecipient.getType()) {
      case RECORD_OWNER:
        return getEmailEventPayloadWithUpdatedInfo(emailRecipient, emailActionDetail.getOwnedBy().getId(), tenantId);
      case RECORD_ASSIGNEE:
        return getEmailEventPayloadWithUpdatedInfo(emailRecipient, emailActionDetail.getAssigneeTo().getId(), tenantId);
      case RECORD_CREATED_BY:
        return getEmailEventPayloadWithUpdatedInfo(emailRecipient, emailActionDetail.getCreatedBy().getId(), tenantId);
      case RECORD_UPDATED_BY:
        return getEmailEventPayloadWithUpdatedInfo(emailRecipient, emailActionDetail.getUpdatedBy().getId(), tenantId);
      case WORKFLOW_CREATOR:
        return new EmailEventPayload(emailRecipient.getEntity(), workflow.getCreatedBy().getId(), emailRecipient.getName(),
            emailRecipient.getEmail());
      case WORKFLOW_UPDATER:
        return new EmailEventPayload(emailRecipient.getEntity(), workflow.getUpdatedBy().getId(), emailRecipient.getName(),
            emailRecipient.getEmail());
      case RECORD_PRIMARY_EMAIL:
        return convertRecordPrimaryEmailToRecipient(emailRecipient, event.getEmailActionDetail(), event.getEntityId());
      case MEETING_ORGANIZER:
        return getEmailEventPayloadForMeetingOrganizer(emailRecipient, emailActionDetail.getOrganizer());
      case LEAD:
      case CONTACT:
        return getEmailRecipientForLeadOrContact(emailRecipient, event);
      case RECORD_OWNER_REPORTING_MANAGER:
        return getReportingManagerEmailEventPayload(emailActionDetail.getOwnedBy().getId(), tenantId);
      case RECORD_CREATED_BY_REPORTING_MANAGER:
        return getReportingManagerEmailEventPayload(emailActionDetail.getCreatedBy().getId(), tenantId);
      case RECORD_UPDATED_BY_REPORTING_MANAGER:
        return getReportingManagerEmailEventPayload(emailActionDetail.getUpdatedBy().getId(), tenantId);
      case RECORD_ASSIGNEE_REPORTING_MANAGER:
        return getReportingManagerEmailEventPayload(emailActionDetail.getAssigneeTo().getId(), tenantId);
      default:
        throw new InvalidEmailActionTypeException();
    }
  }

  private EmailEventPayload getEmailEventPayloadForMeetingOrganizer(EmailRecipient emailRecipient, Organizer organizer) {
    if (ObjectUtils.isEmpty(organizer)) {
      return null;
    }
    if (!EntityType.USER.equals(organizer.getEntity())) {
      log.info("Meeting organizer type is other than user, so return email event payload as custom email");
      return new EmailEventPayload(EmailEntityType.EMAIL.getEntityName(), null, organizer.getEmail(), organizer.getEmail());
    }
    return new EmailEventPayload(emailRecipient.getEntity(), organizer.getId(), organizer.getName(), organizer.getEmail());
  }

  private EmailEventPayload getReportingManagerEmailEventPayload(Long userId, Long tenantId) {
    List<Long> reportingManagers = valueResolver.getOrUpdateUserReportingManagers(userId, tenantId, authService.getAuthenticationToken());
    if (ObjectUtils.isEmpty(reportingManagers)) {
      log.info("No reporting managers for userId: {}, tenantId: {}", userId, tenantId);
      return null;
    }
    return new EmailEventPayload(EmailEntityType.USER.getEntityName(), reportingManagers.get(0), "", "");
  }

  private List<EmailEventPayload> getUserReportingManagersAsRecipients(EmailRecipient emailRecipient, EmailActionDetail emailActionDetail,
      Long tenantId) {
    switch (emailRecipient.getType()) {
      case RECORD_OWNER_REPORTING_MANAGER:
        return getUserReportingManagersEmailEventPayload(emailRecipient, emailActionDetail.getOwnedBy().getId(), tenantId);
      case RECORD_CREATED_BY_REPORTING_MANAGER:
        return getUserReportingManagersEmailEventPayload(emailRecipient, emailActionDetail.getCreatedBy().getId(), tenantId);
      case RECORD_UPDATED_BY_REPORTING_MANAGER:
        return getUserReportingManagersEmailEventPayload(emailRecipient, emailActionDetail.getUpdatedBy().getId(), tenantId);
      case RECORD_ASSIGNEE_REPORTING_MANAGER:
        return getUserReportingManagersEmailEventPayload(emailRecipient, emailActionDetail.getAssigneeTo().getId(), tenantId);
    }
    return Collections.emptyList();
  }

  private List<EmailEventPayload> getUserReportingManagersEmailEventPayload(EmailRecipient emailRecipient, Long userId, Long tenantId) {
    List<Long> reportingManagers = valueResolver.getOrUpdateUserReportingManagers(userId, tenantId, authService.getAuthenticationToken());
    log.info("Build reporting managers email payload for userId: {}, tenantId: {}, reportingManagers: {}", userId, tenantId, reportingManagers);
    return getReportingManagersEmailEventPayload(emailRecipient, reportingManagers, tenantId);
  }

  private List<EmailEventPayload> getReportingManagersEmailEventPayload(EmailRecipient emailRecipient, List<Long> reportingManagers, Long tenantId) {
    if (ObjectUtils.isEmpty(reportingManagers)) {
      return Collections.emptyList();
    }
    return reportingManagers.stream().map(reportingManagerId -> {
      Optional<User> userOptional = userFacade.tryGetUserByIdAndTenantId(reportingManagerId, tenantId);
      return userOptional.map(user -> new EmailEventPayload(emailRecipient.getEntity(), reportingManagerId, user.getName(), user.getEmail()))
          .orElseGet(() -> userService.getUserDetails(reportingManagerId, authService.getAuthenticationToken())
              .map(userDetails -> userFacade.getExistingOrCreateNewUser(userDetails, tenantId))
              .map(persistedUser -> new EmailEventPayload(emailRecipient.getEntity(), reportingManagerId, persistedUser.getName(),
                  persistedUser.getEmail()))
              .block());
    }).collect(Collectors.toList());
  }

  private EmailEventPayload convertRecordPrimaryEmailToRecipient(EmailRecipient emailRecipient,
      EmailActionDetail emailActionDetail, long entityId) {

    return null == emailActionDetail.getEmails() || emailActionDetail.getEmails().length == 0 ? null :
        Arrays.stream(emailActionDetail.getEmails())
            .filter(Email::isPrimary)
            .findFirst()
            .map(email -> new EmailEventPayload(emailRecipient.getEntity(), entityId, emailActionDetail.getName(), email.getValue()))
            .orElse(
                new EmailEventPayload(emailRecipient.getEntity(), emailRecipient.getEntityId(), emailRecipient.getName(), emailRecipient.getEmail()));
  }

  private List<EmailEventPayload> convertAvailableEmailsToRecipients(
      EmailRecipient emailRecipient,
      EmailActionDetail emailActionDetail, long entityId) {
    return null == emailActionDetail.getEmails() || emailActionDetail.getEmails().length == 0 ? Collections.emptyList()
        : Arrays.stream(emailActionDetail.getEmails())
            .map(email -> new EmailEventPayload(emailRecipient.getEntity(), entityId, emailActionDetail.getName(), email.getValue()))
            .collect(Collectors.toList());
  }

  private List<EmailEventPayload> convertAllAssociatedContactsToRecipients(EmailActionDetail emailActionDetail, Metadata metadata) {

    if(ObjectUtils.isEmpty(emailActionDetail.getAssociatedContacts())){
      log.info("Associated contacts are empty for preparing email recipient for metadata {}", metadata);
      return new ArrayList<>();
    }
    List<Long> associatedContactsIds = emailActionDetail.getAssociatedContacts().stream().map(IdName::getId)
        .collect(Collectors.toList());

    List<EmailEventPayload> emailEventPayloads = new ArrayList<>();

    var readAction = new Action();
    readAction.setRead(true);
    readAction.setReadAll(true);
    Set<Permission> permissions = Set.of(new Permission(3, "user", "has access to user resource", readAction),
        new Permission(17, "contact", "has access to contact resource", readAction));

    String authToken = internalAuthProvider.getAuthTokenWithRequiredPermissions(metadata.getUserId(), metadata.getTenantId(), permissions);

    return searchService.getSummaries(CONTACT_PLURAL, associatedContactsIds, authToken)
        .map(entitySummaries -> {
          entitySummaries.forEach(entitySummary -> entitySummary.getEmails()
              .stream()
              .filter(EntitySummary.Email::isPrimary)
              .findFirst()
              .ifPresent(email -> emailEventPayloads.add(new EmailEventPayload(entitySummary.getEntity(), entitySummary.getId(),
                  entitySummary.getName(), email.getValue()))));
          return emailEventPayloads;
        }).block();
  }

  private EmailEventPayload getRelatedTo(EntityEvent event) {

    EntityType entityType = event.getMetadata().getEntityType();
    EmailActionDetail emailActionDetail = event.getEmailActionDetail();

    if (entityType.equals(EntityType.DEAL) || entityType.equals(EntityType.TASK) || entityType.equals(EntityType.MEETING)) {
      return new EmailEventPayload(entityType.name().toLowerCase(), event.getEntityId(), emailActionDetail.getName(), null);
    }

    String primaryEmail =
        null == emailActionDetail.getEmails() || emailActionDetail.getEmails().length == 0 ? null : Arrays.stream(emailActionDetail.getEmails())
            .filter(Email::isPrimary)
            .findFirst()
            .map(Email::getValue)
            .orElse(null);

    return new EmailEventPayload(entityType.name().toLowerCase(), event.getEntityId(), emailActionDetail.getName(), primaryEmail);
  }

  private EmailEventPayload getEmailEventPayloadWithUpdatedInfo(EmailRecipient emailRecipient, long userId, long tenantId) {
      Optional<User> user = userFacade.tryGetUserByIdAndTenantId(userId, tenantId);
    return user.map(mappedUser -> new EmailEventPayload(emailRecipient.getEntity(), userId, mappedUser.getName(),
        mappedUser.getEmail()))
        .orElseGet(() -> userService.getUserDetails(userId, authService.getAuthenticationToken())
            .map(userDetails -> userFacade.getExistingOrCreateNewUser(userDetails, tenantId))
            .map(persistedUser -> new EmailEventPayload(emailRecipient.getEntity(), userId, persistedUser.getName(),
                persistedUser.getEmail()))
            .block());
  }

  public void updateSystemUserInEmailRecipient(String entity, Long entityId, UserEventV2 userEventV2) {
    String name = StringUtils.isBlank(userEventV2.getFirstName()) ? userEventV2.getLastName() :
        userEventV2.getFirstName() + " " + userEventV2.getLastName();

    emailRecipientRepository.findByEntityAndEntityId(entity, entityId)
        .forEach(emailRecipient -> {
          emailRecipient.setName(name);
          emailRecipient.setEmail(userEventV2.getEmail());
          emailRecipientRepository.saveAndFlush(emailRecipient);
        });
  }

  private EmailEventPayload getEmailRecipientForLeadOrContact(EmailRecipient emailRecipient, EntityEvent event) {
    Email[] updatedEmails = event.getEmailActionDetail().getEmails();
    if (updatedEmails != null && emailRecipient.getEntityId().equals(event.getEntityId())) {
      return Arrays.stream(updatedEmails).filter(email -> email.getValue().equals(emailRecipient.getEmail()))
          .findFirst()
          .map(email -> new EmailEventPayload(emailRecipient.getEntity(), emailRecipient.getEntityId(), event.getEmailActionDetail().getName(),
              emailRecipient.getEmail()))
          .orElseGet(() -> {
            log.info("email will not be sent to email {} as it does not match to existing record", emailRecipient.getEmail());
            return null;
          });
    }
    return new EmailEventPayload(emailRecipient.getEntity(), emailRecipient.getEntityId(), emailRecipient.getName(), emailRecipient.getEmail());
  }
}
