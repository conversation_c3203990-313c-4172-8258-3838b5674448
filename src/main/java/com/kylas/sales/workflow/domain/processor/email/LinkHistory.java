package com.kylas.sales.workflow.domain.processor.email;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;
import lombok.Getter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class LinkHistory {
  private final String url;
  private final List<Date> clickedAt;

  @JsonCreator
  public LinkHistory(
      @JsonProperty("url") String url,
      @JsonProperty("clickedAt") List<Date> clickedAt
  ) {
    this.url = url;
    this.clickedAt = clickedAt;
  }
}
