package com.kylas.sales.workflow.domain.processor.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class TaskDetail implements Serializable, EntityDetail {

  private Long id;
  private String name;
  private String description;
  private Date dueDate;
  private String reminder;
  private Date remindAt;
  private Date originalDueDate;
  private Date completedAt;
  private Date createdAt;
  private Date updatedAt;
  private Date cancelledAt;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  private IdName createdBy;
  private IdName updatedBy;
  private IdName ownerId;
  private IdName type;
  private IdName priority;
  private IdName assignedTo;
  private IdName status;
  private List<RelationEvent> relations;
  private Map<String, Map<String, String>> idNameStore = new HashMap<>();
  private Map<String, Object> customFieldValues;
  @JsonIgnore
  private Map<EntityType, List<EntityDetail>> associatedEntities;
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;


  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return this.ownerId;
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.TASK;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    this.associatedEntities = associatedEntities;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return assignedTo;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return name.substring(0, Math.min(name.length(), 510));
  }

  public Map<String, List<Long>> getRelatedToByEntityGroup(List<RelationEvent> relations) {
    return relations.stream()
        .collect(Collectors.groupingBy(relatedTo -> relatedTo.getEntityType().name(), Collectors.mapping(relatedTo1 -> relatedTo1.getEntityId(),Collectors.toList())));
  }
}
