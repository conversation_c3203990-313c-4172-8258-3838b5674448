package com.kylas.sales.workflow.domain.jsonSchema;

import java.util.Optional;
import java.util.regex.Pattern;
import org.everit.json.schema.FormatValidator;

public class CustomEmailValidator implements FormatValidator {

    private static final String EMAIL_REGEX = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}$";
    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX, Pattern.CASE_INSENSITIVE);

    @Override
    public String formatName() {
        return "emails";
    }

    @Override
    public Optional<String> validate(String subject) {
        if (EMAIL_PATTERN.matcher(subject).matches()) {
            return Optional.empty();
        } else {
            return Optional.of("false");
        }
    }
}