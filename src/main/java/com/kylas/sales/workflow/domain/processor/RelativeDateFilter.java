package com.kylas.sales.workflow.domain.processor;

import static com.kylas.sales.workflow.domain.processor.RelativeDateFilter.IntervalType.getByName;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.exception.InvalidConditionException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Getter
@AllArgsConstructor
@ToString
@Slf4j
public class RelativeDateFilter {

  private final Object value;
  private final IntervalType type;

  @JsonCreator
  public RelativeDateFilter(
      @JsonProperty("dateIntervalValue") Object value,
      @JsonProperty("dateIntervalType") String type) {
    this.value = value;
    this.type = getByName(type);
  }

  public void validate() {
    if (IntervalType.CUSTOM.equals(type) && !isValidDateTimeValue(value)) {
      log.info("Invalid datetime value for custom interval type in relative date filter, value: {}", value);
      throw new InvalidConditionException();
    }
    if (type.isUnitInterval() && !isValidUnitValue(value)) {
      log.info("Invalid value for unit interval type in relative date filter, value: {}, intervalType: {}",
          value, type);
      throw new InvalidConditionException();
    }
  }

  private boolean isValidUnitValue(Object value) {
    try {
      Integer.parseInt(value.toString());
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  private boolean isValidDateTimeValue(Object value) {
    try {
      OffsetDateTime.parse(value.toString());
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  public enum IntervalType {
    HOURS,
    DAYS,
    WEEKS,
    MONTHS,
    QUARTERS,
    YEARS,
    PREVIOUS_DAY,
    LAST_WEEK,
    LAST_MONTH,
    LAST_YEAR,
    NEXT_DAY,
    NEXT_WEEK,
    NEXT_MONTH,
    NEXT_YEAR,
    CUSTOM;

    static IntervalType getByName(String intervalName) {
      return Arrays.stream(values())
          .filter(operator -> operator.name().equalsIgnoreCase(intervalName))
          .findAny()
          .orElse(null);
    }

    public boolean isUnitInterval() {
      List<IntervalType> unitIntervals = List.of(HOURS, DAYS, WEEKS, MONTHS, QUARTERS, YEARS);
      return unitIntervals.contains(this);
    }
  }
}
