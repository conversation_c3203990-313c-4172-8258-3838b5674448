package com.kylas.sales.workflow.domain.workflow.action.webhook.attribute;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.deal.Category;
import com.kylas.sales.workflow.domain.processor.deal.Discount;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
public class DealProduct {
  @JsonProperty("id")
  private Long id;
  @JsonProperty("name")
  private String name;
  @JsonProperty("quantity")
  private Double quantity;
  @JsonProperty("price")
  private String price;
  @JsonProperty("discount")
  private Discount discount;
  @JsonProperty("description")
  private String description;
  @JsonProperty("category")
  private Category category;

  public DealProduct(Long id, String name,
      Double quantity, String price,
      Discount discount,
      String description, Category category) {
    this.id = id;
    this.name = name;
    this.quantity = ObjectUtils.isEmpty(quantity) ? 0.0 : BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_DOWN).doubleValue();
    this.price = price;
    this.discount = discount;
    this.description = description;
    this.category = category;
  }
}
