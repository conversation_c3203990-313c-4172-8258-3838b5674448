package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.Actionable;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.deal.Money;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.GPSCoordinateEvent;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class CompanyResponse extends EntityResponse implements Serializable, Actionable, EntityDetail {

  private Long id;
  private String name;
  private IdName numberOfEmployees;
  private Money annualRevenue;
  private String website;
  private IdName industry;
  private IdName businessType;
  private List<Email> emails;
  private List<PhoneNumber> phoneNumbers;
  private IdName timezone;
  private boolean dnd;
  private String address;
  private String city;
  private IdName country;
  private String state;
  private String zipcode;
  private String facebook;
  private String twitter;
  private String linkedIn;
  private IdName createdBy;
  @JsonFormat(shape = Shape.STRING)
  private Date createdAt;
  @JsonFormat(shape = Shape.STRING)
  private Date updatedAt;
  private IdName updatedBy;
  private IdName ownedBy;
  private Map<String, Object> customFieldValues;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  private IdName importedBy;

  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;

  @Override
  @JsonIgnore
  public String getEventName() {
    return null;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return this.ownedBy;
  }

  @Override
  @JsonIgnore
  public EntityType getEntityType() {
    return EntityType.COMPANY;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    return;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public Map<String, Object> getMarketPlaceTriggerValues() {
    return this.marketPlaceTriggerValues;
  }

  @Override
  public Map<EntityType, List<EntityDetail>> getAssociatedEntities() {
    return null;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return this.createdBy;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return this.name;
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    return this;
  }
}
