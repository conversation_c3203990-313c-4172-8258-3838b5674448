package com.kylas.sales.workflow.domain.exception;

import com.kylas.sales.workflow.error.DomainException;
import com.kylas.sales.workflow.error.ErrorCode;
import com.kylas.sales.workflow.error.ErrorResource;

public class InvalidActionException extends DomainException {

  public InvalidActionException() {
    super(ErrorCode.INVALID_ACTION);
  }

  public InvalidActionException(ErrorResource errorCode) {
    super(errorCode);
  }

}
