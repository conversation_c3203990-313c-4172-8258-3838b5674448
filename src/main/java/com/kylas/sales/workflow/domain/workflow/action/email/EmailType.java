package com.kylas.sales.workflow.domain.workflow.action.email;

public enum EmailType {
  RECORD_OWNER,
  RECORD_ASSIGNEE,
  RECORD_CREATED_BY,
  RECORD_UPDATED_BY,
  RECORD_PRIMARY_EMAIL,
  WOR<PERSON><PERSON><PERSON>_CREATOR,
  WOR<PERSON><PERSON><PERSON>_UPDATER,
  ALL_AVAILABLE_EMAILS,
  ALL_ASSOCIATED_CONTACTS,
  TENANT_EMAIL,
  CUSTOM_EMA<PERSON>,
  USER,
  LEAD,
  CONTACT,
  MEETING_ORGANIZER,
  RECORD_OWNER_REPORTING_MANAGER,
  RECORD_CREATED_BY_REPORTING_MANAGER,
  RECORD_UPDATED_BY_REPORTING_MANAGER,
  RECORD_ASSIGNEE_REPORTING_MANAGER
}