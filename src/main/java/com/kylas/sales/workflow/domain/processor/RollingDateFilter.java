package com.kylas.sales.workflow.domain.processor;

import static com.kylas.sales.workflow.common.dto.condition.Operator.WAS_BEFORE;
import static com.kylas.sales.workflow.common.dto.condition.Operator.WAS_IN;

import com.kylas.sales.workflow.common.dto.condition.Operator;
import com.kylas.sales.workflow.domain.processor.RelativeDateFilter.IntervalType;
import com.kylas.sales.workflow.layout.api.response.list.FieldType;
import java.time.DayOfWeek;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
public class RollingDateFilter {

  public static RollingDate from(OffsetDateTime baseDate, RelativeDateFilter relativeDateFilter,
      String timezone, Operator operator, FieldType fieldType) {
    log.info("Get rolling date filter now: {}, timezone: {}, dateIntervalType: {}, dateIntervalValue: {}, operator: {}, fieldType: {}",
        baseDate, timezone, relativeDateFilter.getType(), relativeDateFilter.getValue(), operator, fieldType);
    OffsetDateTime datetime = baseDate.atZoneSameInstant(ZoneId.of(timezone)).toOffsetDateTime();

    if (IntervalType.CUSTOM.equals(relativeDateFilter.getType())) {
      return getRollingDateForCustomDateFilter(datetime, relativeDateFilter, operator, fieldType, timezone);
    }

    if (relativeDateFilter.getType().isUnitInterval()) {
      return getRollingDateForDateFilterWithValue(datetime, relativeDateFilter, operator, fieldType);
    }

    switch (relativeDateFilter.getType()) {
      case PREVIOUS_DAY:
        OffsetDateTime previousDay = datetime.minusDays(1);
        return createRollingDateFilter(previousDay, previousDay);

      case LAST_WEEK:
        OffsetDateTime previousWeek = datetime.minusWeeks(1);
        return createRollingDateFilter(
            previousWeek.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)),
            previousWeek.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
        );

      case LAST_MONTH:
        OffsetDateTime previousMonth = datetime.minusMonths(1);
        return createRollingDateFilter(
            previousMonth.with(TemporalAdjusters.firstDayOfMonth()),
            previousMonth.with(TemporalAdjusters.lastDayOfMonth())
        );

      case LAST_YEAR:
        OffsetDateTime previousYear = datetime.minusYears(1);
        return createRollingDateFilter(
            previousYear.with(TemporalAdjusters.firstDayOfYear()),
            previousYear.with(TemporalAdjusters.lastDayOfYear())
        );

      case NEXT_DAY:
        OffsetDateTime nextDay = datetime.plusDays(1);
        return createRollingDateFilter(nextDay, nextDay);

      case NEXT_WEEK:
        OffsetDateTime nextWeek = datetime.plusWeeks(1);
        return createRollingDateFilter(
            nextWeek.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)),
            nextWeek.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
        );

      case NEXT_MONTH:
        OffsetDateTime nextMonth = datetime.plusMonths(1);
        return createRollingDateFilter(
            nextMonth.with(TemporalAdjusters.firstDayOfMonth()),
            nextMonth.with(TemporalAdjusters.lastDayOfMonth())
        );

      case NEXT_YEAR:
        OffsetDateTime nextYear = datetime.plusYears(1);
        return createRollingDateFilter(
            nextYear.with(TemporalAdjusters.firstDayOfYear()),
            nextYear.with(TemporalAdjusters.lastDayOfYear())
        );
    }
    return null;
  }

  private static RollingDate getRollingDateForCustomDateFilter(OffsetDateTime datetime, RelativeDateFilter relativeDateFilter,
      Operator operator, FieldType fieldType, String timezone) {
    OffsetDateTime customDateTime = null;
    try {
      if (FieldType.DATE_PICKER.equals(fieldType)) {
        customDateTime = ZonedDateTime.parse(relativeDateFilter.getValue().toString()).withZoneSameLocal(ZoneId.of(timezone)).toOffsetDateTime();
      } else {
        customDateTime = OffsetDateTime.parse(relativeDateFilter.getValue().toString());
      }
    } catch (Exception e) {
      log.info("Unable to parse date for relative date filter with custom interval type, value: {}", relativeDateFilter.getValue());
    }
    if (isPastFilterOperator(operator)) {
      return createRollingDateFilterBasedOnFieldType(customDateTime, datetime, fieldType);
    }
    return createRollingDateFilterBasedOnFieldType(datetime, customDateTime, fieldType);
  }

  private static RollingDate getRollingDateForDateFilterWithValue(OffsetDateTime datetime, RelativeDateFilter relativeDateFilter,
      Operator operator, FieldType fieldType) {
    OffsetDateTime filterDate = getRelativeDateForFilterWithValue(datetime, relativeDateFilter, operator);
    if (isPastFilterOperator(operator)) {
      return createRollingDateFilterBasedOnFieldType(filterDate, datetime, fieldType);
    }
    return createRollingDateFilterBasedOnFieldType(datetime, filterDate, fieldType);
  }

  private static OffsetDateTime getRelativeDateForFilterWithValue(OffsetDateTime baseDate, RelativeDateFilter relativeDateFilter, Operator operator) {
    int intervalValue = Integer.parseInt(relativeDateFilter.getValue().toString());
    switch (relativeDateFilter.getType()) {
      case HOURS:
        return calculateFilterDate(baseDate, operator, intervalValue, ChronoUnit.HOURS);
      case DAYS:
        return calculateFilterDate(baseDate, operator, intervalValue, ChronoUnit.DAYS);
      case WEEKS:
        return calculateFilterDate(baseDate, operator, intervalValue, ChronoUnit.WEEKS);
      case MONTHS:
        return calculateFilterDate(baseDate, operator, intervalValue, ChronoUnit.MONTHS);
      case QUARTERS:
        return calculateFilterDate(baseDate, operator, intervalValue * 3, ChronoUnit.MONTHS);
      case YEARS:
        return calculateFilterDate(baseDate, operator, intervalValue, ChronoUnit.YEARS);
    }
    return null;
  }

  private static OffsetDateTime calculateFilterDate(OffsetDateTime baseDate, Operator operator, int value, ChronoUnit unit) {
    if (!isPastFilterOperator(operator)) {
      return baseDate.plus(value, unit);
    }
    return baseDate.minus(value, unit);
  }

  private static boolean isPastFilterOperator(Operator operator) {
    return List.of(WAS_IN, WAS_BEFORE).contains(operator);
  }

  private static RollingDate createRollingDateFilter(OffsetDateTime start, OffsetDateTime end) {
    OffsetDateTime startDate = null;
    OffsetDateTime endDate = null;
    if (ObjectUtils.isNotEmpty(start)) {
      startDate = start.with(LocalTime.MIN).atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }
    if (ObjectUtils.isNotEmpty(end)) {
      endDate = end.with(LocalTime.MAX).atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }
    return new RollingDate(startDate, endDate);
  }

  private static RollingDate createRollingDateFilterWithoutTimeAdjustments(OffsetDateTime start, OffsetDateTime end) {
    OffsetDateTime startDate = null;
    OffsetDateTime endDate = null;
    if (ObjectUtils.isNotEmpty(start)) {
      startDate = start.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }
    if (ObjectUtils.isNotEmpty(end)) {
      endDate = end.atZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime();
    }
    return new RollingDate(startDate, endDate);
  }

  private static RollingDate createRollingDateFilterBasedOnFieldType(OffsetDateTime start, OffsetDateTime end, FieldType fieldType) {
    if (FieldType.DATE_PICKER.equals(fieldType)) {
      return createRollingDateFilter(start, end);
    }
    return createRollingDateFilterWithoutTimeAdjustments(start, end);
  }

  @Getter
  @AllArgsConstructor
  public static class RollingDate {

    private final OffsetDateTime startDate;
    private final OffsetDateTime endDate;
  }
}
