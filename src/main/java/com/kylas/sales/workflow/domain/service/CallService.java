package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.service.client.JsonRule;
import com.kylas.sales.workflow.domain.service.client.SearchResponse;
import com.kylas.sales.workflow.layout.api.response.list.ListLayout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


@Service("CALL")
public class CallService implements Searchable {

  private final String clientBasePath;

  @Autowired
  public CallService(@Value("${client.call.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }


  public Flux<FieldAttribute> getFieldAttributes(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/call-logs/fields")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(FieldAttribute.class);
  }

  @Override
  public Mono<SearchResponse> getEntityDetails(String entity, Long id, String authenticationToken, Class entityClass) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .post()
        .uri(uriBuilder -> uriBuilder.path("/v1/call-logs/search")
            .queryParam("sort", "updatedAt,desc")
            .queryParam("page", 1)
            .queryParam("size", 1)
            .build())
        .bodyValue(JsonRule.getFilterForId(id))
        .accept(MediaType.APPLICATION_JSON)
        .retrieve().bodyToMono(entityClass);
  }

  public Mono<ListLayout> getListLayoutResponse(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/call-logs/layout/list")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(ListLayout.class);
  }
}