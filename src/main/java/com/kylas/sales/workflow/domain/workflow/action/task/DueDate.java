package com.kylas.sales.workflow.domain.workflow.action.task;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import javax.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Embeddable
@Getter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class DueDate implements Serializable {

  private int days;
  private int hours;

  @JsonCreator
  public DueDate(
      @JsonProperty("days") int days,
      @JsonProperty("hours") int hours
  ) {
    this.days = days;
    this.hours = hours;
  }
}
