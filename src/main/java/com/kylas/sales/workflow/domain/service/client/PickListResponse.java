package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class PickListResponse {

  private final List<PickListValue> values;

  @JsonCreator
  public PickListResponse(
      @JsonProperty("values") @JsonAlias("picklistValues") List<PickListValue> values) {
    this.values = values;
  }
}
