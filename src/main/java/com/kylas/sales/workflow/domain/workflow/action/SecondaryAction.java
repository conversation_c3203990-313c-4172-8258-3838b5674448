package com.kylas.sales.workflow.domain.workflow.action;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SecondaryAction implements Serializable {
  private SecondaryActionType action;
  private Double numberValue;
  private TimeInterval dateValue;

  public SecondaryAction(SecondaryActionType action, Double numberValue){
    this.action = action;
    this.numberValue = numberValue;
  }

  public SecondaryAction(SecondaryActionType action, TimeInterval dateValue){
    this.action = action;
    this.dateValue = dateValue;
  }

  public enum SecondaryActionType {
    INCREASE,
    DECREASE,
    INCREASE_BY_PERCENTAGE,
    DECREASE_BY_PERCENTAGE
  }
}
