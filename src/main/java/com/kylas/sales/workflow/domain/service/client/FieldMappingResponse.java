package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldMappingResponse implements Serializable {

  private final String name;
  private final IdName value;

  @JsonCreator
  public FieldMappingResponse(@JsonProperty("name") String name, @JsonProperty("owner") IdName value) {
    this.name = name;
    this.value = value;
  }
}
