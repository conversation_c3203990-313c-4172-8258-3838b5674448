package com.kylas.sales.workflow.domain.jsonSchema;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Optional;
import org.everit.json.schema.FormatValidator;

import java.text.ParseException;
import java.text.SimpleDateFormat;

public class CustomUrlValidator implements FormatValidator {

    @Override
    public String formatName() {
        return "url";
    }
    @Override
    public Optional<String> validate(String subject) {
        try {
            new URL(subject);
            return Optional.empty();
        } catch (MalformedURLException e) {
            return Optional.of("URL provided is not in correct form");
        }
    }
}