package com.kylas.sales.workflow.domain.workflow.action;

import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.ARRAY;
import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.ID_NAME;
import static java.lang.String.valueOf;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static org.apache.commons.lang3.BooleanUtils.toBooleanObject;
import static org.apache.commons.lang3.math.NumberUtils.createNumber;
import static org.apache.commons.lang3.math.NumberUtils.isParsable;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.Pipeline;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.FromToDate;
import com.kylas.sales.workflow.domain.processor.meeting.Meeting.ScheduledDate;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.convertLead.ConversionConverter;
import com.kylas.sales.workflow.domain.workflow.action.task.DueDate;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CallLogAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MeetingAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.TaskAttribute;
import com.kylas.sales.workflow.mq.event.Metadata;
import io.micrometer.core.instrument.util.StringUtils;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Converter
@Slf4j
public class ValueConverter implements AttributeConverter<Object, String> {

  private static final ObjectMapper objectMapper = new ObjectMapper();
  @Autowired
  public ConversionConverter conversionConverter;

  @Override
  public String convertToDatabaseColumn(Object attribute) {
    if (attribute instanceof Number || attribute instanceof Boolean) {
      return valueOf(attribute);
    }
    if (attribute instanceof String) {
      return String.valueOf(attribute);
    }
    try {
      return objectMapper.writeValueAsString(attribute);
    } catch (JsonProcessingException e) {
      log.error("error while converting object to JSON String. {}", attribute);
    }
    return valueOf(attribute);
  }

  @Override
  public Object convertToEntityAttribute(String dbData) {
    return getJsonValue(dbData);
  }

  public Object getValue(EditPropertyAction editPropertyAction, Field field, EntityType entityType) {

    switch (editPropertyAction.getValueType()) {
      case ARRAY:
        return convertToListOrArray(editPropertyAction.getValue(), field);
      case OBJECT:
        return needsCustomConverter(entityType, editPropertyAction.getName()) ? customConverter(entityType, editPropertyAction.getName(),
            editPropertyAction.getValue()) : convertToObject(editPropertyAction, field, entityType);
      case PLAIN:
        return needsCustomConverter(entityType, editPropertyAction.getName()) ? customConverter(entityType, editPropertyAction.getName(),
            editPropertyAction.getValue())
            : convertToWrapper(editPropertyAction.getValue(), field);
    }
    return field.getType();
  }

  private boolean needsCustomConverter(EntityType entityType, String fieldName) {
    return isDealSourceOrCampaign(entityType, fieldName)
        || (entityType.equals(EntityType.CALL_LOG) && asList(CallLogAttribute.OUTCOME.getName(), CallLogAttribute.CALL_TYPE.getName(), CallLogAttribute.CALL_DISPOSITION.getName()).contains(fieldName))
        || (entityType.equals(EntityType.TASK) && fieldName.equalsIgnoreCase(TaskAttribute.DUE_DATE.getName()))
        || (entityType.equals(EntityType.MEETING) && asList(MeetingAttribute.ALL_DAY.getName(), MeetingAttribute.FROM.getName()).contains(fieldName));
  }

  private Object customConverter(EntityType entityType, String fieldName, Object value) {
    switch (entityType) {
      case DEAL:
        return convertPicklistTypeToIdName(value);
      case CALL_LOG:
        if (CallLogAttribute.CALL_TYPE.getName().equals(fieldName)) {
          return CallLogDetail.CallType.valueOf(value.toString().toUpperCase());
        } else if (CallLogAttribute.OUTCOME.getName().equals(fieldName)) {
          return CallLogDetail.OutcomeType.valueOf(value.toString().toUpperCase());
        } else if (CallLogAttribute.CALL_DISPOSITION.getName().equals(fieldName)) {
          return convertPicklistTypeToIdName(value);
        }
        break;
      case TASK:
        return processTaskDueDate(value);
      case MEETING:
        return processFromAndToDate(value, fieldName);
    }
    return null;
  }

  private Object processFromAndToDate(Object value, String fieldName) {
    return fieldName.equalsIgnoreCase("from") ? getScheduledDate(value) : getAllDayDate(value);
  }

  private ScheduledDate getScheduledDate(Object value) {
    try {
      FromToDate fromToDate = objectMapper.readValue(valueOf(value), FromToDate.class);
      Calendar calendar = Calendar.getInstance();
      calendar.add(Calendar.DAY_OF_MONTH, fromToDate.getFrom().getDays());
      calendar.add(Calendar.HOUR_OF_DAY, fromToDate.getFrom().getHours());
      calendar.add(Calendar.MINUTE, fromToDate.getFrom().getMinutes());
      Date fromDate = calendar.getTime();

      calendar.add(Calendar.HOUR_OF_DAY, fromToDate.getDuration().getHours());
      calendar.add(Calendar.MINUTE, fromToDate.getDuration().getMinutes());
      Date toDate = calendar.getTime();

      LocalDateTime fromLocalDateTime = LocalDateTime.ofInstant(fromDate.toInstant(), ZoneId.systemDefault());
      LocalDateTime toLocalDateTime = LocalDateTime.ofInstant(toDate.toInstant(), ZoneId.systemDefault());

      if (!fromLocalDateTime.toLocalDate().equals(toLocalDateTime.toLocalDate())) {
        toLocalDateTime = fromLocalDateTime.with(LocalTime.MAX);
        toDate = Date.from(toLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
      }

      ScheduledDate scheduledDate = new ScheduledDate(false, fromDate, toDate);
      return scheduledDate;
    } catch (JsonProcessingException e) {
      log.error("error while converting JSON String to object. {}", value);
    }
    return null;
  }

  private ScheduledDate getAllDayDate(Object value) {
    try {
      var days = objectMapper.readValue(valueOf(value), new TypeReference<Map<String, Integer>>() {
      });
      var actualDays = days.get("days");
      Calendar calendar = Calendar.getInstance();
      calendar.add(Calendar.DAY_OF_MONTH, actualDays);
      Date date = calendar.getTime();

      LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
      LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
      LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
      var from = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
      var to = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

      return new ScheduledDate(true, from, to);
    } catch (JsonProcessingException e) {
      log.error("error while converting JSON String to object. {}", value);
    }
    return null;
  }

  private Object processTaskDueDate(Object value) {
    try {
      DueDate dueDate = objectMapper.readValue(valueOf(value), DueDate.class);
      Calendar calendar = Calendar.getInstance();
      calendar.add(Calendar.DAY_OF_MONTH, dueDate.getDays());
      calendar.add(Calendar.HOUR_OF_DAY, dueDate.getHours());
      return calendar.getTime();
    } catch (JsonProcessingException e) {
      log.error("error while converting JSON String to object. {}", value);
    }
    return null;
  }

  private static List<?> convertToListOrArray(Object value, Field field) {
    try {
      if (field.getType().isAssignableFrom(List.class)) {
        ParameterizedType parameterizedType = (ParameterizedType) field.getGenericType();
        JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, (Class<?>) parameterizedType.getActualTypeArguments()[0]);
        return objectMapper.readValue(valueOf(value), type);
      }
      if (field.getType().isArray()) {
        JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, field.getType().getComponentType());
        return objectMapper.readValue(valueOf(value), type);
      }
    } catch (JsonProcessingException e) {
      log.error("error in converting to list or array due to invalid json {}", value);
    }
    return emptyList();
  }

  private static Object convertToObject(EditPropertyAction editPropertyAction, Field field, EntityType entityType) {
    Object value = editPropertyAction.getValue();
    try {
      if (entityType.getIdNameFields().contains(editPropertyAction.getName())) {
        return objectMapper.readValue(valueOf(value), IdName.class).getId();
      }
      return objectMapper.readValue(valueOf(value), field.getType());
    } catch (JsonProcessingException e) {
      log.error("error while converting object to JSON String. {}", value);
    }
    return value;
  }

  private Object convertToWrapper(Object value, Field field) {
    Class<?> fieldType = field.getType();
    String stringValue = valueOf(value);
    if (fieldType.isAssignableFrom(String.class)) {
      return stringValue;
    }
    if (fieldType.isAssignableFrom(Long.class)) {
      return createNumber(stringValue);
    }
    if (fieldType.isAssignableFrom(Double.class)) {
      return createNumber(stringValue);
    }
    if (fieldType.isAssignableFrom(Integer.class)) {
      return createNumber(stringValue);
    }
    if (fieldType.isAssignableFrom(Float.class)) {
      return createNumber(stringValue);
    }
    if (fieldType.isAssignableFrom(Boolean.class)) {
      return toBooleanObject(stringValue);
    }
    if (fieldType.isAssignableFrom(Date.class)) {
      try {
        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(stringValue);
      } catch (ParseException e) {
        log.error("{} can not be parsed to date", stringValue);
      }
    }
    return value;
  }

  private Object convertToWrapper(Object value) {
    String stringValue = valueOf(value);
    if (value instanceof Long) {
      return createNumber(stringValue);
    }
    if (value instanceof Double) {
      return createNumber(stringValue);
    }
    if (value instanceof Integer) {
      return createNumber(stringValue);
    }
    if (value instanceof Float) {
      return createNumber(stringValue);
    }
    if (value instanceof Boolean) {
      return toBooleanObject(stringValue);
    }
    if (value instanceof Date || isConvertibleToDate(stringValue)) {
      try {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return dateFormat.format(dateFormat.parse(stringValue));
      } catch (ParseException e) {
        log.error("{} can not be parsed to date", stringValue);
      }
    }
    return value;
  }

  private boolean isConvertibleToDate(String dateString) {
    try {
      Instant.parse(dateString);
      return true;
    } catch (DateTimeParseException e) {
      return false;
    }
  }

  private static Object getJsonValue(String dbData) {
    try {
      JsonNode jsonNode = objectMapper.readTree(dbData);
      if (jsonNode.isArray() || jsonNode.isObject()) {
        return jsonNode;
      }
      if (dbData.equals(jsonNode.toString())) {
        return jsonNode;
      }
      return dbData;
    } catch (JsonProcessingException e) {
      return isParsable(dbData) ? createNumber(dbData)
          : convertEntityAttributeToBoolean(dbData);
    }
  }

  private static Object convertEntityAttributeToBoolean(String dbData) {
    return dbData.equalsIgnoreCase("true") || dbData.equalsIgnoreCase("false") ? toBooleanObject(dbData) : dbData.replace("'", "");
  }

  private boolean isDealSourceOrCampaign(EntityType entityType, String name) {
    return entityType.equals(EntityType.DEAL) && (name.equalsIgnoreCase(DealAttribute.SOURCE.getName()) || name
        .equalsIgnoreCase(DealAttribute.CAMPAIGN.getName()));
  }

  private Object convertPicklistTypeToIdName(Object value) {
    String stringValue = valueOf(value);
    return new IdName(Long.parseLong(stringValue), null);
  }

  public Object convertCustomFieldValue(EditPropertyAction editPropertyAction, EntityType entityType) {
    if (editPropertyAction.getValueType().equals(ID_NAME)) {
      String value = valueOf(editPropertyAction.getValue());
      return new IdName(Long.valueOf(value), null);
    }
    if (editPropertyAction.getValueType().equals(ARRAY) && Arrays.asList(EntityType.LEAD, EntityType.CONTACT).contains(entityType)) {
      String json = valueOf(editPropertyAction.getValue());
      try {
        List<IdName> mappedList = objectMapper.readValue(json, new TypeReference<>() {});
        return mappedList.stream()
            .map(IdName::getId)
            .collect(Collectors.toList());
      } catch (JsonProcessingException e) {
        return Collections.emptyList();
      }
    }
    return convertToWrapper(editPropertyAction.getValue());
  }

  public Pipeline getPipeline(Object value) {
    try {
      return objectMapper.readValue(objectMapper.writeValueAsString(value), Pipeline.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  private Object fetchValueFromField(Metadata metadata, EntityDetail entityDetail, String copyFromField, EditPropertyAction editPropertyAction,Map<String,FieldAttribute> fieldAttributeMap,
      User tenantUser) {
    return conversionConverter.getFieldValueForTargetEntityField(
        fieldAttributeMap.get(copyFromField), fieldAttributeMap.get(editPropertyAction.getName()), entityDetail,
        tenantUser,metadata.getEntityType(), Date.from(Instant.now()));
  }

  public Map<String, Object> copyFrom(Metadata metadata,EditPropertyAction editPropertyAction,EntityDetail entityDetail,Map<String, FieldAttribute> fieldAttributeMap,User tenantUser) {
    Map<String, Object> objects = new HashMap<>();
    CopyConfiguration copyConfiguration = editPropertyAction.getCopyConfiguration();
    if (copyConfiguration == null){
      return emptyMap();
    }
    List<EditPropertyFieldMapping> copyMappings = copyConfiguration.getVariables();
    copyMappings.forEach(editPropertyCopyMapping -> {
      String variable = editPropertyCopyMapping.getVariable();
      Object value = fetchValueFromField(metadata, entityDetail, editPropertyCopyMapping.getFieldName(), editPropertyAction, fieldAttributeMap,
          tenantUser);
      if (value != null) {
        objects.put(variable, value);
      } else if (StringUtils.isNotEmpty(editPropertyCopyMapping.getIfMissing())) {
        objects.put(variable, editPropertyCopyMapping.getIfMissing());
      }
    });
    return objects;
  }
}
