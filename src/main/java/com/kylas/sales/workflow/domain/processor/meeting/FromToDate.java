package com.kylas.sales.workflow.domain.processor.meeting;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FromToDate {

  private DateObject from;
  private DurationObject duration;

  @JsonCreator
  public FromToDate(
      @JsonProperty("from") DateObject from,
      @JsonProperty("duration") DurationObject duration
  ) {
    this.from = from;
    this.duration = duration;
  }

  @Getter
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class  DateObject {

    private int days;
    private int hours;
    private int minutes;

    @JsonCreator
    public DateObject(
        @JsonProperty("days") int days,
        @JsonProperty("hours") int hours,
        @JsonProperty("minutes") int minutes
    ) {
      this.days = days;
      this.hours = hours;
      this.minutes = minutes;
    }
  }

  @Getter
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class DurationObject {

    private int hours;
    private int minutes;

    @JsonCreator
    public DurationObject(
        @JsonProperty("hours") int hours,
        @JsonProperty("minutes") int minutes
    ) {
      this.hours = hours;
      this.minutes = minutes;
    }
  }
}

