package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_COMPANY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_CONTACT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_DEAL;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ASSOCIATED_LEAD;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookService;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AssociatedEntityParameterBuilder {

  private final LeadParameterBuilder leadParameterBuilder;
  private final DealParameterBuilder dealParameterBuilder;
  private final ContactParameterBuilder contactParameterBuilder;
  private final CompanyParameterBuilder companyParameterBuilder;


  @Autowired
  public AssociatedEntityParameterBuilder(LeadParameterBuilder leadParameterBuilder, DealParameterBuilder dealParameterBuilder,
      ContactParameterBuilder contactParameterBuilder, CompanyParameterBuilder companyParameterBuilder) {
    this.leadParameterBuilder = leadParameterBuilder;
    this.dealParameterBuilder = dealParameterBuilder;
    this.contactParameterBuilder = contactParameterBuilder;
    this.companyParameterBuilder = companyParameterBuilder;
  }

  public static final List<WebhookEntity> ASSOCIATED_ENTITIES = List.of(ASSOCIATED_LEAD, ASSOCIATED_DEAL, ASSOCIATED_CONTACT, ASSOCIATED_COMPANY);

  public Map<ParamKey, List<Object>> getAssociatedEntity(Map<EntityType, List<EntityDetail>> associatedEntities, String jwtToken, WebhookAction webhookAction) {
    Map<ParamKey, List<Object>> associatedEntitiesMap = new HashMap<>();

    ASSOCIATED_ENTITIES.stream()
        .filter(webhookEntity -> isAnyAssociatedEntityParameterPresent(webhookAction, webhookEntity))
        .forEach(webhookEntity -> {
          associatedEntitiesMap.put(getParamKey(webhookEntity), List.of(new ArrayList<>()));
          var entityType = getEntity(webhookEntity);

          if (ObjectUtils.isNotEmpty(associatedEntities) && ObjectUtils.isNotEmpty(associatedEntities.get(entityType))) {
            List<EntityDetail> entityDetails = associatedEntities.get(entityType);

            getParameterBuilder(entityType).ifPresent(
                parameterBuilder -> {
                  List<Parameter> associatedParameters = getEntityWiseParameters(webhookAction, entityType);
                  appendIdParameter(associatedParameters, entityType);
                  List<Object> associatedEntitiesObject = entityDetails.stream()
                      .map(entityDetail -> buildAssociatedEntity(entityDetail, new WebhookAction(webhookAction.getMethod(), associatedParameters),
                          jwtToken, parameterBuilder))
                      .collect(Collectors.toList());
                  associatedEntitiesMap.put(getParamKey(getWebhookEntity(entityType)), List.of(associatedEntitiesObject));
                }
            );
          }
        });
    return associatedEntitiesMap;
  }

  private boolean isAnyAssociatedEntityParameterPresent(WebhookAction webhookAction, WebhookEntity webhookEntity) {
    return webhookAction.getParameters().stream().anyMatch(parameter -> parameter.getEntity().equals(webhookEntity));
  }

  private ParamKey getParamKey(WebhookEntity webhookEntity) {
    if (ASSOCIATED_LEAD.equals(webhookEntity)) {
      return new ParamKey("associatedLeads", "associatedLeadsParam");
    }
    if (ASSOCIATED_DEAL.equals(webhookEntity)) {
      return new ParamKey("associatedDeals", "associatedDealsParam");
    }
    if (ASSOCIATED_CONTACT.equals(webhookEntity)) {
      return new ParamKey("associatedContacts", "associatedContactsParam");
    }
    if (ASSOCIATED_COMPANY.equals(webhookEntity)) {
      return new ParamKey("associatedCompanies", "associatedCompaniesParam");
    }
    return null;
  }

  private EntityType getEntity(WebhookEntity webhookEntity) {
    if (ASSOCIATED_LEAD.equals(webhookEntity)) {
      return EntityType.LEAD;
    }
    if (ASSOCIATED_DEAL.equals(webhookEntity)) {
      return EntityType.DEAL;
    }
    if (ASSOCIATED_CONTACT.equals(webhookEntity)) {
      return EntityType.CONTACT;
    }
    if (ASSOCIATED_COMPANY.equals(webhookEntity)) {
      return EntityType.COMPANY;
    }
    return null;
  }

  private Optional<ParameterBuilder> getParameterBuilder(EntityType entityType) {
    final var builders = List.of(leadParameterBuilder, dealParameterBuilder, contactParameterBuilder, companyParameterBuilder);
    return builders.stream()
        .filter(parameterBuilder -> parameterBuilder.canBuild(entityType))
        .findFirst();
  }

  private void appendIdParameter(List<Parameter> parameters1, EntityType entityType) {
    if (parameters1.stream().noneMatch(parameter -> parameter.getAttribute().equalsIgnoreCase("id"))) {
      parameters1.add(new Parameter("id", getWebhookEntity(entityType), "id", true));
    }
  }

  private Object buildAssociatedEntity(EntityDetail entityDetail, WebhookAction webhookAction, String jwtToken, ParameterBuilder parameterBuilder) {
    return parameterBuilder.build(webhookAction, entityDetail, jwtToken).entrySet().stream()
        .filter(entry -> isNotEmpty(entry.getValue()))
        .collect(Collectors.toMap(entry -> entry.getKey().getName(),
            entry -> !WebhookService.attributesList.contains(entry.getKey().getAttributeName()) && entry.getValue().size() == 1 ? entry.getValue()
                .get(0) : entry.getValue()));
  }

  private List<Parameter> getEntityWiseParameters(WebhookAction webhookAction, EntityType entityType) {
    return webhookAction.getParameters().stream()
        .filter(parameter -> parameter.getEntity().equals(getWebhookEntity(entityType)))
        .collect(Collectors.toList());
  }

  private WebhookEntity getWebhookEntity(EntityType entityType) {
    if (entityType == EntityType.LEAD) {
      return WebhookEntity.ASSOCIATED_LEAD;
    }
    if (entityType == EntityType.DEAL) {
      return WebhookEntity.ASSOCIATED_DEAL;
    }
    if (entityType == EntityType.CONTACT) {
      return WebhookEntity.ASSOCIATED_CONTACT;
    }
    if (entityType == EntityType.COMPANY) {
      return WebhookEntity.ASSOCIATED_COMPANY;
    }
    return null;
  }

}
