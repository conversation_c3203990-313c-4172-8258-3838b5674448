package com.kylas.sales.workflow.domain.processor;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Pipeline {

  private final Long id;
  private String name;
  private IdName stage;
  private final List<IdName> stages;
  private final String pipelineStageReason;
  private final List<String> pipelineStageReasons;

  @JsonCreator
  public Pipeline(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("stage") IdName stage, @JsonProperty("stages") List<IdName> stages,
      @JsonProperty("pipelineStageReason") String pipelineStageReason, @JsonProperty("pipelineStageReasons") List<String> pipelineStageReasons) {
    this.id = id;
    this.name = name;
    this.stage = stage;
    this.stages = stages;
    this.pipelineStageReason = pipelineStageReason;
    this.pipelineStageReasons = pipelineStageReasons;
  }
}
