package com.kylas.sales.workflow.domain.workflow.action.task;

import com.kylas.sales.workflow.domain.processor.task.AssignedToType;
import java.io.Serializable;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Embeddable
@Getter
@NoArgsConstructor
@ToString
public class AssignedTo implements Serializable {

  @Enumerated(value = EnumType.STRING)
  private AssignedToType type;
  private Long id;
  private String name;

  public AssignedTo(AssignedToType type, Long id, String name) {
    this.type = type;
    this.id = id;
    this.name = name;
  }
}
