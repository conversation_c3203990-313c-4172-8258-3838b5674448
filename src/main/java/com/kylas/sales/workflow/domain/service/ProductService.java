package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.ProductDetails;
import com.kylas.sales.workflow.domain.service.client.ProductResponse;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class ProductService {

  private final String clientBasePath;

  @Autowired
  public ProductService(@Value("${client.product.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

    public Mono<IdName> getProductNameById(Long productId, String authenticationToken) {
        return WebConfig.getWebClientBuilder()
            .baseUrl(clientBasePath)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
            .build()
            .get()
            .uri(uriBuilder -> uriBuilder.path("/v1/products/" + productId).build())
            .accept(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(IdName.class);
    }

    public Mono<ProductResponse> getProductById(Long productId, String authenticationToken) {
        return WebConfig.getWebClientBuilder()
            .baseUrl(clientBasePath)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
            .build()
            .get()
            .uri(uriBuilder -> uriBuilder.path("/v1/products/" + productId).build())
            .accept(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(ProductResponse.class);
    }

    public Mono<List<ProductDetails>> getProductsByIds(List<Long> productIds, String authenticationToken) {
        return WebConfig.getWebClientBuilder()
            .baseUrl(clientBasePath)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
            .build()
            .get()
            .uri(uriBuilder -> uriBuilder.path("/v1/products").queryParam("id", productIds).build())
            .accept(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToFlux(ProductDetails.class)
            .collectList();
    }

}
