package com.kylas.sales.workflow.domain.workflow.action.reassign;

import static com.kylas.sales.workflow.domain.workflow.action.share.ShareActionService.RECORD_PROPERTIES_MAP;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.mq.command.EntityUpdatedCommandPublisher;
import com.kylas.sales.workflow.mq.event.Metadata;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReassignActionService {

  private final EntityUpdatedCommandPublisher entityUpdatedCommandPublisher;
  private final ObjectMapper objectMapper;
  private final ExecutionLogFacade executionLogFacade;

  @Autowired
  public ReassignActionService(EntityUpdatedCommandPublisher entityUpdatedCommandPublisher,
      ObjectMapper objectMapper,
      ExecutionLogFacade executionLogFacade) {
    this.entityUpdatedCommandPublisher = entityUpdatedCommandPublisher;
    this.objectMapper = objectMapper;
    this.executionLogFacade = executionLogFacade;
  }

  public void processReassignAction(Metadata metadata, EntityDetail entity, ReassignAction reassignAction) {
    Long ownerId = reassignAction.getOwnerId();

    if (ownerId != null) {
      handleReassignment(metadata, entity, ownerId, reassignAction.getName(), reassignAction);
      return;
    }

    String userField = RECORD_PROPERTIES_MAP.get(EntityType.valueOf(reassignAction.getName().toUpperCase()));
    IdName user = extractUserFromEntity(entity, userField);

    if (user != null && user.getId() != null) {
      handleReassignment(metadata, entity, user.getId(), user.getName(), reassignAction);
    } else {
      log.info("User not found on property {} for entityId {} userId {} and tenantId {}",
          userField, entity.getId(), metadata.getUserId(), metadata.getTenantId());
    }
  }

  private void handleReassignment(Metadata metadata, EntityDetail entity, Long ownerId, String ownerName, ReassignAction reassignAction) {
    HashMap<String, Object> reassignmentDetailsFinal = new HashMap<>();
    HashMap<String, Object> reassignmentDetails = new HashMap<>();
    reassignmentDetails.put("id", ownerId);
    reassignmentDetails.put("name", ownerName);
    reassignmentDetailsFinal.put("reassignTo", reassignmentDetails);

    ExecutionLog executionLog = executionLogFacade.createExecutionLog(reassignAction, entity, reassignmentDetailsFinal);
    metadata.setEventId(executionLog.getId());
    processEntityUpdatedCommandPublisher(metadata, entity, ownerId);
  }

  private IdName extractUserFromEntity(EntityDetail entity, String userField) {
    Map<String, Object> entityJson = objectMapper.convertValue(entity, Map.class);
    return ObjectUtils.isNotEmpty(entityJson.get(userField))
        ? objectMapper.convertValue(entityJson.get(userField), IdName.class)
        : null;
  }

  private void processEntityUpdatedCommandPublisher(Metadata metadata, EntityDetail entity, Long ownerId) {
    entityUpdatedCommandPublisher.execute(metadata, new ReassignDetail(entity.getId(), ownerId, metadata.getEntityType()), true);
  }
}