package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.MARKETPLACE_ACTION;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.validator.UrlValidator.ALLOW_ALL_SCHEMES;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import java.util.Collections;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.validator.UrlValidator;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class MarketplaceAction extends AbstractWorkflowAction implements WorkflowAction {

  @NotBlank
  private String name;

  private String description;

  @Enumerated(value = EnumType.STRING)
  private HttpMethod method;

  @NotBlank
  private String resourceId;

  @NotBlank
  private String requestUrl;

  @LazyCollection(LazyCollectionOption.FALSE)
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "marketplace_action_id")
  @Setter(AccessLevel.NONE)
  private List<MarketplaceActionParameter> parameters;

  private Long actionId;

  private String appId;

  private Boolean active;

  public MarketplaceAction(@NotBlank String name, String description, HttpMethod method, String resourceId,
      @NotBlank String requestUrl, List<MarketplaceActionParameter> parameters, Long actionId, String appId, Boolean active) {
    this.name = name;
    this.description = description;
    this.method = method;
    this.resourceId = resourceId;
    this.requestUrl = requestUrl;
    this.parameters = parameters;
    this.actionId = actionId;
    this.appId = appId;
    this.active = active;
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    return MarketplaceActionMapper.fromActionResponse(actionResponse);
  }

  public static ActionResponse toActionResponse(MarketplaceAction action) {
    return MarketplaceActionMapper.toActionResponse(action);
  }


@Override
public void setWorkflow(Workflow workflow) {
  super.setWorkflow(workflow);
}

  public void setParameters(List<MarketplaceActionParameter> parameters) {
    this.parameters.clear();
    if (parameters != null) {
      this.parameters.addAll(parameters);
    }
  }

  @Override
  public MarketplaceAction update(ActionResponse action) {
    var payload = (ActionDetail.MarketplaceAction) action.getPayload();
    MarketplaceActionMapper.validate(payload);

    this.setName(payload.getName());
    this.setDescription(payload.getDescription());
    this.setMethod(payload.getMethod());
    this.setResourceId(payload.getResourceId());
    this.setRequestUrl(payload.getRequestUrl());
    this.setParameters(payload.getParameters());
    this.setActionId(payload.getActionId());
    this.setAppId(payload.getAppId());
    this.setActive(payload.isActive());
    return this;
  }


  @Override
  public ActionType getType()
  {
    return MARKETPLACE_ACTION;
  }

  @Component
  private static class MarketplaceActionMapper {

    private static final UrlValidator urlValidator = new UrlValidator(ALLOW_ALL_SCHEMES);

    public static ActionResponse toActionResponse(MarketplaceAction action) {
      var marketplaceAction = new ActionDetail.MarketplaceAction(action.name, action.description,
          action.method,action.resourceId, action.requestUrl, action.parameters, action.getActionId(), action.getAppId(), action.getActive());
      return new ActionResponse(action.getId(), MARKETPLACE_ACTION, marketplaceAction);
    }

    public static MarketplaceAction fromActionResponse(ActionResponse action) {
      var payload = (ActionDetail.MarketplaceAction) action.getPayload();
      validate(payload);

      return new MarketplaceAction(
          payload.getName(),
          payload.getDescription(),
          payload.getMethod(),
          payload.getResourceId(),
          payload.getRequestUrl(),
          payload.getParameters(),
          payload.getActionId(),
          payload.getAppId(),
          payload.isActive());
    }

    private static void validate(ActionDetail.MarketplaceAction payload) {
      var keys =
          isNull(payload.getParameters()) ? Collections.emptyList()
              : payload.getParameters().stream().map(MarketplaceActionParameter::getName).collect(toList());
      var duplicateKeyExists = keys.size() != keys.stream().distinct().count();
      if (!urlValidator.isValid(payload.getRequestUrl()) || duplicateKeyExists) {
        throw new InvalidActionException();
      }
    }
  }
}
