package com.kylas.sales.workflow.domain.workflow.action.convertLead;

import static java.util.Collections.emptyMap;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.Pipeline;
import com.kylas.sales.workflow.domain.processor.deal.DealProduct;
import com.kylas.sales.workflow.domain.processor.deal.Discount;
import com.kylas.sales.workflow.domain.processor.deal.Discount.Type;
import com.kylas.sales.workflow.domain.processor.deal.Money;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.EmailType;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.processor.lead.PhoneType;
import com.kylas.sales.workflow.domain.processor.lead.Product;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.client.FieldMappingResponse;
import com.kylas.sales.workflow.domain.service.client.PickListValue;
import com.kylas.sales.workflow.domain.service.client.ProductDetails;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.layout.api.response.list.FieldType;
import com.kylas.sales.workflow.security.AuthService;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.zone.ZoneRulesException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ConversionConverter {
  private final ConfigService configService;
  private final AuthService authService;

  @Autowired
  public ConversionConverter(ConfigService configService, AuthService authService) {
    this.configService = configService;
    this.authService = authService;
  }

  public Object getFieldValueForTargetEntityField(FieldAttribute sourceField, FieldAttribute targetEntityField,
      EntityDetail entityDetail, User tenantUser, EntityType targetEntityType, Date currentTime) {

    var fieldValue = getFieldValue(entityDetail, sourceField.isStandard(), sourceField.getName(), sourceField.isMarketPlaceField(), currentTime);
    var targetEntityFieldType = FieldType.valueOf(targetEntityField.getType());
    var sourceEntityFieldType = FieldType.valueOf(sourceField.getType());

    if (ObjectUtils.isEmpty(fieldValue)) {
      return null;
    }

    if (FieldType.DATETIME_PICKER.equals(targetEntityFieldType) || FieldType.DATE_PICKER.equals(targetEntityFieldType)) {
      if (sourceField.isMarketPlaceField()){
        fieldValue = convertMarketPlaceDateFieldToStandardDateField(tenantUser, fieldValue, sourceEntityFieldType);
      }
      Object targetDateFieldValue = getTargetDateFieldValue(fieldValue);
      if (targetEntityFieldType.equals(FieldType.DATE_PICKER) && sourceEntityFieldType.equals(FieldType.DATETIME_PICKER)) {
        return convertToStartOfDay(targetDateFieldValue, tenantUser);
      }
      return targetDateFieldValue;
    }

    if (List.of(FieldType.TEXT_FIELD, FieldType.PARAGRAPH_TEXT).contains(targetEntityFieldType)) {
      String textValue;
      textValue = getTextValue(sourceField, tenantUser, fieldValue, entityDetail);
      if (ObjectUtils.isEmpty(textValue)) {
        return null;
      }
      textValue = textValue.replaceAll("[?*\"]", "");
      int endIndex = Math.min(FieldType.TEXT_FIELD.equals(targetEntityFieldType) ? 255 : 2550, textValue.length());
      return textValue.substring(0, endIndex);
    }

    if (FieldType.PICK_LIST.equals(targetEntityFieldType)) {
      return getTargetPickListFieldValue(sourceField, targetEntityField, entityDetail, fieldValue, targetEntityType);
    }

    if (FieldType.MULTI_PICKLIST.equals(targetEntityFieldType)) {
      return getTargetMultiPickListFieldValue(sourceField, targetEntityField, entityDetail, targetEntityType, fieldValue);
    }

    if (FieldType.NUMBER.equals(targetEntityFieldType)) {
      return getTargetNumberFieldValue(sourceField, fieldValue);
    }

    if (FieldType.MONEY.equals(targetEntityFieldType)) {
      return getTargetMoneyFieldValue(sourceField, fieldValue, targetEntityField, entityDetail);
    }

    return fieldValue;
  }

  private Object getTargetMoneyFieldValue(FieldAttribute sourceField, Object fieldValue, FieldAttribute targetEntityField, EntityDetail entityDetail) {
    var fieldType = FieldType.valueOf(sourceField.getType());

    if (fieldType.equals(FieldType.NUMBER)){
      try {
        Object moneyFieldValue = PropertyUtils.getProperty(entityDetail, targetEntityField.getName());
        Money money = (Money) moneyFieldValue;
        return new Money(money.getCurrencyId(), ((Number)fieldValue).doubleValue());
      } catch (Exception e) {
        log.error("Unable to get {} field value from details", targetEntityField.getName(), e);
      }
    }
    return fieldValue;
  }

  private static Object convertMarketPlaceDateFieldToStandardDateField(User tenantUser, Object fieldValue, FieldType sourceEntityFieldType) {
    try {
      String time = fieldValue.toString();
      DateTimeFormatter sourceFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(ZoneId.of("UTC"));
      DateTimeFormatter requiredFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("UTC"));
      Instant instant = Instant.from(sourceFormat.parse(time));
      if (sourceEntityFieldType.equals(FieldType.DATE_PICKER)) {
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of(tenantUser.getTimezone()));
        ZonedDateTime startOfDay = zonedDateTime.toLocalDate().atStartOfDay(ZoneId.of(tenantUser.getTimezone()));
        fieldValue = startOfDay.withZoneSameInstant(ZoneId.of("UTC")).format(requiredFormat);
      } else if (sourceEntityFieldType.equals(FieldType.DATETIME_PICKER)) {
        fieldValue = instant.atZone(ZoneId.of("UTC")).format(requiredFormat);
      }
    } catch (DateTimeParseException e){
      log.error("Unable to parse date: {}", fieldValue, e);
    }
    return fieldValue;
  }

  private Object getTargetNumberFieldValue(FieldAttribute sourceField, Object fieldValue) {
    var fieldType = FieldType.valueOf(sourceField.getType());
    if (fieldType == FieldType.PHONE) {
      if (sourceField.isMarketPlaceField()){
        Optional<HashMap> first = ((List<HashMap>) fieldValue).stream().findFirst();
        if (first.isPresent()){
          HashMap<String, Object> phone = first.get();
          PhoneType type = PhoneType.valueOf((String) phone.get("type"));
          String code = (String) phone.get("code");
          String value = (String) phone.get("value");
          String dialCode = (String) phone.get("dialCode");
          boolean isPrimary = (Boolean) phone.get("primary");

          PhoneNumber phoneNumber = new PhoneNumber(type, code, value, dialCode, isPrimary);
          if (phoneNumber.getDialCode() == null){
            return Long.parseLong(phoneNumber.getValue());
          } else {
            return Long.parseLong((phoneNumber.getDialCode() + phoneNumber.getValue()).substring(1));
          }
        }
      } else {
        Optional<PhoneNumber> first = Arrays.stream(((PhoneNumber[]) fieldValue)).findFirst();
        if (first.isPresent()) {
          return Long.parseLong(first.get().getDialCode() + first.get().getValue().substring(1));
        }
      }
    }
    if (fieldType == FieldType.MONEY) {
      Money money = (Money) fieldValue;
      return money.getValue().longValue();
    }
    return fieldValue;
  }

  public IdName getMappedValueForTargetEntityOwnerField(FieldMappingResponse targetEntityMappedField, User tenantUser,
      LeadDetail leadDetail) {
    switch (targetEntityMappedField.getValue().getName()) {
      case "CURRENT_USER":
        return new IdName(tenantUser.getId(), tenantUser.getName());
      case "RECORD_OWNER":
        return leadDetail.getOwnerId();
      case "RECORD_CREATED_BY":
        return leadDetail.getCreatedBy();
      default:
        return targetEntityMappedField.getValue();
    }
  }

  public Map<String, Object> getMappedProductsAndEstimatedValue(List<ProductDetails> leadProducts, List<IdName> allCurrencies, User tenantUser) {
    if (ObjectUtils.isEmpty(leadProducts)) {
      return emptyMap();
    }

    Optional<Long> tenantCurrencyId = allCurrencies.stream()
        .filter(currency -> currency.getName().equalsIgnoreCase(tenantUser.getCurrency())).findFirst().map(IdName::getId);

    if (tenantCurrencyId.isEmpty()) {
      log.info("Unable to find currency id for tenant user currency {}, tenant id: {}, user id: {}",
          tenantUser.getCurrency(), tenantUser.getTenantId(), tenantUser.getId());
      return emptyMap();
    }

    Map<String, Object> map = new HashMap<>();
    double estimatedValue = 0.0;
    List<DealProduct> dealProducts = new ArrayList<>();
    for (ProductDetails productResponse : leadProducts) {
      estimatedValue += productResponse.getPriceDetail().getValue();
      DealProduct dealProduct = new DealProduct(productResponse.getId(), productResponse.getName(),
          1.0, new Money(productResponse.getPriceDetail().getCurrency().getId(), productResponse.getPriceDetail().getValue()),
          new Discount(Type.PERCENTAGE, 0.0), null,null, null, null, null);
      dealProducts.add(dealProduct);
    }
    map.put("products", dealProducts);
    map.put("estimatedValue", new Money(tenantCurrencyId.get(), estimatedValue));
    return map;
  }

  private String convertToStartOfDay(Object dateTime, User tenantUser) {
    String tenantTimezone = tenantUser.getTimezone();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("UTC"));

    try {
      ZoneId zoneId = ZoneId.of(tenantTimezone);
      if (dateTime instanceof Date) {
        return convertDateToStartOfDay((Date) dateTime, zoneId, formatter);
      } else if (dateTime instanceof String) {
        return convertStringToStartOfDay((String) dateTime, zoneId, formatter);
      }
    } catch (ZoneRulesException e) {
      log.error("Invalid timezone: {}", tenantTimezone, e);
    } catch (Exception e) {
      log.error("Unable to convert date time value {} to start of the day", dateTime, e);
    }
    return null;
  }

  private String convertDateToStartOfDay(Date date, ZoneId zoneId, DateTimeFormatter formatter) {
    ZonedDateTime zonedDateTime = date.toInstant().atZone(zoneId);
    ZonedDateTime startOfDay = zonedDateTime.toLocalDate().atStartOfDay(zoneId);
    return formatter.format(startOfDay.toInstant());
  }

  private String convertStringToStartOfDay(String dateTime, ZoneId zoneId, DateTimeFormatter formatter) {
    try {
      DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("UTC"));
      Instant instant = Instant.from(utcFormatter.parse(dateTime));
      ZonedDateTime zonedDateTime = instant.atZone(zoneId);
      ZonedDateTime startOfDay = zonedDateTime.toLocalDate().atStartOfDay(zoneId);
      return formatter.format(startOfDay.toInstant());
    } catch (DateTimeParseException e) {
      log.error("Unable to parse date time string: {}", dateTime, e);
      return null;
    }
  }

  public Money getMappedEstimatedValue(LeadDetail leadDetail, List<IdName> allCurrencies, User tenantUser) {
    if (ObjectUtils.isNotEmpty(leadDetail.getRequirementBudget())) {
      Optional<Long> currencyId = allCurrencies.stream()
          .filter(currency -> currency.getName().equalsIgnoreCase(leadDetail.getRequirementCurrency())).findFirst().map(IdName::getId);
      if (currencyId.isEmpty()) {
        currencyId = getTenantCurrencyId(allCurrencies, tenantUser.getCurrency());
      }
      if (currencyId.isEmpty()) {
        log.info("Unable to find currency id for tenant user currency {}, tenant id: {}, user id: {}",
            tenantUser.getCurrency(), tenantUser.getTenantId(), tenantUser.getId());
        return null;
      }
      return new Money(currencyId.get(), leadDetail.getRequirementBudget());
    }

    Optional<Long> tenantCurrencyId = getTenantCurrencyId(allCurrencies, tenantUser.getCurrency());
    if (tenantCurrencyId.isEmpty()) {
      log.info("Unable to find currency id for tenant user currency {}, tenant id: {}, user id: {}",
          tenantUser.getCurrency(), tenantUser.getTenantId(), tenantUser.getId());
      return null;
    }
    return new Money(tenantCurrencyId.get(), 0.0);
  }

  private Optional<Long> getTenantCurrencyId(List<IdName> allCurrencies, String tenantCurrency) {
    return allCurrencies.stream().filter(currency -> currency.getName().equalsIgnoreCase(tenantCurrency)).findFirst().map(IdName::getId);
  }

  public Object getTargetDateFieldValue(Object fieldValue) {
    DateFormat utcDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    utcDateFormat.setLenient(false);

    try {
      if (fieldValue instanceof Date) {
        return utcDateFormat.format((Date) fieldValue);
      } else if (fieldValue instanceof String) {
        utcDateFormat.parse((String) fieldValue);
        return fieldValue;
      } else if (fieldValue instanceof Long) {
        Date date = new Date((Long) fieldValue);
        return utcDateFormat.format(date);
      } else if (fieldValue instanceof Number) {
        Date date = new Date(((Number) fieldValue).longValue());
        return utcDateFormat.format(date);
      } else {
        log.error("Unsupported field value type: {}", fieldValue.getClass().getName());
      }
    } catch (ParseException e) {
      log.error("Unable to parse date field value: {}", fieldValue, e);
    } catch (Exception e) {
      log.error("Unexpected error while processing field value: {}", fieldValue, e);
    }

    return null;
  }

  private Object getFieldValue(EntityDetail entityDetail, boolean isStandard, String fieldName, boolean isMarketPlaceField, Date currentTime) {
    try {
      if (fieldName.equals("workflowExecutionTime")){
        return currentTime;
      }
      if (isStandard) {
        if (entityDetail.getEntityType() == EntityType.DEAL && Objects.equals(fieldName, "pipelineStage")){
          Pipeline pipeline = ((Pipeline) PropertyUtils.getProperty(entityDetail, "pipeline"));
          if (pipeline != null) {
            return ((Pipeline) PropertyUtils.getProperty(entityDetail, "pipeline")).getStage();
          }
        }
        if (entityDetail.getEntityType() == EntityType.TASK && Objects.equals(fieldName, "relation")){
          return PropertyUtils.getProperty(entityDetail, "relations");
        }
        return PropertyUtils.getProperty(entityDetail, fieldName);
      } else if (isMarketPlaceField) {
        return PropertyUtils.getMappedProperty(entityDetail, "marketPlaceTriggerValues", fieldName);
      }
      return PropertyUtils.getMappedProperty(entityDetail, "customFieldValues", fieldName);
    } catch (Exception e) {
      log.error("Unable to get {} field value from details", fieldName, e);
    }
    return null;
  }

  private String getTextValue(FieldAttribute field, User tenantUser, Object fieldValue, EntityDetail entityDetail) {
    var fieldType = FieldType.valueOf(field.getType());

    switch (fieldType) {
      case PHONE:
        if (field.isMarketPlaceField()){
          List<PhoneNumber> phoneNumbers = new ArrayList<>();
          for (HashMap phone : ((List<HashMap>) fieldValue)){
            PhoneType type = PhoneType.valueOf((String) phone.get("type"));
            String code = (String) phone.get("code");
            String value = (String) phone.get("value");
            String dialCode = (String) phone.get("dialCode");
            boolean isPrimary = (Boolean) phone.get("primary");

            phoneNumbers.add(new PhoneNumber(type, code, value, dialCode, isPrimary));
          }
          return phoneNumbers.stream()
              .map(phoneNumber -> (phoneNumber.getDialCode() != null ? phoneNumber.getDialCode() : "") + phoneNumber.getValue())
              .collect(Collectors.joining(", "));
        } else {
          return Arrays.stream((PhoneNumber[]) fieldValue).map(phoneNumber -> phoneNumber.getDialCode() + phoneNumber.getValue())
              .collect(Collectors.joining(", "));
        }

      case EMAIL:
        if (field.isMarketPlaceField()){
          List<Email> emails = new ArrayList<>();
          for (HashMap email : ((List<HashMap>) fieldValue)){
            EmailType type = EmailType.valueOf((String) email.get("type"));
            String value = (String) email.get("value");
            boolean primary = (Boolean) email.get("primary");

            emails.add(new Email(type, value, primary));
          }
          return emails.stream().map(Email::getValue).collect(Collectors.joining(","));
        } else {
          return Arrays.stream((Email[]) fieldValue).map(Email::getValue).collect(Collectors.joining(", "));
        }

      case PICK_LIST:
        if (entityDetail.getEntityType() == EntityType.DEAL && !field.isStandard()){
          Object pickListValue = ((HashMap<?, ?>) fieldValue).get("name");
          return Optional.ofNullable(pickListValue).map(Object::toString).orElse(null);
        }
        if (fieldValue instanceof IdName) {
          return ((IdName) fieldValue).getName();
        }
        if (field.isInternal()) {
          return field.getPickList().getValues().stream()
              .filter(pickListValue -> pickListValue.getName().equals(fieldValue))
              .findFirst().map(PickListValue::getDisplayName).orElse(null);
        }
        if (fieldValue instanceof Number) {
          Map<String, Map<Long, String>> idNameStore= null;
          try {
            idNameStore = (Map<String, Map<Long, String>>) PropertyUtils.getNestedProperty(entityDetail,"idNameStore");
          } catch (Exception e) {
            log.error("Unable to get number field value from picklist field value {}", fieldValue, e);
          }
          if (entityDetail.getEntityType() == EntityType.TASK){
            return idNameStore.getOrDefault(field.getName(), new HashMap<>()).get(fieldValue.toString());
          } else {
            return idNameStore.getOrDefault(field.getName(), new HashMap<>()).get(((Number) fieldValue).longValue());
          }
        }
        log.info("Picklist field {} value {} is not valid", field.getName(), fieldValue);
        break;

      case MULTI_PICKLIST:
        try {
          if (entityDetail.getEntityType() == EntityType.DEAL) {
            ArrayList<HashMap<?, ?>> multiPickListValues = (ArrayList<HashMap<?, ?>>) fieldValue;
            if (multiPickListValues != null) {
              return multiPickListValues.stream()
                  .map(e -> e.get("name"))
                  .filter(Objects::nonNull)
                  .map(Object::toString)
                  .collect(Collectors.joining(", "));
            }
          } else {
            Map<String, Map<Long, String>> idNameStore = (Map<String, Map<Long, String>>) PropertyUtils.getNestedProperty(entityDetail, "idNameStore");
            return ((List<Number>) fieldValue).stream().map(value -> idNameStore.getOrDefault(field.getName(), new HashMap<>())
                .get(value.longValue())).collect(Collectors.joining(", "));
          }
        } catch (Exception e) {
          log.error("Unable to get multi picklist field value from picklist field value {}", fieldValue, e);
        }
        break;

      case PIPELINE:
        if (entityDetail.getEntityType() == EntityType.DEAL){
          return ((Pipeline) fieldValue).getName();
        } else {
          return ((IdName) fieldValue).getName();
        }

      case PIPELINE_STAGE:
        if (entityDetail.getEntityType() == EntityType.DEAL){
          return ((IdName) fieldValue).getName();
        } else {
          return fieldValue.toString();
        }

      case LOOK_UP:
        try {
          if (field.getName().equals("products")) {
            if (entityDetail.getEntityType() == EntityType.DEAL){
              List<DealProduct> products = (List<DealProduct>) PropertyUtils.getNestedProperty(entityDetail, "products");
              return products.stream().map(DealProduct::getName).collect(Collectors.joining(", "));
            } else {
              List<Product> products = (List<Product>) PropertyUtils.getNestedProperty(entityDetail, "products");
              return products.stream().map(Product::getName).collect(Collectors.joining(", "));
            }
          }
          if (field.getName().equals("associatedContacts")){
            ArrayList<IdName> idNameList = (ArrayList<IdName>) fieldValue;
            return idNameList.stream().map(IdName::getName).collect(Collectors.joining(", "));
          }
          return ((IdName) fieldValue).getName();
        }catch (Exception e){
         log.error("Unable to get product name from product lookup with value:{}",fieldValue,e);
        }
        break;

      case DATETIME_PICKER:
        Object updatedFieldValueForDateTime = fieldValue;
        if (field.isMarketPlaceField()){
          updatedFieldValueForDateTime = convertMarketPlaceDateFieldToStandardDateField(tenantUser, fieldValue, FieldType.DATETIME_PICKER);
        }
        return getDateTimeStringInTenantTimezoneAndDateFormat(updatedFieldValueForDateTime, tenantUser);

      case DATE_PICKER:
        Object updatedFieldValueForDate = fieldValue;
        if (field.isMarketPlaceField()){
          updatedFieldValueForDate = convertMarketPlaceDateFieldToStandardDateField(tenantUser, fieldValue, FieldType.DATE_PICKER);
        }
        return getDateTimeStringInTenantTimezoneAndDateFormat(updatedFieldValueForDate, tenantUser);

      case CHECKBOX:
      case TOGGLE:
        return Boolean.TRUE.equals(fieldValue) ? "Yes" : "No";
      case MONEY:
        return getFormattedCurrency(fieldValue);
      case ENTITY_LOOKUP:
        return getEntityLookupNames(fieldValue, entityDetail);
      default:
        return fieldValue.toString();
    }
    return null;
  }

  private String getEntityLookupNames(Object fieldValue, EntityDetail entityDetail) {
    if (ObjectUtils.isEmpty(fieldValue) || ObjectUtils.isEmpty(entityDetail.getAssociatedEntities())){
      return null;
    }

    return entityDetail.getAssociatedEntities().values().stream()
        .filter(entities -> entities != null && !entities.isEmpty())
        .map(entities -> entities.get(0))
        .map(EntityDetail::getEntityName)
        .sorted().collect(Collectors.joining(", "));
  }

  private String getFormattedCurrency(Object fieldValue) {
    Money money = (Money) fieldValue;
    List<IdName> currencies;
    try {
      currencies = configService.getCurrency(List.of(money.getCurrencyId()), authService.getAuthenticationToken()).collectList().block();
      if (ObjectUtils.isEmpty(currencies)){
        return money.getValue().toString();
      }
      String currencyName = currencies.get(0).getName();
      return currencyName + ' ' + money.getValue();
    } catch (Exception e){
      log.error("Unable to fetch currency {}", fieldValue, e);
    }
    return null;
  }

  private String getDateTimeStringInTenantTimezoneAndDateFormat(Object dateTime, User tenantUser) {
    String tenantDateFormat = getDateFormat(tenantUser.getDateFormat());
    String tenantTimezone = tenantUser.getTimezone();

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(tenantDateFormat);
    try {
      if (dateTime instanceof Date) {
        return ((Date) dateTime).toInstant().atZone(ZoneId.of(tenantTimezone)).toLocalDateTime().format(formatter);
      }
      if (dateTime instanceof String) {
        DateFormat utcDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        Date parsedDate = utcDateFormat.parse((String) dateTime);
        return parsedDate.toInstant().atZone(ZoneId.of(tenantTimezone)).toLocalDateTime().format(formatter);
      }
    } catch (Exception e) {
      log.error("Unable to format date time value {}", dateTime, e);
    }
    return null;
  }

  private String getDateFormat(String dateFormat) {
    if (dateFormat.contains("[at]")) {
      return "MMM dd, YYYY 'at' hh:mm a";
    }
    return "dd-MM-YYYY HH:mm:ss";
  }

  private Object getTargetPickListFieldValue(FieldAttribute sourceField, FieldAttribute targetEntityField, EntityDetail entityDetail,
      Object fieldValue, EntityType targetEntityType) {
    if(fieldValue instanceof List){
      List<?> fieldValues = ((List<?>) fieldValue);
      for (Object value : fieldValues){
        Object matchedValue = getFirstMatchedValueForPickList(sourceField, targetEntityField, entityDetail, value, targetEntityType);
        if (matchedValue != null){
          return matchedValue;
        }
      }
    } else {
      return getFirstMatchedValueForPickList(sourceField, targetEntityField, entityDetail, fieldValue, targetEntityType);
    }
    return null;
  }

  private Object getFirstMatchedValueForPickList(FieldAttribute sourceField, FieldAttribute targetEntityField, EntityDetail entityDetail, Object fieldValue,
      EntityType targetEntityType) {
    String pickListValueDisplayName = null;
    if (fieldValue instanceof IdName) {
      pickListValueDisplayName = ((IdName) fieldValue).getName();
    } else if (sourceField.isInternal()) {
      Object finalFieldValue = fieldValue;
      pickListValueDisplayName = sourceField.getPickList().getValues().stream()
          .filter(value -> value.getName().equals(finalFieldValue)).findFirst().map(PickListValue::getDisplayName).orElse(null);
    } else if (fieldValue instanceof Number) {
      Map<String, Map<Long, String>> idNameStore = emptyMap();
      try {
        idNameStore = (Map<String, Map<Long, String>>) PropertyUtils.getNestedProperty(entityDetail, "idNameStore");
      } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
        log.error("Exception occurred while getting actual value from {}", sourceField.getName(),e);
      }
      if (entityDetail.getEntityType() == EntityType.TASK) {
        pickListValueDisplayName = idNameStore.getOrDefault(sourceField.getName(), new HashMap<>())
            .get(fieldValue.toString());
      } else {
        pickListValueDisplayName = idNameStore.getOrDefault(sourceField.getName(), new HashMap<>())
            .get(((Number) fieldValue).longValue());
      }
    } else if (fieldValue instanceof HashMap){
      Object picklistValue = ((HashMap<?, ?>) fieldValue).get("name");
      pickListValueDisplayName = Optional.ofNullable(picklistValue).map(Object::toString).orElse(null);
    }

    if (ObjectUtils.isEmpty(pickListValueDisplayName)) {
      log.info("Unable to find pick list value display name for field name {}, fieldValue: {}", sourceField.getName(), fieldValue);
      return null;
    }

    Optional<PickListValue> matchedPickListValue = getMatchedPickListValue(targetEntityField, pickListValueDisplayName);
    return matchedPickListValue.map(pickListValue -> {
      Set<EntityType> entityTypes = EnumSet.of(EntityType.LEAD, EntityType.CONTACT, EntityType.TASK);
      if (entityTypes.contains(targetEntityType) && targetEntityField.isInternal()) {
        return pickListValue.getName();
      }
      if (entityTypes.contains(targetEntityType)) {
        return pickListValue.getId();
      }
      return new IdName(pickListValue.getId(), pickListValue.getDisplayName());
    }).orElse(null);
  }

  private Object getTargetMultiPickListFieldValue(FieldAttribute sourceField, FieldAttribute targetEntityField,
      EntityDetail entityDetail, EntityType targetEntityType, Object fieldValue) {
    Map<String, Map<Long, String>> idNameStore = null;
    List<String> picklistValues;
    if (entityDetail.getEntityType() == EntityType.DEAL) {
      if (fieldValue instanceof IdName) {
        picklistValues = new ArrayList<>(Arrays.asList(((IdName) fieldValue).getName().split(",")));
      } else if (fieldValue instanceof HashMap) {
        String pickListValueDisplayName = ((HashMap<?, ?>) fieldValue).get("name").toString();
        picklistValues = new ArrayList<>(Arrays.asList(pickListValueDisplayName.split(",")));
      } else {
        ArrayList<HashMap<?, ?>> multiPickListValues = (ArrayList<HashMap<?, ?>>) fieldValue;
        picklistValues = multiPickListValues.stream().map(e -> e.get("name").toString()).collect(Collectors.toList());
      }
    } else {
      try {
        idNameStore = (Map<String, Map<Long, String>>) PropertyUtils.getNestedProperty(entityDetail, "idNameStore");
      } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
        log.error("Exception occurred while getting actual value from {}", sourceField.getName(), e);
        throw new RuntimeException(e);
      }
      if (sourceField.isInternal()) {
        String internalName = (String) fieldValue;
        picklistValues = List.of(sourceField.getPickList().getValues().stream()
            .filter(value -> value.getName().equals(internalName)).findFirst().map(PickListValue::getDisplayName).orElse(null));
      } else {
        picklistValues = new ArrayList<>(idNameStore.getOrDefault(sourceField.getName(), new HashMap<>()).values());
      }
    }

    Map<String, PickListValue> targetEntityPickListFieldValuesMap = targetEntityField.getPickList().getValues().stream()
        .collect(Collectors.toMap(pickListValue1 -> pickListValue1.getDisplayName().toLowerCase(), pickListValue -> pickListValue));

    return picklistValues.stream().map(key -> targetEntityPickListFieldValuesMap.get(key.toLowerCase()))
        .filter(value -> ObjectUtils.isNotEmpty(value) && !value.isDisabled())
        .map(pickListValue -> {
          if (EntityType.CONTACT.equals(targetEntityType) || EntityType.LEAD.equals(targetEntityType)) {
            return pickListValue.getId();
          }
          return new IdName(pickListValue.getId(), pickListValue.getDisplayName());
        })
        .collect(Collectors.toList());
  }

  private Optional<PickListValue> getMatchedPickListValue(FieldAttribute targetEntityField, String picklistDisplayName) {
    return targetEntityField.getPickList().getValues().stream()
        .filter(value -> !value.isDisabled() && value.getDisplayName().equalsIgnoreCase(picklistDisplayName)).findFirst();
  }
}