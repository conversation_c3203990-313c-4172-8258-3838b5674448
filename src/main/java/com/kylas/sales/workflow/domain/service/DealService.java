package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.layout.api.response.list.ListLayout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class DealService {

  private final String clientBasePath;
  @Autowired
  public DealService(@Value("${client.deal.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }


  public Flux<FieldAttribute> getFieldAttributes(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/deals/fields")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(FieldAttribute.class);
  }

  public Mono<ListLayout> getListLayoutResponse(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/deals/layout/list")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(ListLayout.class);
  }
}
