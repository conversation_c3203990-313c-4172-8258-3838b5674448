package com.kylas.sales.workflow.domain.processor.task;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import lombok.Getter;

@Getter
public class RelationEvent {

  private final EntityType entityType;
  private final long entityId;

  @JsonCreator
  public RelationEvent(
      @JsonProperty("entityType") EntityType entityType,
      @JsonProperty("entityId") long entityId
  ) {
    this.entityType = entityType;
    this.entityId = entityId;
  }
}
