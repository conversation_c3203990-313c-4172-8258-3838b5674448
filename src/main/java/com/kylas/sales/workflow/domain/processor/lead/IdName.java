package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdName implements Serializable {

  private final Long id;
  private final String name;

  @JsonCreator
  public IdName(@JsonProperty("id") Long id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }
}
