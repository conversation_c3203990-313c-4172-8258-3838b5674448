package com.kylas.sales.workflow.domain.workflow.action;

import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.assignTo.AssignToAction;
import com.kylas.sales.workflow.domain.workflow.action.associatedActions.TriggerWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.convertLead.ConvertLeadAction;
import com.kylas.sales.workflow.domain.workflow.action.email.EmailAction;
import com.kylas.sales.workflow.domain.workflow.action.generateCallSummary.GenerateCallSummaryAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.reassign.ReassignAction;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageAction;
import com.kylas.sales.workflow.domain.workflow.action.share.ShareAction;
import com.kylas.sales.workflow.domain.workflow.action.task.CreateTaskAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;

public interface WorkflowAction {

  void setWorkflow(Workflow workflow);

  enum ActionType {
    EDIT_PROPERTY {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return EditPropertyAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return EditPropertyAction.toActionResponse((EditPropertyAction) workflowAction);
      }
    },
    WEBHOOK {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return WebhookAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return WebhookAction.toActionResponse((WebhookAction) workflowAction);
      }
    },
    MARKETPLACE_ACTION {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return MarketplaceAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return MarketplaceAction.toActionResponse((MarketplaceAction) workflowAction);
      }
    },
    REASSIGN {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return ReassignAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return ReassignAction.toActionResponse((ReassignAction) workflowAction);
      }
    },
    CREATE_TASK {
      @Override
      public AbstractWorkflowAction create(ActionResponse action) {
        return CreateTaskAction.createNew(action);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return CreateTaskAction.toActionResponse((CreateTaskAction) workflowAction);
      }
    },
    SEND_EMAIL {
      @Override
      public AbstractWorkflowAction create(ActionResponse action) {
        return EmailAction.createNew(action);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return EmailAction.toActionResponse((EmailAction) workflowAction);
      }
    },
    SHARE {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return ShareAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return ShareAction.toActionResponse((ShareAction) workflowAction);
      }
    },
    ASSIGN_TO {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return AssignToAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return AssignToAction.toActionResponse((AssignToAction) workflowAction);
      }
    },
    TRIGGER_WORKFLOW {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return TriggerWorkflowAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return TriggerWorkflowAction.toActionResponse((TriggerWorkflowAction) workflowAction);
      }
    },

    CONVERT_LEAD {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return ConvertLeadAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return ConvertLeadAction.toActionResponse((ConvertLeadAction) workflowAction);
      }
    },

    SEND_WHATSAPP_MESSAGE {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageAction.toActionResponse(
            (SendWhatsappMessageAction) workflowAction);
      }
    },

    GENERATE_CALL_SUMMARY {
      @Override
      public AbstractWorkflowAction create(ActionResponse actionResponse) {
        return com.kylas.sales.workflow.domain.workflow.action.generateCallSummary.GenerateCallSummaryAction.createNew(actionResponse);
      }

      @Override
      public ActionResponse toActionResponse(AbstractWorkflowAction workflowAction) {
        return GenerateCallSummaryAction.toActionResponse((GenerateCallSummaryAction) workflowAction);
      }
    };

    public abstract AbstractWorkflowAction create(ActionResponse action);

    public abstract ActionResponse toActionResponse(AbstractWorkflowAction workflowAction);
  }
}
