package com.kylas.sales.workflow.domain.workflow.action.webhook.attribute;

import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toList;

import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.service.CallService;
import com.kylas.sales.workflow.domain.service.CompanyService;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.DealService;
import com.kylas.sales.workflow.domain.service.EmailService;
import com.kylas.sales.workflow.domain.service.MeetingService;
import com.kylas.sales.workflow.domain.service.client.SalesService;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerVariable;
import com.kylas.sales.workflow.layout.api.response.list.Column;
import com.kylas.sales.workflow.layout.api.response.list.ListLayout;
import com.kylas.sales.workflow.security.AuthService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class AttributeFactory {

  private final ConfigService configService;
  private final AuthService authService;
  private final DealService dealService;
  private final CallService callService;
  private final MeetingService meetingService;
  private final EmailService emailService;
  private final SalesService salesService;
  private final CompanyService companyService;

  @Autowired
  public AttributeFactory(ConfigService configService, AuthService authService, DealService dealService,
      CallService callService, MeetingService meetingService, EmailService emailService, SalesService salesService,
      CompanyService companyService) {
    this.configService = configService;
    this.authService = authService;
    this.dealService = dealService;
    this.callService = callService;
    this.meetingService = meetingService;
    this.emailService = emailService;
    this.salesService = salesService;
    this.companyService = companyService;
  }

  public Mono<List<Attribute>> getUserAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return configService
        .getFieldAttributes("user", authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(UserAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name),true))
                .collect(Collectors.toList()));
  }

  public List<Attribute> getLeadAttributes(List<FieldAttribute> allLeadAttributes, List<Attribute> standardLeadAttributes) {
    List<Attribute> leadAttributes = new ArrayList<>(standardLeadAttributes);

    List<Attribute> nonStandardLeadAttributes = allLeadAttributes.stream()
        .filter(leadAttribute -> !leadAttribute.isStandard())
        .map(leadAttribute -> new Attribute(leadAttribute.getName(), StringUtils.capitalize(leadAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    leadAttributes.addAll(nonStandardLeadAttributes);
    return leadAttributes;
  }

  public List<Attribute> getMarketPlaceAttributes(List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables) {
   return marketPlaceTriggerVariables.stream()
       .map(marketPlaceTriggerVariable -> new Attribute(marketPlaceTriggerVariable.getName(),marketPlaceTriggerVariable.getDisplayName(), false))
       .collect(toList());
  }

  public List<Attribute> getContactAttributes(List<FieldAttribute> allContactAttributes, List<Attribute> standardContactAttributes) {
    List<Attribute> contactAttributes = new ArrayList<>(standardContactAttributes);

    List<Attribute> nonStandardContactAttributes = allContactAttributes.stream()
        .filter(leadAttribute -> !leadAttribute.isStandard())
        .map(contactAttribute -> new Attribute(contactAttribute.getName(), StringUtils.capitalize(contactAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    contactAttributes.addAll(nonStandardContactAttributes);
    return contactAttributes;
  }

  public List<Attribute> getDealAttributes(List<FieldAttribute> allDealAttributes, List<Attribute> standardDealAttributes) {
    List<Attribute> dealAttributes = new ArrayList<>(standardDealAttributes);

    List<Attribute> nonStandardDealAttributes = allDealAttributes.stream()
        .filter(dealAttribute -> !dealAttribute.isStandard())
        .map(dealAttribute -> new Attribute(dealAttribute.getName(), StringUtils.capitalize(dealAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    dealAttributes.addAll(nonStandardDealAttributes);
    return dealAttributes;
  }

  public List<Attribute> getCallLogAttributes(List<FieldAttribute> allDealAttributes, List<Attribute> standardCallLogAttributes) {
    List<Attribute> callAttributes = new ArrayList<>(standardCallLogAttributes);

    List<Attribute> nonStandardCallLogAttributes = allDealAttributes.stream()
        .filter(callLogAttribute -> !callLogAttribute.isStandard())
        .map(callLogAttribute -> new Attribute(callLogAttribute.getName(), StringUtils.capitalize(callLogAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    callAttributes.addAll(nonStandardCallLogAttributes);
    return callAttributes;
  }

  public List<Attribute> getCompanyAttributes(List<FieldAttribute> allCompanyAttributes, List<Attribute> standardCompanyAttributes) {
    List<Attribute> companyAttributes = new ArrayList<>(standardCompanyAttributes);

    List<Attribute> nonStandardCompanyAttributes = allCompanyAttributes.stream()
        .filter(companyAttribute -> !companyAttribute.isStandard())
        .map(dealAttribute -> new Attribute(dealAttribute.getName(), StringUtils.capitalize(dealAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    companyAttributes.addAll(nonStandardCompanyAttributes);
    return companyAttributes;
  }


  public Mono<List<FieldAttribute>> getAllEntityAttributes(String entity) {
    var authenticationToken = authService.getAuthenticationToken();
    if (entity.equals("deal")) {
      return dealService.getFieldAttributes(authenticationToken).collectList();
    }else if(entity.equals("callLog")){
      return callService.getFieldAttributes(authenticationToken).collectList();
    }else if (entity.equals("meeting")) {
      return meetingService.getFieldAttributes(authenticationToken).collectList();
    } else if (entity.equals("email")) {
      return emailService.getFieldAttributes(authenticationToken).collectList();
    }else if (entity.equals("company")) {
      return companyService.getFieldAttributes(authenticationToken).collectList();
    }
    return configService.getFieldAttributes(entity, authenticationToken).collectList();
  }

  public Mono<List<Column>> getEntityListLayoutResponse(String entity) {
    var authenticationToken = authService.getAuthenticationToken();
    Mono<ListLayout> listLayoutMono;

    switch (entity) {
      case "deal":
        listLayoutMono = dealService.getListLayoutResponse(authenticationToken);
        break;
      case "callLog":
        listLayoutMono = callService.getListLayoutResponse(authenticationToken);
        break;
      case "meeting":
        listLayoutMono = meetingService.getListLayoutResponse(authenticationToken);
        break;
      case "email":
        listLayoutMono = emailService.getListLayoutResponse(authenticationToken);
        break;
      case "task":
        listLayoutMono = configService.getListLayoutResponse(authenticationToken);
        break;
      default:
        listLayoutMono = salesService.getListLayoutResponse(entity, authenticationToken);
    }

    return listLayoutMono.map(response -> response.getPageConfig().getTableConfig().getColumns());
  }

  public Mono<List<Attribute>> getStandardLeadAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return configService
        .getFieldAttributes("lead", authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(LeadAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }


  public Mono<List<Attribute>> getStandardContactAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return configService
        .getFieldAttributes("contact", authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(ContactAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }

  public Mono<List<Attribute>> getStandardDealAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return dealService
        .getFieldAttributes(authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(DealAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }

  public Mono<List<Attribute>> getStandardCallLogAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return callService
        .getFieldAttributes(authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(CallLogAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }

  public Mono<List<Attribute>> getStandardTaskAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return configService
        .getFieldAttributes("task", authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(TaskAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name),true))
                .collect(Collectors.toList()));
  }

  public Mono<List<Attribute>> getStandardMeetingAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return meetingService
        .getFieldAttributes(authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(MeetingAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }

  public List<Attribute> getTaskAttributes(List<FieldAttribute> allTaskAttributes, List<Attribute> standardTaskAttributes) {
    List<Attribute> taskAttributes = new ArrayList<>(standardTaskAttributes);

    List<Attribute> nonStandardTaskAttributes = allTaskAttributes.stream()
        .filter(taskAttribute -> !taskAttribute.isStandard())
        .map(taskAttribute -> new Attribute(taskAttribute.getName(), StringUtils.capitalize(taskAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    taskAttributes.addAll(nonStandardTaskAttributes);
    return taskAttributes;
  }

  public List<Attribute> getMeetingAttributes(List<FieldAttribute> allMeetingAttributes, List<Attribute> standardMeetingAttributes) {
    List<Attribute> meetingAttributes = new ArrayList<>(standardMeetingAttributes);

    List<Attribute> nonStandardMeetingAttributes = allMeetingAttributes.stream()
        .filter(meetingAttribute -> !meetingAttribute.isStandard())
        .map(meetingAttribute -> new Attribute(meetingAttribute.getName(), StringUtils.capitalize(meetingAttribute.getDisplayName()), false))
        .collect(Collectors.toList());

    meetingAttributes.addAll(nonStandardMeetingAttributes);
    return meetingAttributes;
  }

  public Mono<List<Attribute>> getStandardCompanyAttributes() {
    var authenticationToken = authService.getAuthenticationToken();
    return companyService
        .getFieldAttributes(authenticationToken)
        .collectList()
        .map(entityDefinitions ->
            stream(CompanyAttribute.values())
                .map(attribute -> new Attribute(attribute.name, getDisplayName(entityDefinitions, attribute.name), true))
                .collect(Collectors.toList()));
  }

  public List<Attribute> getStandardEmailAttributes() {
    return stream(EmailAttribute.values())
        .map(emailAttribute -> new Attribute(emailAttribute.getName(), emailAttribute.getDisplayName(), true))
        .collect(toList());
  }

  private String getDisplayName(List<FieldAttribute> entityDefinitions,
      String attributeName) {
    return entityDefinitions.stream()
        .filter(entityDefinition -> entityDefinition.getName().equals(attributeName))
        .findFirst()
        .map(FieldAttribute::getDisplayName)
        .orElse(StringUtils.capitalize(attributeName));
  }

  public LeadWebhookEntity[] getEntitiesLead() {
    return LeadWebhookEntity.values();
  }

  public ContactWebhookEntity[] getEntitiesContact() {
    return ContactWebhookEntity.values();
  }

  public DealWebhookEntity[] getEntitiesDeal() {
    return DealWebhookEntity.values();
  }

  public CallLogWebhookEntity[] getEntitiesCallLog() {
    return CallLogWebhookEntity.values();
  }

  public TaskWebhookEntity[] getEntitiesTask() {
    return TaskWebhookEntity.values();
  }

  public MeetingWebhookEntity[] getEntitiesMeeting() {
    return MeetingWebhookEntity.values();
  }

  public EmailWebhookEntity[] getEntitiesEmail() {
    return EmailWebhookEntity.values();
  }

  @AllArgsConstructor
  @Getter
  public enum WebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    LEAD("Lead", EntityType.LEAD, LeadAttribute.values()),
    LEAD_OWNER("Lead Owner", EntityType.USER, UserAttribute.values()),
    CONTACT("Contact", EntityType.CONTACT, ContactAttribute.values()),
    CONTACT_OWNER("Contact Owner", EntityType.USER, UserAttribute.values()),
    DEAL("Deal", EntityType.DEAL, DealAttribute.values()),
    DEAL_OWNER("Deal Owner", EntityType.USER, UserAttribute.values()),
    CALL_LOG("Call Log", EntityType.CALL_LOG, CallLogAttribute.values()),
    LOGGED_BY("Logged By", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    TASK("Task", EntityType.TASK, TaskAttribute.values()),
    TASK_ASSIGNEE("Task Assignee", EntityType.USER, UserAttribute.values()),
    MEETING("Meeting",EntityType.MEETING, MeetingAttribute.values()),
    ORGANIZER("Organizer",EntityType.USER, UserAttribute.values()),
    ASSOCIATED_LEAD("Associated Lead", EntityType.LEAD, LeadAttribute.values()),
    ASSOCIATED_DEAL("Associated Deal", EntityType.DEAL, DealAttribute.values()),
    ASSOCIATED_CONTACT("Associated Contact", EntityType.CONTACT, ContactAttribute.values()),
    ASSOCIATED_COMPANY("Associated Company", EntityType.COMPANY, CompanyAttribute.values()),
    EMAIL("Email", EntityType.EMAIL, EmailAttribute.values()),
    MEETING_OWNER("Meeting Owner", EntityType.USER, UserAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);

    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum MarketplaceEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    LEAD("Lead", EntityType.LEAD, LeadAttribute.values()),
    LEAD_OWNER("Lead Owner", EntityType.USER, UserAttribute.values()),
    CONTACT("Contact", EntityType.CONTACT, ContactAttribute.values()),
    CONTACT_OWNER("Contact Owner", EntityType.USER, UserAttribute.values()),
    DEAL("Deal", EntityType.DEAL, DealAttribute.values()),
    DEAL_OWNER("Deal Owner", EntityType.USER, UserAttribute.values()),
    LOGGED_BY("Logged By", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    CALL_LOG("Call Log", EntityType.CALL_LOG, CallLogAttribute.values()),
    MEETING("Meeting",EntityType.MEETING, MeetingAttribute.values()),
    MEETING_OWNER("Meeting Owner", EntityType.USER, UserAttribute.values()),
    ORGANIZER("Organizer",EntityType.USER, UserAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);

    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum LeadWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    LEAD("Lead", EntityType.LEAD, LeadAttribute.values()),
    LEAD_OWNER("Lead Owner", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);


    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum ContactWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    CONTACT("Contact", EntityType.CONTACT, ContactAttribute.values()),
    CONTACT_OWNER("Contact Owner", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);


    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum DealWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    DEAL("Deal", EntityType.DEAL, DealAttribute.values()),
    DEAL_OWNER("Deal Owner", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);


    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum CallLogWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    CALL_LOG("Call Log", EntityType.CALL_LOG, CallLogAttribute.values()),
    LOGGED_BY("Logged By", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    ASSOCIATED_LEAD("Associated Lead", EntityType.LEAD, LeadAttribute.values()),
    ASSOCIATED_DEAL("Associated Deal", EntityType.DEAL, DealAttribute.values()),
    ASSOCIATED_CONTACT("Associated Contact", EntityType.CONTACT, ContactAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);


    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum TaskWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    TASK("Task", EntityType.TASK, TaskAttribute.values()),
    TASK_ASSIGNEE("Task Assignee", EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    ASSOCIATED_LEAD("Associated Lead", EntityType.LEAD, LeadAttribute.values()),
    ASSOCIATED_DEAL("Associated Deal", EntityType.DEAL, DealAttribute.values()),
    ASSOCIATED_CONTACT("Associated Contact", EntityType.CONTACT, ContactAttribute.values()),
    ASSOCIATED_COMPANY("Associated Company", EntityType.COMPANY, CompanyAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);

    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum MeetingWebhookEntity{
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    MEETING("Meeting",EntityType.MEETING, MeetingAttribute.values()),
    MEETING_OWNER("Meeting Owner", EntityType.USER, UserAttribute.values()),
    ORGANIZER("Organizer",EntityType.USER, UserAttribute.values()),
    CREATED_BY("Created By", EntityType.USER, UserAttribute.values()),
    UPDATED_BY("Updated By", EntityType.USER, UserAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    ASSOCIATED_LEAD("Associated Lead", EntityType.LEAD, LeadAttribute.values()),
    ASSOCIATED_DEAL("Associated Deal", EntityType.DEAL, DealAttribute.values()),
    ASSOCIATED_CONTACT("Associated Contact", EntityType.CONTACT, ContactAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);

    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum EmailWebhookEntity {
    CUSTOM("Custom Parameter", EntityType.CUSTOM, null),
    EMAIL("Email", EntityType.EMAIL, EmailAttribute.values()),
    TENANT("Tenant", EntityType.TENANT, TenantAttribute.values()),
    ASSOCIATED_LEAD("Associated Lead", EntityType.LEAD, LeadAttribute.values()),
    ASSOCIATED_DEAL("Associated Deal", EntityType.DEAL, DealAttribute.values()),
    ASSOCIATED_CONTACT("Associated Contact", EntityType.CONTACT, ContactAttribute.values()),
    MARKETPLACE_TRIGGER("Marketplace Trigger", EntityType.MARKETPLACE_TRIGGER, null);

    private final String displayName;
    private final EntityType type;
    private final EntityAttribute[] entityAttributes;
  }

  @AllArgsConstructor
  @Getter
  public enum UserAttribute implements EntityAttribute {
    ID("id","id"),
    SALUTATION("salutation", "salutation.name"),
    FIRST_NAME("firstName", "firstName"),
    LAST_NAME("lastName", "lastName"),
    EMAIL("email", "email"),
    PHONE_NUMBERS("phoneNumbers", "phoneNumbers"),
    DESIGNATION("designation", "designation"),
    DEPARTMENT("department", "department"),
    ACTIVE("active", "active"),
    CURRENCY("currency", "currency"),
    TIMEZONE("timezone", "timezone"),
    SIGNATURE("signature", "signature"),
    LANGUAGE("language", "language"),
    CREATED_BY("createdBy", "createdBy.name"),
    UPDATED_BY("updatedBy", "updatedBy.name");

    private final String name;
    private final String pathToField;
  }

  @AllArgsConstructor
  @Getter
  public enum LeadAttribute implements EntityAttribute {
    ID("id", "id"),
    SALUTATION("salutation", "salutation.name"),
    FIRST_NAME("firstName", "firstName"),
    LAST_NAME("lastName", "lastName"),
    EMAILS("emails", "emails"),
    PHONE_NUMBERS("phoneNumbers", "phoneNumbers"),
    PIPELINE("pipeline", "pipeline.name"),
    STATUS("pipelineStage", "pipelineStage.name"),
    TIMEZONE("timezone", "timezone"),
    ADDRESS("address", "address"),
    CITY("city", "city"),
    STATE("state", "state"),
    COUNTRY("country", "country"),
    ZIPCODE("zipcode", "zipcode"),
    FACEBOOK("facebook", "facebook"),
    TWITTER("twitter", "twitter"),
    LINKEDIN("linkedIn", "linkedIn"),
    COMPANY_NAME("companyName", "companyName"),
    DEPARTMENT("department", "department"),
    DESIGNATION("designation", "designation"),
    COMPANY_INDUSTRY("companyIndustry", "companyIndustry"),
    COMPANY_BUSINESS_TYPE("companyBusinessType", "companyBusinessType"),
    COMPANY_EMPLOYEES("companyEmployees", "companyEmployees.name"),
    COMPANY_ANNUAL_REVENUE("companyAnnualRevenue", "companyAnnualRevenue"),
    COMPANY_WEBSITE("companyWebsite", "companyWebsite"),
    COMPANY_PHONES("companyPhones", "companyPhones"),
    REQUIREMENT_NAME("requirementName", "requirementName"),
    REQUIREMENT_PRODUCTS("products", "products"),
    REQUIREMENT_CURRENCY("requirementCurrency", "requirementCurrency"),
    REQUIREMENT_BUDGET("requirementBudget", "requirementBudget"),
    CAMPAIGN("campaign", "campaign.name"),
    SOURCE("source", "source.name"),
    CREATED_BY("createdBy", "createdBy.name"),
    CREATED_AT("createdAt", "createdAt"),
    UPDATED_BY("updatedBy", "updatedBy.name"),
    UPDATED_AT("updatedAt", "updatedAt"),
    CREATED_VIA_ID("createdViaId", "createdViaId"),
    CREATED_VIA_NAME("createdViaName", "createdViaName"),
    CREATED_VIA_TYPE("createdViaType", "createdViaType"),
    UPDATED_VIA_ID("updatedViaId", "updatedViaId"),
    UPDATED_VIA_NAME("updatedViaName", "updatedViaName"),
    UPDATED_VIA_TYPE("updatedViaType", "updatedViaType"),
    SUB_SOURCE("subSource", "subSource"),
    UTM_SOURCE("utmSource", "utmSource"),
    UTM_CAMPAIGN("utmCampaign", "utmCampaign"),
    UTM_CONTENT("utmContent", "utmContent"),
    UTM_MEDIUM("utmMedium", "utmMedium"),
    UTM_TERM("utmTerm", "utmTerm"),
    OWNED_BY("ownerId","ownerId.name"),
    COMPANY_ADDRESS("companyAddress","companyAddress"),
    COMPANY_CITY("companyCity", "companyCity"),
    COMPANY_STATE("companyState", "companyState"),
    COMPANY_ZIPCODE("companyZipcode", "companyZipcode"),
    COMPANY_COUNTRY("companyCountry", "companyCountry"),
    ACTUAL_CLOSURE("actualClosureDate", "actualClosureDate"),
    EXPECTED_CLOSURE("expectedClosureOn", "expectedClosureOn"),
    CONVERTED_BY("convertedBy", "convertedBy.name"),
    IMPORTED_BY("importedBy","importedBy.name"),
    PIPELINE_STAGE_REASON("pipelineStageReason", "pipelineStageReason"),
    SCORE("score", "score");

    private final String name;
    private final String pathToField;
  }


  @AllArgsConstructor
  @Getter
  public enum ContactAttribute implements EntityAttribute {
    ID("id", "id"),
    SALUTATION("salutation", "salutation.name"),
    FIRST_NAME("firstName", "firstName"),
    LAST_NAME("lastName", "lastName"),
    EMAILS("emails", "emails"),
    PHONE_NUMBERS("phoneNumbers", "phoneNumbers"),
    TIMEZONE("timezone", "timezone"),
    ADDRESS("address", "address"),
    CITY("city", "city"),
    STATE("state", "state"),
    COUNTRY("country", "country"),
    ZIPCODE("zipcode", "zipcode"),
    FACEBOOK("facebook", "facebook"),
    TWITTER("twitter", "twitter"),
    LINKEDIN("linkedin", "linkedin"),
    COMPANY("company", "company.name"),
    DEPARTMENT("department", "department"),
    DESIGNATION("designation", "designation"),
    CREATED_BY("createdBy", "createdBy.name"),
    CREATED_AT("createdAt", "createdAt"),
    UPDATED_BY("updatedBy", "updatedBy.name"),
    UPDATED_AT("updatedAt", "updatedAt"),
    STAKEHOLDER("stakeholder", "stakeholder"),
    DO_NOT_DISTURB("dnd", "dnd"),
    CREATED_VIA_ID("createdViaId", "createdViaId"),
    CREATED_VIA_NAME("createdViaName", "createdViaName"),
    CREATED_VIA_TYPE("createdViaType", "createdViaType"),
    UPDATED_VIA_ID("updatedViaId", "updatedViaId"),
    UPDATED_VIA_NAME("updatedViaName", "updatedViaName"),
    UPDATED_VIA_TYPE("updatedViaType", "updatedViaType"),
    CAMPAIGN("campaign", "campaign.name"),
    SOURCE("source", "source.name"),
    SUB_SOURCE("subSource", "subSource"),
    UTM_SOURCE("utmSource", "utmSource"),
    UTM_CAMPAIGN("utmCampaign", "utmCampaign"),
    UTM_CONTENT("utmContent", "utmContent"),
    UTM_MEDIUM("utmMedium", "utmMedium"),
    UTM_TERM("utmTerm", "utmTerm"),
    OWNER_ID("ownerId", "ownerId.name"),
    SCORE("score","score");

    private final String name;
    private final String pathToField;
  }

  @AllArgsConstructor
  @Getter
  public enum DealAttribute implements EntityAttribute {
    ID("id", "id","Id"),
    NAME("name", "name","Name"),
    ESTIMATED_VALUE("estimatedValue", "estimatedValue","Estimated Value"),
    ACTUAL_VALUE("actualValue", "actualValue","Actual Value"),
    ESTIMATED_CLOSURE("estimatedClosureOn", "estimatedClosureOn","Estimated Closure Date"),
    ACTUAL_CLOSURE("actualClosureDate", "actualClosureDate", "Actual Closure Date"),
    ASSOCIATED_CONTACTS("associatedContacts", "associatedContacts.name", "Associated Contacts"),
    PIPELINE("pipeline", "pipeline.name", "Pipeline"),
    STATUS("pipelineStage", "pipeline.stage.name", "Pipeline Stage"),
    COMPANY("company", "company.name", "Company"),
    OWNED_BY("ownedBy", "ownedBy.name", "Owned By"),
    CREATED_BY("createdBy", "createdBy.name", "Created By"),
    CREATED_AT("createdAt", "createdAt", "Created At"),
    UPDATED_BY("updatedBy", "updatedBy.name", "Updated By"),
    UPDATED_AT("updatedAt", "updatedAt", "Updated At"),
    CAMPAIGN("campaign", "campaign.name", "Campaign"),
    SOURCE("source", "source.name", "Source"),
    PRODUCTS("products", "products", "Product or Service"),
    CREATED_VIA_ID("createdViaId", "createdViaId", "Created Via Id"),
    CREATED_VIA_NAME("createdViaName", "createdViaName", "Created Via Name"),
    CREATED_VIA_TYPE("createdViaType", "createdViaType", "Created Via Type"),
    UPDATED_VIA_ID("updatedViaId", "updatedViaId", "Updated Via Id"),
    UPDATED_VIA_NAME("updatedViaName", "updatedViaName", "Updated Via Name"),
    UPDATED_VIA_TYPE("updatedViaType", "updatedViaType", "Updated Via Type"),
    SUB_SOURCE("subSource", "subSource", "Sub Source"),
    UTM_SOURCE("utmSource", "utmSource", "UTM Source"),
    UTM_CAMPAIGN("utmCampaign", "utmCampaign", "UTM Campaign"),
    UTM_CONTENT("utmContent", "utmContent", "UTM Content"),
    UTM_MEDIUM("utmMedium", "utmMedium", "UTM Medium"),
    UTM_TERM("utmTerm", "utmTerm", "UTM Term"),
    FORECASTING_TYPE("forecastingType", "forecastingType", "Forecasting Type"),
    PIPELINE_STAGE_REASON("pipelineStageReason", "pipelineStageReason", "Pipeline Stage Reason"),
    TASK_DUE_ON("taskDueOn", "taskDueOn", "Task Due On"),
    MEETING_SCHEDULED_ON("meetingScheduledOn", "meetingScheduledOn", "Meeting Scheduled On"),
    LATEST_ACTIVITY_CREATED_AT("latestActivityCreatedAt", "latestActivityCreatedAt", "Latest Activity On"),
    IS_NEW("isNew", "isNew", "Is New"),
    SCORE("score","score", "Score");

    private final String name;
    private final String pathToField;
    private final String displayName;
  }

  @AllArgsConstructor
  @Getter
  public enum CallLogAttribute implements EntityAttribute {
    ID("id", "id","Id"),
    OUTCOME("outcome", "outcome", "Outcome"),
    START_TIME("startTime","startTime", "StartTime"),
    CALL_TYPE("callType", "callType", "Call Type"),
    DURATION("duration","duration", "Duration"),
    IS_MANUAL("isManual", "isManual", "Is Manual"),
    ORIGINATOR ("originator", "originator", "Originator"),
    RECEIVER("receiver", "receiver", "RECEIVER"),
    CALL_RECORDING("callRecording","callRecording", "Call Recording"),
    DEVICE_ID("deviceId", "deviceId", "Device ID"),
    CREATED_BY("createdBy", "createdBy.name", "Created By"),
    UPDATED_BY("updatedBy", "updatedBy.name", "Updated By"),
    LOGGED_BY("owner", "owner.name", "Logged By"),
    CREATED_AT("createdAt", "createdAt", "Created At"),
    UPDATED_AT("updatedAt", "updatedAt", "Updated At"),
    RELATED_TO("relatedTo","relatedTo", "Related To"),
    CALL_DISPOSITION("callDisposition", "callDisposition.name", "Call Disposition"),
    CUSTOMER_EMOTION("customerEmotion", "customerEmotion", "Customer Emotion"),
    OVERALL_SENTIMENT("overallSentiment", "overallSentiment", "Overall Sentiment"),
    CALL_SUMMARY("callSummary", "callSummary", "Call Summary");

    private final String name;
    private final String pathToField;
    private final String displayName;
  }

  @AllArgsConstructor
  @Getter
  public enum TaskAttribute implements EntityAttribute {
    ID("id", "id"),
    NAME("name", "name"),
    DESCRIPTION("description", "description"),
    TYPE("type", "type.name"),
    DUE_DATE("dueDate", "dueDate"),
    STATUS("status", "status.name"),
    PRIORITY("priority", "priority.name"),
    ASSIGNED_TO("assignedTo", "assignedTo.name"),
    REMINDER("reminder", "reminder"),
    RELATION("relation", "relations"),
    OWNER_ID("ownerId", "ownerId.name"),
    CREATED_AT("createdAt", "createdAt"),
    UPDATED_AT("updatedAt", "updatedAt"),
    CREATED_BY("createdBy", "createdBy.name"),
    UPDATED_BY("updatedBy", "updatedBy.name"),
    COMPLETED_AT("completedAt", "completedAt"),
    CANCELLED_AT("cancelledAt", "cancelledAt"),
    ORIGINAL_DUE_DATE("originalDueDate", "originalDueDate"),
    CREATED_VIA_ID("createdViaId", "createdViaId"),
    CREATED_VIA_NAME("createdViaName", "createdViaName"),
    CREATED_VIA_TYPE("createdViaType", "createdViaType"),
    UPDATED_VIA_ID("updatedViaId", "updatedViaId"),
    UPDATED_VIA_NAME("updatedViaName", "updatedViaName"),
    UPDATED_VIA_TYPE("updatedViaType", "updatedViaType");

    private final String name;
    private final String pathToField;

  }

  @AllArgsConstructor
  @Getter
  public enum MeetingAttribute implements EntityAttribute{
    ID("id", "id"),
    TITLE("title", "title"),
    DESCRIPTION("description", "description"),
    STATUS("status", "status"),
    MEDIUM("medium","medium"),
    ALL_DAY("allDay", "allDay"),
    FROM("from","from"),
    TO("to","to"),
    CREATED_AT("createdAt", "createdAt"),
    CREATED_BY("createdBy", "createdBy.name"),
    UPDATED_AT("updatedAt", "updatedAt"),
    UPDATED_BY("updatedBy", "updatedBy.name"),
    CANCELLED_AT("cancelledAt", "cancelledAt"),
    CANCELLED_BY("cancelledBy", "cancelledBy.name"),
    CONDUCTED_AT("conductedAt", "conductedAt"),
    CONDUCTED_BY("conductedBy", "conductedBy.name"),
    IMPORTED_BY("importedBy", "importedBy.name"),
    TIMEZONE("timezone", "timezone.name"),
    INVITEES("participants", "participants"),
    JOINING_LINK("providerLink","providerLink"),
    ORGANIZER("organizer", "organizer"),
    OWNER("owner","owner.name"),
    LOCATION("location", "location"),
    RELATED_TO("relatedTo", "relatedTo"),
    CHECKED_IN_AT("checkedInAt", "checkedInDetails.at"),
    CHECKED_OUT_AT("checkedOutAt", "checkedOutDetails.at");

    private final String name;
    private final String pathToField;
  }

  @AllArgsConstructor
  @Getter
  public enum EmailAttribute implements EntityAttribute {
    ID("id", "id", "Id"),
    BODY("body", "body", "Body"),
    SUBJECT("subject", "subject", "Subject"),
    MAILED_AT("createdAt", "createdAt", "Mailed At"),
    EMAIL_TYPE("direction", "direction", "Email Type"),
    OPENED_AT("openedAt", "openedAt", "Opened At"),
    SENT_BY("sentBy", "sentBy", "Sent By"),
    RECEIVED_BY("receivedBy", "receivedBy", "Received By"),
    TRACKING_ENABLED("trackingEnabled", "trackingEnabled", "Tracking Enabled"),
    STATUS("status", "status", "Status"),
    RELATED_TO("relatedTo", "relatedTo", "Related To"),
    ATTACHMENTS("attachments","attachments", "Attachments"),
    CLICKED_AT("clickedAt", "linksClickedAt", "Clicked At");

    private final String name;
    private final String pathToField;
    private final String displayName;
  }

  @AllArgsConstructor
  @Getter
  public enum CompanyAttribute implements EntityAttribute {
    ID("id", "id", "ID"),
    NAME("name", "name", "Name"),
    NUMBER_OF_EMPLOYEES("numberOfEmployees", "numberOfEmployees.name", "Number of Employees"),
    ANNUAL_REVENUE("annualRevenue", "annualRevenue", "Annual Revenue"),
    WEBSITE("website", "website", "Website"),
    INDUSTRY("industry", "industry.name", "Industry"),
    BUSINESS_TYPE("businessType", "businessType.name", "Business Type"),
    TIMEZONE("timezone", "timezone.name", "Timezone"),
    DND("dnd", "dnd", "Do Not Disturb"),
    ADDRESS("address", "address", "Address"),
    CITY("city", "city", "City"),
    COUNTRY("country", "country.name", "Country"),
    STATE("state", "state", "State"),
    ZIPCODE("zipcode", "zipcode", "Zipcode"),
    FACEBOOK("facebook", "facebook", "Facebook"),
    TWITTER("twitter", "twitter", "Twitter"),
    LINKEDIN("linkedIn", "linkedIn", "LinkedIn"),
    CREATED_AT("createdAt", "createdAt", "Created At"),
    UPDATED_AT("updatedAt", "updatedAt", "Updated At"),
    CREATED_BY("createdBy", "createdBy.name", "Created By"),
    UPDATED_BY("updatedBy", "updatedBy.name", "Updated By"),
    CREATED_VIA_ID("createdViaId", "createdViaId", "Created Via Id"),
    CREATED_VIA_NAME("createdViaName", "createdViaName", "Created Via Name"),
    CREATED_VIA_TYPE("createdViaType", "createdViaType", "Created Via Type"),
    UPDATED_VIA_ID("updatedViaId", "updatedViaId", "Updated Via Id"),
    UPDATED_VIA_NAME("updatedViaName", "updatedViaName", "Updated Via Name"),
    UPDATED_VIA_TYPE("updatedViaType", "updatedViaType", "Updated Via Type"),
    IMPORTED_BY("importedBy", "importedBy.name", "Imported By"),
    OWNED_BY("ownedBy", "ownedBy.name", "Owner"),
    PHONE_NUMBERS("phoneNumbers", "phoneNumbers", "Phone Numbers"),
    EMAILS("emails", "emails", "Emails");

    private final String name;
    private final String pathToField;
    private final String displayName;
  }
}
