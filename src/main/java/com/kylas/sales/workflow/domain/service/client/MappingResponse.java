package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MappingResponse implements Serializable {

  private final FieldMappingResponse leadField;
  private final List<FieldMappingResponse> dealFields;
  private final List<FieldMappingResponse> contactFields;
  private final List<FieldMappingResponse> companyFields;

  @JsonCreator
  public MappingResponse(@JsonProperty("leadField") FieldMappingResponse leadField,
      @JsonProperty("dealFields") List<FieldMappingResponse> dealFields,
      @JsonProperty("contactFields") List<FieldMappingResponse> contactFields,
      @JsonProperty("companyFields") List<FieldMappingResponse> companyFields) {
    this.leadField = leadField;
    this.dealFields = dealFields;
    this.contactFields = contactFields;
    this.companyFields = companyFields;
  }
}
