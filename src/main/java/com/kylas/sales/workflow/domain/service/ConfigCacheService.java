package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.CacheFacade;
import com.kylas.sales.workflow.domain.service.client.ConversionMappingResponse;
import com.kylas.sales.workflow.security.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class ConfigCacheService {

  private final ConfigService configService;
  private final CacheFacade cacheFacade;
  private final AuthService authService;

  @Autowired
  public ConfigCacheService(ConfigService configService, CacheFacade cacheFacade, AuthService authService) {
    this.configService = configService;
    this.cacheFacade = cacheFacade;
    this.authService = authService;
  }

  public Mono<ConversionMappingResponse> getConversionMapping(long tenantId) {
    ConversionMappingResponse conversionMapping = cacheFacade.getConversionMapping(tenantId);
    if (conversionMapping != null) {
      return Mono.just(conversionMapping);
    }
    return configService.getConversionMapping(authService.getAuthenticationToken())
        .map(mappingResponse -> cacheFacade.putConversionMapping(tenantId, mappingResponse));
  }

  public void refreshConversionMapping(long tenantId) {
    cacheFacade.refreshConversionMapping(tenantId);
  }
}
