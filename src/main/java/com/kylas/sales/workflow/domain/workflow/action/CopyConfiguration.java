package com.kylas.sales.workflow.domain.workflow.action;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CopyConfiguration implements Serializable {
  private String text;
  private List<EditPropertyFieldMapping> variables;
  private SecondaryAction secondaryAction;

  public CopyConfiguration(String text, List<EditPropertyFieldMapping> variables){
    this.text = text;
    this.variables = variables;
  }
}
