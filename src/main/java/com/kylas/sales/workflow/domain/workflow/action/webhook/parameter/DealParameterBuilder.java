package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.EntityType.DEAL;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.ACTUAL_VALUE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.ASSOCIATED_CONTACTS;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.COMPANY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.ESTIMATED_VALUE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealAttribute.PRODUCTS;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CREATED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.DEAL_OWNER;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.UPDATED_BY;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.springframework.http.HttpMethod.GET;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.deal.DealDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.CompanyService;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.ProductService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.service.client.ProductResponse;
import com.kylas.sales.workflow.domain.service.client.SalesService;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.user.User;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MarketplaceEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.DealProduct;
import com.kylas.sales.workflow.security.AuthService;
import com.kylas.sales.workflow.security.InternalAuthProvider;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class DealParameterBuilder extends ParameterBuilder {

  private final SalesService salesService;
  private final ProductService productService;
  private final CompanyService companyService;
  private final AuthService authService;
  private final InternalAuthProvider internalAuthProvider;

  @Autowired
  public DealParameterBuilder(UserService userService, ConfigService configService,
      SalesService salesService, ProductService productService, CompanyService companyService,
      AuthService authService, InternalAuthProvider internalAuthProvider) {
    super(userService, configService);
    this.salesService = salesService;
    this.productService = productService;
    this.companyService = companyService;
    this.authService = authService;
    this.internalAuthProvider = internalAuthProvider;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return DEAL.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    DealDetail deal = (DealDetail) entityDetail;

    return Mono
        .zip(
            getUserIfRequired(webhookAction, jwtToken, DEAL_OWNER, deal.getOwnedBy()),
            getUserIfRequired(webhookAction, jwtToken, CREATED_BY, deal.getCreatedBy()),
            getUserIfRequired(webhookAction, jwtToken, UPDATED_BY, deal.getUpdatedBy()),
            getTenantIfRequired(webhookAction, jwtToken),
            getCurrencyIfRequired(webhookAction, jwtToken, deal.getActualValue()),
            getCurrencyIfRequired(webhookAction, jwtToken, deal.getEstimatedValue()),
            getAssociatedContactsParamValue(webhookAction.getMethod(), deal, jwtToken),
            getProductParamValue(webhookAction.getMethod(), deal)
        )
        .zipWith(getCompanyParamValue(webhookAction.getMethod(), deal))
        .zipWith(getCurrenciesForProducts(webhookAction.getMethod(), deal, jwtToken))
        .map(tuple ->
            webhookAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = deal;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  EntityType entityType = parameter.getEntity().getType();
                  if (parameter.getEntity().equals(DEAL_OWNER)) {
                    entity = UserDetails.from(tuple.getT1().getT1().getT1());
                  } else if (parameter.getEntity().equals(CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT1().getT1().getT2());
                  } else if (parameter.getEntity().equals(UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT1().getT1().getT3());
                  } else if (parameter.getEntity().equals(TENANT)) {
                    entity = tuple.getT1().getT1().getT4();
                  } else if (entityType.equals(DEAL) && parameter.getAttribute().equals(ACTUAL_VALUE.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedActualMoneyText(deal, tuple.getT1().getT1().getT5()));
                  } else if (entityType.equals(DEAL) && parameter.getAttribute().equals(ESTIMATED_VALUE.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedEstimatedMoneyText(deal, tuple.getT1().getT1().getT6()));
                  } else if (entityType.equals(DEAL) && parameter.getAttribute().equals(ASSOCIATED_CONTACTS.getName())) {
                    return new SimpleEntry<>(paramKey, tuple.getT1().getT1().getT7());
                  } else if (entityType.equals(DEAL) && parameter.getAttribute().equals(COMPANY.getName())) {
                    return new SimpleEntry<>(paramKey, tuple.getT1().getT2());
                  } else if (entityType.equals(DEAL) && parameter.getAttribute().equals(PRODUCTS.getName())) {
                    return new SimpleEntry<>(paramKey,
                        getFormattedProducts(webhookAction.getMethod(), deal, tuple.getT2(), tuple.getT1().getT1().getT8()));
                  }
                  else if(entityType.equals(DEAL) &&  ObjectUtils.isNotEmpty(deal.getCustomFieldValues()) && (deal.getCustomFieldValues().get(parameter.getAttribute())) instanceof List){
                    List<Map<String,Object>> values= (List<Map<String, Object>>) (deal.getCustomFieldValues().get(parameter.getAttribute()));
                    List<Object> valueNames =values.stream().map(value->(String)value.get("name")).collect(toList());
                    return new SimpleEntry<>(paramKey, valueNames);
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entityDetail, String jwtToken) {

    DealDetail deal = (DealDetail) entityDetail;

    return Mono
        .zip(
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.DEAL_OWNER, deal.getOwnedBy()),
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.CREATED_BY, deal.getCreatedBy()),
            getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.UPDATED_BY, deal.getUpdatedBy()),
            getTenantIfRequired(marketplaceAction, jwtToken),
            getCurrencyIfRequired(marketplaceAction, jwtToken, deal.getActualValue()),
            getCurrencyIfRequired(marketplaceAction, jwtToken, deal.getEstimatedValue()),
            getAssociatedContactsParamValue(marketplaceAction.getMethod(), deal, jwtToken),
            getProductParamValue(marketplaceAction.getMethod(), deal)
        )
        .zipWith(getCurrenciesForProducts(marketplaceAction.getMethod(), deal, jwtToken))
        .map(tuple ->
            marketplaceAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = deal;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  if (parameter.getEntity().equals(MarketplaceEntity.DEAL_OWNER)) {
                    entity = UserDetails.from(tuple.getT1().getT1());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.CREATED_BY)) {
                    entity = UserDetails.from(tuple.getT1().getT2());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.UPDATED_BY)) {
                    entity = UserDetails.from(tuple.getT1().getT3());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.TENANT)) {
                    entity = tuple.getT1().getT4();
                  } else if (parameter.getEntity().equals(MarketplaceEntity.DEAL) && parameter.getAttribute().equals(ACTUAL_VALUE.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedActualMoneyText(deal, tuple.getT1().getT5()));
                  } else if (parameter.getEntity().equals(MarketplaceEntity.DEAL) && parameter.getAttribute().equals(ESTIMATED_VALUE.getName())) {
                    return new SimpleEntry<>(paramKey, getFormattedEstimatedMoneyText(deal, tuple.getT1().getT6()));
                  } else if (parameter.getEntity().equals(MarketplaceEntity.DEAL) && parameter.getAttribute().equals(ASSOCIATED_CONTACTS.getName())) {
                    return new SimpleEntry<>(paramKey, tuple.getT1().getT7());
                  } else if (parameter.getEntity().equals(MarketplaceEntity.DEAL) && parameter.getAttribute().equals(PRODUCTS.getName())) {
                    return new SimpleEntry<>(paramKey,
                        getFormattedProducts(marketplaceAction.getMethod(), deal, tuple.getT2(), tuple.getT1().getT8()));
                  }else if(parameter.getEntity().equals(MarketplaceEntity.DEAL) &&  ObjectUtils.isNotEmpty(deal.getCustomFieldValues()) && (deal.getCustomFieldValues().get(parameter.getAttribute())) instanceof List){
                    List<Map<String,Object>> values= (List<Map<String, Object>>) (deal.getCustomFieldValues().get(parameter.getAttribute()));
                    List<Object> valueNames =values.stream().map(value->(String)value.get("name")).collect(toList());
                    return new SimpleEntry<>(paramKey, valueNames);
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();

  }

  private List<Object> getFormattedProducts(HttpMethod method, DealDetail deal, Map<Long, String> currencyIdToName, ProductResponse productResponse) {
    if (ObjectUtils.isEmpty(deal) || ObjectUtils.isEmpty(deal.getProducts())) {
      return null;
    }
    if (method == GET) {
      return deal.getProducts().stream()
          .map(dealProduct -> String.valueOf(dealProduct.getId()))
          .collect(toList());
    }
    return deal.getProducts()
        .stream()
        .map(dealProduct -> new DealProduct(dealProduct.getId(), dealProduct.getName(),
            dealProduct.getQuantity(), currencyIdToName.get(dealProduct.getPrice().getCurrencyId()) + " " + dealProduct.getPrice().getValue(),
            dealProduct.getDiscount()
            , productResponse.getDescription(), dealProduct.getCategory()))
        .collect(toList());
  }

  private Mono<Map<Long, String>> getCurrenciesForProducts(HttpMethod method, DealDetail deal, String jwtToken) {
    if (isEmpty(deal.getProducts())) {
      return Mono.just(emptyMap());
    }
    Set<Long> currencyIds = deal.getProducts()
        .stream()
        .map(dealProduct -> dealProduct.getPrice().getCurrencyId())
        .collect(Collectors.toSet());
    return super.getCurrencyIfRequired(currencyIds, jwtToken)
        .collect(Collectors.toMap(idName -> idName.getId(), idName -> idName.getName()));
  }

  private Mono<List<Object>> getAssociatedContactsParamValue(HttpMethod method, DealDetail dealDetail, String jwtToken) {

    if (isNull(dealDetail.getAssociatedContacts())) {
      return Mono.just(emptyList());
    }

    List<IdName> associatedContacts = dealDetail.getAssociatedContacts();

    if (method == GET) {
      return Mono.just(associatedContacts.stream()
          .map(contact -> String.valueOf(contact.getId()))
          .collect(toList()));
    }

    List<Long> contactIds = associatedContacts.stream().map(IdName::getId).collect(toList());
    return salesService.getContactsByIds(contactIds, jwtToken)
        .map(contactResponses -> {
          if (contactResponses.isEmpty()) {
            return emptyList();
          }
          return contactResponses.stream().map(contactResponse -> (Object) contactResponse).collect(toList());
        });
  }

  private Mono<ProductResponse> getProductParamValue(HttpMethod method, DealDetail deal) {

    if (ObjectUtils.isEmpty(deal.getProducts())) {
      return Mono.just(new ProductResponse());
    }

    List<Long> productIds = deal.getProducts()
        .stream()
        .map(dealProduct -> dealProduct.getId())
        .collect(toList());

    Long productId = productIds.get(0);
    if (method == GET) {
      return Mono.just(ProductResponse.createNew(productId));
    }

    return productService.getProductById(productId, getAuthTokenWithRequiredPermissions());
  }

  private final Mono<List<Object>> getCompanyParamValue(HttpMethod method, DealDetail deal) {

    if (isNull(deal.getCompany())) {
      return Mono.just(emptyList());
    }

    IdName company = deal.getCompany();

    if (method == GET) {
      return Mono.just(List.of(company.getId()));
    }

    return companyService.getCompanyById(company.getId(), getAuthTokenWithRequiredPermissions())
        .map(List::of);
  }

  private List<Object> getFormattedActualMoneyText(DealDetail deal, IdName t5) {
    try {
      return List.of(t5.getName() + " " + deal.getActualValue().getValue());
    } catch (NullPointerException ignored) {
    }
    return List.of("");
  }


  private List<Object> getFormattedEstimatedMoneyText(DealDetail deal, IdName t6) {
    return List.of(t6.getName() + " " + deal.getEstimatedValue().getValue());
  }

  private String getAuthTokenWithRequiredPermissions() {

    User user = authService.getLoggedInUser();

    var readAction = new Action();
    readAction.setRead(true);
    readAction.setReadAll(true);
    Set<Permission> permissions = Set.of(new Permission(3, "user", "has access to user resource", readAction),
        new Permission(18, "company", "has permission to company resource", readAction),
        new Permission(22, "products-services", "has access to Products and Services", readAction));

    return internalAuthProvider.getAuthTokenWithRequiredPermissions(user.getId(), user.getTenantId(), permissions);
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      return getNestedProperty(entity, parameter.fetchPathToField());
    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      var fetchPathToFieldOptional = parameter.fetchPathToField();
      if(fetchPathToFieldOptional.isPresent()) {
        return getNestedProperty(entity,fetchPathToFieldOptional.get());
      }
      return actualValue;
    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }
}
