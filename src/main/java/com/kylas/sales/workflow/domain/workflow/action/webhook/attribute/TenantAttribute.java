package com.kylas.sales.workflow.domain.workflow.action.webhook.attribute;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TenantAttribute implements EntityAttribute {
  ID("id", "Id", "id"),
  ACCOUNT_NAME("accountName", "Account Name", "accountName"),
  INDUSTRY("industry", "Industry", "industry"),
  ADDRESS("address", "Address", "address"),
  CITY("city", "City", "city"),
  STATE("state", "State", "state"),
  COUNTRY("country", "Country", "country"),
  ZIPCODE("zipcode", "Zipcode", "zip"),
  LANGUAGE("language", "Language", "language"),
  CURRENCY("currency", "Currency", "currency"),
  TIMEZONE("timezone", "Timezone", "timezone"),
  COMPANY_NAME("companyName", "Company Name", "companyName"),
  WEBSITE("website", "Website", "website");

  private final String name;
  private final String displayName;
  private final String pathToField;


  public static List<Attribute> getAttributes() {
    return Arrays.stream(values())
        .map(attribute -> new Attribute(attribute.name, attribute.displayName,true))
        .collect(Collectors.toList());
  }
}
