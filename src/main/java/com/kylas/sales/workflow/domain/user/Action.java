package com.kylas.sales.workflow.domain.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class Action implements Serializable {

  @JsonProperty("read")
  private boolean read;

  @JsonProperty("update")
  private boolean update;

  @JsonProperty("write")
  private boolean write;

  @JsonProperty("readAll")
  private boolean readAll;

  @JsonProperty("updateAll")
  private boolean updateAll;

  @JsonProperty("email")
  private boolean email;
  @JsonProperty("call")
  private boolean call;
  @JsonProperty("sms")
  private boolean sms;
  @JsonProperty("task")
  private boolean task;
  @JsonProperty("note")
  private boolean note;
  @JsonProperty("meeting")
  private boolean meeting;
  @JsonProperty("document")
  private boolean document;

  public static Action getReadAndUpdateAllAction() {
    Action readAndUpdateAllAction = new Action();
    readAndUpdateAllAction.setRead(true);
    readAndUpdateAllAction.setReadAll(true);
    readAndUpdateAllAction.setUpdate(true);
    readAndUpdateAllAction.setUpdateAll(true);
    return readAndUpdateAllAction;
  }

  public boolean canRead() {
    return this.read;
  }

  public boolean canWrite() {
    return this.write;
  }

  public boolean canReadAll() {
    return readAll;
  }

  public boolean canUpdate() {
    return this.update;
  }

  public boolean canUpdateAll() {
    return this.updateAll;
  }

  public static Action getReadAllAction() {
    Action readAllAction = new Action();
    readAllAction.setRead(true);
    readAllAction.setReadAll(true);
    return readAllAction;
  }

  public static Action getReadAllWithCallAction() {
    Action readAllAction = new Action();
    readAllAction.setRead(true);
    readAllAction.setReadAll(true);
    readAllAction.setCall(true);
    return readAllAction;
  }
}
