package com.kylas.sales.workflow.domain.workflow.executionLogs;

import com.kylas.sales.workflow.common.dto.ActionDetail.WebhookAction.AuthorizationType;
import java.util.Map;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpMethod;

@Getter
public class WebhookLogPayload {

  private final String name;
  private final String description;
  private final String requestUrl;
  private final HttpMethod method;
  private final AuthorizationType authorizationType;
  private final Map<String, Object> body;

  public WebhookLogPayload(String name, String description, String requestUrl, HttpMethod method, AuthorizationType authorizationType,
      Map<String, Object> body) {
    this.name = name;
    this.description = description;
    this.requestUrl = requestUrl;
    this.method = method;
    this.authorizationType = authorizationType;
    this.body = ObjectUtils.isEmpty(body) ? null : body;
  }
}
