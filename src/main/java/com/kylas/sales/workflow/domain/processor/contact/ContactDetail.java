package com.kylas.sales.workflow.domain.processor.contact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.GPSCoordinateEvent;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class ContactDetail implements Serializable, EntityDetail {

  private Long id;
  private Long tenantId;
  private IdName ownerId;

  private String firstName;
  private String lastName;
  private String name;
  private IdName salutation;
  private String address;
  private String city;
  private String state;
  private String zipcode;
  private String country;

  private Boolean dnd;
  private String timezone;
  private PhoneNumber[] phoneNumbers;
  private Email[] emails;

  private String facebook;
  private String twitter;
  private String linkedin;

  private IdName company;
  private String designation;
  private String department;
  private Boolean stakeholder;
  private Date convertedAt;
  private IdName convertedBy;

  private Integer version;
  private Date createdAt;
  private Date updatedAt;
  private IdName createdBy;
  private IdName updatedBy;
  private IdName importedBy;

  private Map<String, Object> customFieldValues;
  private List<Long> associatedDeals;

  // Source Fields
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;

  private IdName campaign;
  private IdName source;

  // UTM fields
  private String subSource;
  private String utmSource;
  private String utmCampaign;
  private String utmMedium;
  private String utmContent;
  private String utmTerm;
  private Double score;
  private Map<String, Map<Long, String>> idNameStore = new HashMap<>();
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;
  private GPSCoordinateEvent addressCoordinate;

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return this.ownerId;
  }

  @Override
  @JsonIgnore
  public EntityType getEntityType() {
    return EntityType.CONTACT;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    return;
  }

  @Override
  public Map<EntityType, List<EntityDetail>> getAssociatedEntities() {
    return null;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return ownerId;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return Stream.of(this.firstName, this.lastName)
        .filter(StringUtils::isNoneEmpty)
        .collect(Collectors.joining(" "));
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }
}
