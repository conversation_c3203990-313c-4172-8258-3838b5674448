package com.kylas.sales.workflow.domain.workflow.action.webhook;

import static com.kylas.sales.workflow.domain.workflow.EntityType.CALL_LOG;
import static com.kylas.sales.workflow.domain.workflow.EntityType.COMPANY;
import static com.kylas.sales.workflow.domain.workflow.EntityType.CONTACT;
import static com.kylas.sales.workflow.domain.workflow.EntityType.DEAL;
import static com.kylas.sales.workflow.domain.workflow.EntityType.LEAD;
import static com.kylas.sales.workflow.domain.workflow.EntityType.MEETING;
import static com.kylas.sales.workflow.domain.workflow.EntityType.TASK;
import static com.kylas.sales.workflow.domain.workflow.EntityType.USER;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.kylas.sales.workflow.domain.MarketPlaceTriggerDetailRepository;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerDetail;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerDto;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerVariable;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceServiceImpl;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.Attribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CallLogWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.ContactWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.DealWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.EmailWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.LeadWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MeetingWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.TaskWebhookEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.TenantAttribute;
import com.kylas.sales.workflow.security.AuthService;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuple4;
import reactor.util.function.Tuple5;
import reactor.util.function.Tuple7;
import reactor.util.function.Tuple8;

@Service
public class EntityTypeConfiguration {

  private final AttributeFactory attributeFactory;
  private final MarketPlaceTriggerDetailRepository marketPlaceTriggerDetailRepository;
  private final MarketplaceServiceImpl marketplaceService;
  private final AuthService authService;

  @Autowired
  public EntityTypeConfiguration(AttributeFactory attributeFactory, MarketPlaceTriggerDetailRepository marketPlaceTriggerDetailRepository,
      MarketplaceServiceImpl marketplaceService, AuthService authService) {
    this.attributeFactory = attributeFactory;
    this.marketPlaceTriggerDetailRepository = marketPlaceTriggerDetailRepository;
    this.marketplaceService = marketplaceService;
    this.authService = authService;
  }


  public Flux<EntityConfig> getConfigurations(EntityType entityType, Optional<String> optionalTriggerId) {
    Mono<List<MarketPlaceTriggerVariable>> marketlaceTriggerVariables=Mono.just(emptyList());

    if(optionalTriggerId.isPresent()){
      String triggerId = optionalTriggerId.get();
      MarketPlaceTriggerDetail byTriggerIdAndEntity = marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(UUID.fromString(triggerId),
          entityType.name());
      if(byTriggerIdAndEntity==null){
        MarketPlaceTriggerDto marketPlaceTriggerDto = marketplaceService.fetchTriggers(
            UUID.fromString(triggerId), authService.getAuthenticationToken()).block();
        MarketPlaceTriggerDetail marketPlaceTriggerDetail = marketPlaceTriggerDetailRepository.saveAndFlush(
            new MarketPlaceTriggerDetail(marketPlaceTriggerDto.getName(),
                marketPlaceTriggerDto.getId(), marketPlaceTriggerDto.getEntity(), marketPlaceTriggerDto.getAppId(),
                marketPlaceTriggerDto.getTriggerVariables()));
        marketlaceTriggerVariables=Mono.just(marketPlaceTriggerDetail.getTriggerVariables());
      }else {
        marketlaceTriggerVariables=Mono.just(byTriggerIdAndEntity.getTriggerVariables());
      }
    }

    switch (entityType) {
      case LEAD:
        return Mono
            .zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("lead"), attributeFactory.getStandardLeadAttributes(),marketlaceTriggerVariables)
            .map(tuples ->
                stream(attributeFactory.getEntitiesLead())
                    .map(leadWebhookEntity -> new EntityConfig(leadWebhookEntity.name(), leadWebhookEntity.getDisplayName(),
                        getAttributesForLead(tuples, leadWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case CONTACT:
        return Mono
            .zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("contact"),
                attributeFactory.getStandardContactAttributes(),marketlaceTriggerVariables)
            .map(tuples ->
                stream(attributeFactory.getEntitiesContact())
                    .map(contactWebhookEntity -> new EntityConfig(contactWebhookEntity.name(), contactWebhookEntity.getDisplayName(),
                        getAttributesForContact(tuples, contactWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case DEAL:
        return Mono
            .zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("deal"), attributeFactory.getStandardDealAttributes(),marketlaceTriggerVariables)
            .map(tuples ->
                stream(attributeFactory.getEntitiesDeal())
                    .map(dealWebhookEntity -> new EntityConfig(dealWebhookEntity.name(), dealWebhookEntity.getDisplayName(),
                        getAttributesForDeal(tuples, dealWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case CALL_LOG:
        return Mono
            .zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("callLog"), attributeFactory.getStandardCallLogAttributes(),
                attributeFactory.getAllEntityAttributes("lead"), attributeFactory.getStandardLeadAttributes(), attributeFactory.getAllEntityAttributes("deal"),
                attributeFactory.getStandardDealAttributes())
            .zipWith(Mono.zip(attributeFactory.getAllEntityAttributes("contact"), attributeFactory.getStandardContactAttributes(),marketlaceTriggerVariables))
            .map(tuples ->
                stream(attributeFactory.getEntitiesCallLog())
                    .map(callLogWebhookEntity -> new EntityConfig(callLogWebhookEntity.name(), callLogWebhookEntity.getDisplayName(),
                        getAttributesForCallLog(tuples.getT1(), tuples.getT2(), callLogWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case TASK:
        return Mono
            .zip(attributeFactory.getUserAttributes(),attributeFactory.getAllEntityAttributes("task"), attributeFactory.getStandardTaskAttributes(),
                attributeFactory.getAllEntityAttributes("lead"), attributeFactory.getStandardLeadAttributes(), attributeFactory.getAllEntityAttributes("deal"),
                attributeFactory.getStandardDealAttributes())
            .zipWith(Mono.zip(attributeFactory.getAllEntityAttributes("contact"), attributeFactory.getStandardContactAttributes(),marketlaceTriggerVariables,
                attributeFactory.getAllEntityAttributes("company"), attributeFactory.getStandardCompanyAttributes()
            ))
            .map(tuples ->
                stream(attributeFactory.getEntitiesTask())
                    .map(taskWebhookEntity -> new EntityConfig(taskWebhookEntity.name(), taskWebhookEntity.getDisplayName(),
                        getAttributesForTask(tuples.getT1(), tuples.getT2(), taskWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case MEETING:
        return Mono
            .zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("meeting"), attributeFactory.getStandardMeetingAttributes(),
      attributeFactory.getAllEntityAttributes("lead"), attributeFactory.getStandardLeadAttributes(), attributeFactory.getAllEntityAttributes("deal"),
          attributeFactory.getStandardDealAttributes())
            .zipWith(Mono.zip(attributeFactory.getAllEntityAttributes("contact"), attributeFactory.getStandardContactAttributes(),marketlaceTriggerVariables))
            .map(tuples ->
                stream(attributeFactory.getEntitiesMeeting())
                    .map(meetingWebhookEntity -> new EntityConfig(meetingWebhookEntity.name(), meetingWebhookEntity.getDisplayName(),
                        getAttributesForMeeting(tuples.getT1(), tuples.getT2(), meetingWebhookEntity)))
                    .collect(toList()))
            .flatMapMany(Flux::fromIterable);

      case EMAIL:
        return Mono.zip(attributeFactory.getUserAttributes(), attributeFactory.getAllEntityAttributes("lead"), attributeFactory.getStandardLeadAttributes(), attributeFactory.getAllEntityAttributes("deal"),
            attributeFactory.getStandardDealAttributes(), attributeFactory.getAllEntityAttributes("contact"), attributeFactory.getStandardContactAttributes(),marketlaceTriggerVariables)
            .map(tuples -> stream(attributeFactory.getEntitiesEmail())
                  .map(emailWebhookEntity -> new EntityConfig(emailWebhookEntity.name(), emailWebhookEntity.getDisplayName(),
                      getAttributesForEmail(tuples, emailWebhookEntity))).collect(toList())
            ).flatMapMany(Flux::fromIterable);
    }
    return null;
  }

  private List<Attribute> getAttributesForContact(Tuple4<List<Attribute>, List<FieldAttribute>,List<Attribute>,List<MarketPlaceTriggerVariable>> tuples, ContactWebhookEntity webhookEntity) {
    return webhookEntity.getType().equals(USER) ? tuples.getT1() :
        webhookEntity.getType().equals(CONTACT) ? attributeFactory.getContactAttributes(tuples.getT2(),tuples.getT3()):
            webhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(tuples.getT4()) :
            webhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() : emptyList();
  }

  private List<Attribute> getAttributesForDeal(Tuple4<List<Attribute>, List<FieldAttribute>, List<Attribute>,List<MarketPlaceTriggerVariable>> tuples,
      DealWebhookEntity webhookEntity) {
    return webhookEntity.getType().equals(USER) ? tuples.getT1() :
        webhookEntity.getType().equals(DEAL) ? attributeFactory.getDealAttributes(tuples.getT2(), tuples.getT3()) :
            webhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(tuples.getT4()) :
                webhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() : emptyList();
  }

  private List<Attribute> getAttributesForLead(Tuple4<List<Attribute>, List<FieldAttribute>,List<Attribute>,List<MarketPlaceTriggerVariable>> tuples, LeadWebhookEntity webhookEntity) {
    return webhookEntity.getType().equals(USER) ? tuples.getT1() :
        webhookEntity.getType().equals(LEAD)? attributeFactory.getLeadAttributes(tuples.getT2(),tuples.getT3()) :
            webhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(tuples.getT4()) :
            webhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() :emptyList();
  }

  private List<Attribute> getAttributesForCallLog(
      Tuple7<List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>, List<Attribute>> tuples,
      Tuple3<List<FieldAttribute>, List<Attribute>,List<MarketPlaceTriggerVariable>> additionalTuples,
      CallLogWebhookEntity callLogWebhookEntity) {
    return callLogWebhookEntity.getType().equals(USER) ? tuples.getT1() :
        callLogWebhookEntity.getType().equals(CALL_LOG)? attributeFactory.getCallLogAttributes(tuples.getT2(),tuples.getT3()) :
            callLogWebhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() :
                callLogWebhookEntity.getType().equals(EntityType.LEAD) ? attributeFactory.getLeadAttributes(tuples.getT4(),tuples.getT5()) :
                    callLogWebhookEntity.getType().equals(DEAL) ? attributeFactory.getDealAttributes(tuples.getT6(), tuples.getT7()) :
                        callLogWebhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(additionalTuples.getT3()) :
                        callLogWebhookEntity.getType().equals(CONTACT) ? attributeFactory.getContactAttributes(additionalTuples.getT1(), additionalTuples.getT2())
                            :emptyList();
  }

  private List<Attribute> getAttributesForTask(
      Tuple7<List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>, List<Attribute>> tuples,
      Tuple5<List<FieldAttribute>, List<Attribute>,List<MarketPlaceTriggerVariable>,List<FieldAttribute>, List<Attribute>> additionalTuples,
      TaskWebhookEntity taskWebhookEntity) {
    return taskWebhookEntity.getType().equals(USER) ? tuples.getT1() :
        taskWebhookEntity.getType().equals(TASK) ? attributeFactory.getTaskAttributes(tuples.getT2(),tuples.getT3()) :
            taskWebhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() :
                taskWebhookEntity.getType().equals(EntityType.LEAD) ? attributeFactory.getLeadAttributes(tuples.getT4(),tuples.getT5()) :
                    taskWebhookEntity.getType().equals(DEAL) ? attributeFactory.getDealAttributes(tuples.getT6(), tuples.getT7()) :
                        taskWebhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(additionalTuples.getT3()) :
                            taskWebhookEntity.getType().equals(CONTACT) ? attributeFactory.getContactAttributes(additionalTuples.getT1(), additionalTuples.getT2()) :
                                taskWebhookEntity.getType().equals(COMPANY) ? attributeFactory.getCompanyAttributes(additionalTuples.getT4(), additionalTuples.getT5())
                            :emptyList();
  }

  private List<Attribute> getAttributesForMeeting(
      Tuple7<List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>,List<Attribute>, List<FieldAttribute>, List<Attribute>> tuples,
      Tuple3<List<FieldAttribute>, List<Attribute>,List<MarketPlaceTriggerVariable>> additionalTuples,
      MeetingWebhookEntity meetingWebhookEntity) {
    return meetingWebhookEntity.getType().equals(USER) ? tuples.getT1() :
        meetingWebhookEntity.getType().equals(MEETING)? attributeFactory.getMeetingAttributes(tuples.getT2(),tuples.getT3()) :
            meetingWebhookEntity.getType().equals(EntityType.TENANT) ? TenantAttribute.getAttributes() :
                meetingWebhookEntity.getType().equals(EntityType.LEAD) ? attributeFactory.getLeadAttributes(tuples.getT4(),tuples.getT5()) :
                    meetingWebhookEntity.getType().equals(DEAL) ? attributeFactory.getDealAttributes(tuples.getT6(), tuples.getT7()) :
                        meetingWebhookEntity.getType().equals(EntityType.MARKETPLACE_TRIGGER) ? attributeFactory.getMarketPlaceAttributes(additionalTuples.getT3()) :
                            meetingWebhookEntity.getType().equals(CONTACT) ? attributeFactory.getContactAttributes(additionalTuples.getT1(), additionalTuples.getT2())
                            :emptyList();
  }
  private List<Attribute> getAttributesForEmail(
      Tuple8<List<Attribute>, List<FieldAttribute>, List<Attribute>, List<FieldAttribute>, List<Attribute>, List<FieldAttribute>, List<Attribute>,List<MarketPlaceTriggerVariable>> tuples,
      EmailWebhookEntity emailWebhookEntity) {
    switch (emailWebhookEntity.getType()) {
      case USER:
        return tuples.getT1();
      case TENANT:
        return TenantAttribute.getAttributes();
      case EMAIL:
        return attributeFactory.getStandardEmailAttributes();
      case LEAD:
        return attributeFactory.getLeadAttributes(tuples.getT2(), tuples.getT3());
      case DEAL:
        return attributeFactory.getDealAttributes(tuples.getT4(), tuples.getT5());
      case CONTACT:
        return attributeFactory.getContactAttributes(tuples.getT6(), tuples.getT7());
      case MARKETPLACE_TRIGGER:
        return attributeFactory.getMarketPlaceAttributes(tuples.getT8());
    }
    return Collections.emptyList();
  }

}
