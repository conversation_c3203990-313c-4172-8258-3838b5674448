package com.kylas.sales.workflow.domain.workflow.action.convertLead;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.CONVERT_LEAD;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import javax.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
public class ConvertLeadAction extends AbstractWorkflowAction implements WorkflowAction {

  private boolean deal;
  private boolean contact;
  private boolean company;

  public ConvertLeadAction(boolean deal, boolean contact, boolean company) {
    this.deal = deal;
    this.contact = contact;
    this.company = company;
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    var payload = (ActionDetail.ConvertLeadAction) actionResponse.getPayload();
    return new ConvertLeadAction(payload.isDeal(), payload.isContact(), payload.isCompany());
  }

  public static ActionResponse toActionResponse(ConvertLeadAction action) {
    return new ActionResponse(action.getId(), CONVERT_LEAD,
        new ActionDetail.ConvertLeadAction(action.isDeal(), action.isContact(), action.isCompany()));
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse actionResponse) {
    var payload = (ActionDetail.ConvertLeadAction) actionResponse.getPayload();
    this.setDeal(payload.isDeal());
    this.setContact(payload.isContact());
    this.setCompany(payload.isCompany());
    return this;
  }

  @Override
  public ActionType getType() {
    return CONVERT_LEAD;
  }
}
