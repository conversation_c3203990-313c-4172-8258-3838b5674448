package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.deal.Money;
import java.util.Date;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
public class ProductResponse {

  private long id;
  private String name;
  private Money price;
  private String description;

  @Getter(AccessLevel.NONE)
  private boolean active;

  @JsonFormat(shape = Shape.STRING)
  private Date createdAt;
  @JsonFormat(shape = Shape.STRING)
  private Date updatedAt;

  @JsonCreator
  public ProductResponse(
      @JsonProperty("id") long id,
      @JsonProperty("name") String name,
      @JsonProperty("price") Money price,
      @JsonProperty("description") String description,
      @JsonProperty("isActive") boolean active,
      @JsonProperty("createdAt") Date createdAt,
      @JsonProperty("updatedAt") Date updatedAt) {
    this.id = id;
    this.name = name;
    this.price = price;
    this.description = description;
    this.active = active;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
  public static ProductResponse createNew(Long productId){
    ProductResponse productResponse = new ProductResponse();
    productResponse.id = productId;
    return productResponse;
  }
}
