package com.kylas.sales.workflow.domain.processor.deal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DealProduct {

  private Long id;
  private String name;
  private Double quantity;
  private Money price;
  private Discount discount;
  private Category category;
  private IdName units;
  private String hsnSacCode;
  private IdName countryOfOrigin;
  private Map<String, Object> customFieldValues;

  public DealProduct(@JsonProperty("id") Long id, @JsonProperty("name") String name,
      @JsonProperty("quantity") Double quantity, @JsonProperty("price") Money price,
      @JsonProperty("discount") Discount discount, @JsonProperty("category") Category category,
      @JsonProperty("units") IdName units, @JsonProperty("hsnSacCode") String hsnSacCode,
      @JsonProperty("countryOfOrigin") IdName countryOfOrigin,
      @JsonProperty("customFieldValues") Map<String, Object> customFieldValues) {
    this.id = id;
    this.name = name;
    this.quantity = ObjectUtils.isEmpty(quantity) ? 0.0 : BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_DOWN).doubleValue();
    this.price = price;
    this.discount = discount;
    this.category = category;
    this.units = units;
    this.hsnSacCode = hsnSacCode;
    this.countryOfOrigin = countryOfOrigin;
    this.customFieldValues = customFieldValues;
  }
}
