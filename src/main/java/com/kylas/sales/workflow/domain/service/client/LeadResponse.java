package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.ConversionAssociation;
import com.kylas.sales.workflow.domain.processor.lead.Email;
import com.kylas.sales.workflow.domain.processor.lead.ForecastingType;
import com.kylas.sales.workflow.domain.processor.lead.GPSCoordinateEvent;
import com.kylas.sales.workflow.domain.processor.lead.LeadDetail;
import com.kylas.sales.workflow.domain.processor.lead.PhoneNumber;
import com.kylas.sales.workflow.domain.processor.lead.Product;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class LeadResponse extends EntityResponse {
  @JsonProperty private Long id;
  @JsonProperty private Long tenantId;
  @JsonProperty private Long ownerId;
  @JsonProperty private String firstName;
  @JsonProperty private String lastName;
  @JsonProperty private String name;
  @JsonProperty private Long salutation;
  @JsonProperty private String timezone;
  @JsonProperty private String address;
  @JsonProperty private String city;
  @JsonProperty private String state;
  @JsonProperty private String zipcode;
  @JsonProperty private String country;
  @JsonProperty private String department;
  @JsonProperty private Boolean dnd;
  @JsonProperty private PhoneNumber[] phoneNumbers;
  @JsonProperty private String[] photoUrls;
  @JsonProperty private Email[] emails;
  @JsonProperty private String facebook;
  @JsonProperty private String twitter;
  @JsonProperty private String linkedIn;

  @JsonProperty private Long pipeline;
  @JsonProperty private Long pipelineStage;
  @JsonProperty private String pipelineStageReason;

  // company
  @JsonProperty private String companyName;
  @JsonProperty private String companyAddress;
  @JsonProperty private String companyCity;
  @JsonProperty private String companyState;
  @JsonProperty private String companyZipcode;
  @JsonProperty private String companyCountry;
  @JsonProperty private Long companyEmployees;
  @JsonProperty private Double companyAnnualRevenue;
  @JsonProperty private String companyWebsite;
  @JsonProperty private String companyIndustry;
  @JsonProperty private String companyBusinessType;
  @JsonProperty private PhoneNumber[] companyPhones;
  @JsonProperty private String requirementName;
  @JsonProperty private String requirementCurrency;
  @JsonProperty private Double requirementBudget;
  @JsonProperty private Date expectedClosureOn;

  @JsonProperty private List<Product> products;

  @JsonProperty private ConversionAssociation conversionAssociation;
  @JsonProperty private Date convertedAt;
  @JsonProperty private Long convertedBy;

  @JsonProperty private String designation;
  @JsonProperty private Long campaign;
  @JsonProperty private Long source;

  private ForecastingType forecastingType;

  private Map<String, Object> customFieldValues;
  private Long importedBy;

  private Boolean deleted;

  private Integer version;
  private Date createdAt;
  private Date updatedAt;
  private Long createdBy;
  private Long updatedBy;

  private Date actualClosureDate;

  // Source Fields
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date taskDueOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date meetingScheduledOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date latestActivityCreatedAt;
  private Boolean isNew;

  // UTM fields
  private String subSource;
  private String utmSource;
  private String utmCampaign;
  private String utmMedium;
  private String utmContent;
  private String utmTerm;
  private Double score;
  private Map<String, Map<Integer, String>> idNameStore = new HashMap<>();
  private GPSCoordinateEvent addressCoordinate;
  private GPSCoordinateEvent companyAddressCoordinate;

  @Override
  public EntityDetail createFromEntityResponse(Metadata metaData) {
    var salutation = getIdName(metaData.getIdNameStore(), this.getSalutation(), "salutation");
    var ownerId = getIdName(metaData.getIdNameStore(), this.getOwnerId(), "ownerId");
    var pipeline = getIdName(metaData.getIdNameStore(), this.getPipeline(), "pipeline");
    var pipelineStage = getIdName(metaData.getIdNameStore(), this.getPipelineStage(), "pipelineStage");
    var companyEmployees = getIdName(metaData.getIdNameStore(), this.getCompanyEmployees(), "companyEmployees");
    var convertedBy = getIdName(metaData.getIdNameStore(), this.getConvertedBy(), "convertedBy");
    var importedBy = getIdName(metaData.getIdNameStore(), this.getImportedBy(), "importedBy");
    var createdBy = getIdName(metaData.getIdNameStore(), this.getCreatedBy(), "createdBy");
    var updatedBy = getIdName(metaData.getIdNameStore(), this.getUpdatedBy(), "updatedBy");
    var campaign = getIdName(metaData.getIdNameStore(), this.getCampaign(), "campaign");
    var source = getIdName(metaData.getIdNameStore(), this.getSource(), "source");
    return new LeadDetail(
        this.getId(),
        this.getTenantId(),
        ownerId,
        this.getFirstName(),
        this.getLastName(),
        this.getName(),
        salutation,
        this.getTimezone(),
        this.getAddress(),
        this.getCity(),
        this.getState(),
        this.getZipcode(),
        this.getCountry(),
        this.getDepartment(),
        this.getDnd(),
        this.getPhoneNumbers(),
        this.getPhotoUrls(),
        this.getEmails(),
        this.getFacebook(),
        this.getTwitter(),
        this.getLinkedIn(),
        pipeline,
        pipelineStage,
        this.getPipelineStageReason(),
        this.getCompanyName(),
        this.getCompanyAddress(),
        this.getCompanyCity(),
        this.getCompanyState(),
        this.getCompanyZipcode(),
        this.getCompanyCountry(),
        companyEmployees,
        this.getCompanyAnnualRevenue(),
        this.getCompanyWebsite(),
        this.getCompanyIndustry(),
        this.getCompanyBusinessType(),
        this.getCompanyPhones(),
        this.getRequirementName(),
        this.getRequirementCurrency(),
        this.getRequirementBudget(),
        this.getExpectedClosureOn(),
        this.getProducts(),
        this.getConversionAssociation(),
        this.getConvertedAt(),
        convertedBy,
        this.getDesignation(),
        campaign,
        source,
        this.getForecastingType(),
        this.getCustomFieldValues(),
        importedBy,
        this.getDeleted(),
        this.getVersion(),
        this.getCreatedAt(),
        this.getUpdatedAt(),
        createdBy,
        updatedBy,
        this.getActualClosureDate(),
        this.getCreatedViaId(),
        this.getCreatedViaName(),
        this.getCreatedViaType(),
        this.getUpdatedViaId(),
        this.getUpdatedViaName(),
        this.getUpdatedViaType(),
        this.getTaskDueOn(),
        this.getMeetingScheduledOn(),
        this.getLatestActivityCreatedAt(),
        this.getIsNew(),
        this.getSubSource(),
        this.getUtmSource(),
        this.getUtmCampaign(),
        this.getUtmMedium(),
        this.getUtmContent(),
        this.getUtmTerm(),
        this.getScore(),
        metaData.getIdNameStore(), null,
        this.getAddressCoordinate(),
        this.getCompanyAddressCoordinate()
    );
  }
}