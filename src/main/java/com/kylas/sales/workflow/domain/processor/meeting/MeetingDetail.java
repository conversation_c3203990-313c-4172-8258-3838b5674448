package com.kylas.sales.workflow.domain.processor.meeting;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.EntityResponse;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class MeetingDetail extends EntityResponse implements Serializable, EntityDetail {

  private Long id;
  private String title;
  private String description;
  private String status;
  private String medium;
  private Boolean allDay;
  private Date from;
  private Date to;
  private Date conductedAt;
  private Date createdAt;
  private Date updatedAt;
  private Date cancelledAt;
  private Timezone timezone;
  private List<Participants> participants;
  private URL providerLink;
  private Organizer organizer;
  private IdName createdBy;
  private IdName updatedBy;
  private IdName owner;
  private IdName cancelledBy;
  private IdName conductedBy;
  private IdName importedBy;
  private String location;
  private CheckedDetails checkedInDetails;
  private CheckedDetails checkedOutDetails;
  private List<RelatedTo> relatedTo;
  private Map<String, Object> customFieldValues;
  @JsonIgnore
  private Map<EntityType, List<EntityDetail>> associatedEntities;
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public IdName getOwner() {
    return this.owner;
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.MEETING;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    this.associatedEntities = associatedEntities;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return owner;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return title.substring(0, Math.min(title.length(), 510));
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    return this;
  }


  public Map<String, List<Long>> getRelatedToByEntityGroup(List<RelatedTo> relatedTo) {
    return relatedTo.stream()
        .collect(Collectors.groupingBy(relatedTo1 -> relatedTo1.getEntity().name(), Collectors.mapping(relatedTo1 -> relatedTo1.id,Collectors.toList())));
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Participants {

    private Long id;
    private EntityType entity;
    private String name;
    private String email;
    private String rsvpResponse;
    private String rsvpMessage;

    @JsonCreator
    public Participants(
        @JsonProperty("id") Long id,
        @JsonProperty("entity") String entity,
        @JsonProperty("name") String name,
        @JsonProperty("email") String email,
        @JsonProperty("rsvpResponse") String rsvpResponse,
        @JsonProperty("rsvpMessage") String rsvpMessage
    ) {
      this.id = id;
      this.entity = EntityType.valueOf(entity.toUpperCase());
      this.email = email;
      this.name = name;
      this.rsvpResponse = rsvpResponse;
      this.rsvpMessage = rsvpMessage;
    }

    public Participants withName(String name) {
      this.name = name;
      return this;
    }
  }


  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class RelatedTo {

    private final Long id;
    private final EntityType entity;
    private final String name;
    private final String email;

    @JsonCreator
    public RelatedTo(
        @JsonProperty("id") Long id,
        @JsonProperty("entity") String entity,
        @JsonProperty("name") String name,
        @JsonProperty("email") String email
    ) {
      this.id = id;
      this.entity = EntityType.valueOf(entity.toUpperCase());
      this.name = name;
      this.email = email;
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Timezone {
    private Long id;
    private String entity;
    private String name;

    @JsonCreator
    public Timezone(
        @JsonProperty("id") Long id,
        @JsonProperty("entity") String entity,
        @JsonProperty("name") String name
    ){
      this.id = id;
      this.entity = entity;
      this.name= name;
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CheckedDetails {

    private final Date at;
    private final String latitude;
    private final String longitude;

    @JsonCreator
    public CheckedDetails(
        @JsonProperty("at") Date at,
        @JsonProperty("latitude") String latitude,
        @JsonProperty("longitude") String longitude) {
      this.at = at;
      this.latitude = latitude;
      this.longitude = longitude;
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Organizer {

    private Long id;
    private EntityType entity;
    private String name;
    private String email;

    @JsonCreator
    public Organizer(
        @JsonProperty("id") Long id,
        @JsonProperty("entity") String entity,
        @JsonProperty("name") String name,
        @JsonProperty("email") String email
    ) {
      this.id = id;
      this.entity = EntityType.valueOf(entity.toUpperCase());
      this.email = email;
      this.name = name;
    }
  }
}
