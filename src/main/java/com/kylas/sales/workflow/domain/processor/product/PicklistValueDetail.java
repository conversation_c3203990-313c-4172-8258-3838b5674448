package com.kylas.sales.workflow.domain.processor.product;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
public class PicklistValueDetail implements Serializable, EntityDetail {

  private Long id;
  private  boolean isDisabled;
  private  String name;
  private  String displayName;
  private  String fieldName;

  @JsonCreator
  public PicklistValueDetail(
      @JsonProperty("id") long id,
      @JsonProperty("isDisabled") boolean isDisabled,
      @JsonProperty("name") String name,
      @JsonProperty("displayName") String displayName,
      @JsonProperty("fieldName") String fieldName) {
    this.id = id;
    this.isDisabled = isDisabled;
    this.name = name;
    this.displayName = displayName;
    this.fieldName = fieldName;
  }

  public PicklistValueDetail(Long id, String displayName) {
    this.id = id;
    this.displayName = displayName;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return null;
  }

  @Override
  public EntityType getEntityType() {
    return null;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    return;
  }

  @Override
  public Map<EntityType, List<EntityDetail>> getAssociatedEntities() {
    return null;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return null;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return null;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {

  }

  @Override
  public Map<String, Object> getMarketPlaceTriggerValues() {
    return Collections.emptyMap();
  }
}