package com.kylas.sales.workflow.domain.workflow.action.email;

import java.io.Serializable;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@NoArgsConstructor
@ToString
public class EmailRecipient implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;
  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "email_type")
  private EmailType type;
  @NotBlank
  @Column(name = "entity_type")
  private String entity;
  private Long entityId;
  private String name;
  private String email;

  public EmailRecipient(@NotNull EmailType type, @NotBlank String entity, Long entityId, String name, String email) {
    this.type = type;
    this.entity = entity;
    this.entityId = entityId;
    this.name = name;
    this.email = email;
  }
}
