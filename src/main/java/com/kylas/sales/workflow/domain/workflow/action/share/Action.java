package com.kylas.sales.workflow.domain.workflow.action.share;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class Action implements Serializable {

  @JsonProperty("read")
  private boolean read;
  @JsonProperty("update")
  private boolean update;
  @JsonProperty("email")
  private boolean email;
  @JsonProperty("call")
  private boolean call;
  @JsonProperty("sms")
  private boolean sms;
  @JsonProperty("task")
  private boolean task;
  @JsonProperty("note")
  private boolean note;
  @JsonProperty("meeting")
  private boolean meeting;
  @JsonProperty("document")
  private boolean document;
  @JsonProperty("quotation")
  private boolean quotation;

}
