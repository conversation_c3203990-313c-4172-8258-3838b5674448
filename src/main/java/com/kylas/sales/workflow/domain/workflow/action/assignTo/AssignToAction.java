package com.kylas.sales.workflow.domain.workflow.action.assignTo;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.ASSIGN_TO;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isBlank;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import javax.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class AssignToAction extends AbstractWorkflowAction implements WorkflowAction {

  private Long assignTo;
  private String name;

  public AssignToAction(Long assignTo, String name) {
    this.assignTo = assignTo;
    this.name = name;
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    var payload = (ActionDetail.AssignToAction) actionResponse.getPayload();
    if (isNull(payload.getId()) || isBlank(payload.getName())) {
      throw new InvalidActionException();
    }
    return new AssignToAction(payload.getId(), payload.getName());
  }

  public static ActionResponse toActionResponse(AssignToAction action) {
    return new ActionResponse(action.getId(), ASSIGN_TO, new ActionDetail.AssignToAction(action.getAssignTo(), action.getName()));
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse actionResponse) {
    var payload = (ActionDetail.AssignToAction) actionResponse.getPayload();
    if (isNull(payload.getId()) || isBlank(payload.getName())) {
      throw new InvalidActionException();
    }
    this.setAssignTo(payload.getId());
    this.setName(payload.getName());
    return this;
  }

  @Override
  public ActionType getType() {
    return ASSIGN_TO;
  }
}
