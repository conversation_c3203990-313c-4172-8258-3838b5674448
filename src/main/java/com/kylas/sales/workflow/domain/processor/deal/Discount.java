package com.kylas.sales.workflow.domain.processor.deal;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class Discount {

  @Enumerated(EnumType.STRING)
  @JsonProperty("type")
  private Type type;
  @JsonProperty("value")
  private Double value;

  @JsonCreator
  public Discount(@JsonProperty("type") Type type, @JsonProperty("value") Double value) {
    this.type = type;
    this.value = value;
  }

  public enum Type {
    FIXED,
    PERCENTAGE,
    ;
  }

}
