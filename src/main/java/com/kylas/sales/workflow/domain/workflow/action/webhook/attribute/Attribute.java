package com.kylas.sales.workflow.domain.workflow.action.webhook.attribute;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class Attribute {

  private final String name;
  private final String displayName;
  @JsonProperty(value = "isStandard")
  private final boolean standard;

  public Attribute(String name, String displayName, boolean isStandard) {
    this.name = name;
    this.displayName = displayName;
    this.standard=isStandard;
  }
}
