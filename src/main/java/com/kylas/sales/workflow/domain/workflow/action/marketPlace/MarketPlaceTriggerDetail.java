package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Getter
@Setter
@NoArgsConstructor
@Entity
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class MarketPlaceTriggerDetail implements Serializable {
  @Id
  private UUID triggerId;
  private String name;
  private UUID appId;
  private String entity;
  @Type(type = "jsonb")
  @Column(name = "triggerVariables", columnDefinition = "jsonb")
  private List<MarketPlaceTriggerVariable> triggerVariables;

  public MarketPlaceTriggerDetail(String name, UUID triggerId, String entity, UUID appId,List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables){
    this.name=name;
    this.triggerId=triggerId;
    this.entity=entity;
    this.appId=appId;
    this.triggerVariables=marketPlaceTriggerVariables;
  }

  public MarketPlaceTriggerDetail update(String name,UUID triggerId, String entity, UUID appId, List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables){
    return new MarketPlaceTriggerDetail(name,triggerId, entity, appId,marketPlaceTriggerVariables);
  }

  public static MarketPlaceTriggerDetail createNew(String name,UUID triggerId, String entity, UUID appId, List<MarketPlaceTriggerVariable> marketPlaceTriggerVariables){
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = new MarketPlaceTriggerDetail(name, triggerId, entity, appId, marketPlaceTriggerVariables);;
    return marketPlaceTriggerDetail;
  }

}