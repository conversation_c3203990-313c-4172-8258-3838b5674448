package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class LeadDetail implements Serializable, EntityDetail {

  private Long id;
  private Long tenantId;
  private IdName ownerId;


  private String firstName;
  private String lastName;
  private String name;
  private IdName salutation;


  private String timezone;

  private String address;
  private String city;
  private String state;
  private String zipcode;
  private String country;

  private String department;
  private Boolean dnd;

  private PhoneNumber[] phoneNumbers;
  private String[] photoUrls;
  private Email[] emails;


  private String facebook;
  private String twitter;
  private String linkedIn;

  private IdName pipeline;
  private IdName pipelineStage;
  private String pipelineStageReason;

  // company
  private String companyName;
  private String companyAddress;
  private String companyCity;
  private String companyState;
  private String companyZipcode;
  private String companyCountry;
  private IdName companyEmployees;
  private Double companyAnnualRevenue;
  private String companyWebsite;
  private String companyIndustry;
  private String companyBusinessType;
  private PhoneNumber[] companyPhones;

  private String requirementName;
  private String requirementCurrency;
  private Double requirementBudget;
  private Date expectedClosureOn;

  private List<Product> products;

  @JsonIgnore
  private ConversionAssociation conversionAssociation;
  private Date convertedAt;
  private IdName convertedBy;

  private String designation;
  private IdName campaign;
  private IdName source;

  private ForecastingType forecastingType;

  private Map<String, Object> customFieldValues;
  private IdName importedBy;

  private Boolean deleted;
  private Integer version;
  private Date createdAt;
  private Date updatedAt;
  private IdName createdBy;
  private IdName updatedBy;

  private Date actualClosureDate;
  
  // Source Fields
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date taskDueOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date meetingScheduledOn;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date latestActivityCreatedAt;
  private Boolean isNew;

  // UTM fields
  private String subSource;
  private String utmSource;
  private String utmCampaign;
  private String utmMedium;
  private String utmContent;
  private String utmTerm;
  private Double score;
  private Map<String, Map<Long, String>> idNameStore = new HashMap<>();
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;
  private GPSCoordinateEvent addressCoordinate;
  private GPSCoordinateEvent companyAddressCoordinate;

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  @JsonIgnore
  public IdName getOwner() {
    return this.ownerId;
  }

  @Override
  @JsonIgnore
  public EntityType getEntityType() {
    return EntityType.LEAD;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> map) {
    return;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String,Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public Map<EntityType, List<EntityDetail>> getAssociatedEntities() {
    return null;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return ownerId;
  }

  @Override
  @JsonIgnore
  public String getEntityName() {
    return Stream.of(this.firstName, this.lastName)
        .filter(StringUtils::isNoneEmpty)
        .collect(Collectors.joining(" "));
  }
}
