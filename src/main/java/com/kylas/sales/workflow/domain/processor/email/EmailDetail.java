package com.kylas.sales.workflow.domain.processor.email;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.EntityResponse;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import com.kylas.sales.workflow.domain.service.client.SearchResponse;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class EmailDetail extends EntityResponse implements Serializable, EntityDetail, SearchResponse {

  private Long id;
  private String body;
  @JsonProperty("sender")
  private EntitySummary sentBy;
  private List<EntitySummary> relatedTo;
  private List<EntitySummary> toRecipients;
  private List<EntitySummary> ccRecipients;
  private List<EntitySummary> bccRecipients;
  private String subject;
  @JsonProperty("sentAt")
  private Date createdAt;
  private Long tenantId;
  private Long threadId;
  private IdName owner;
  private List<Date> openedAt;
  private List<LinkHistory> linksClickedAt;
  private List<EmailAttachment> attachments;
  private String direction;
  private String status;
  private boolean trackingEnabled;
  private List<Long> emailsMatchingGlobalMessageId;
  @JsonIgnore
  private Map<EntityType, List<EntityDetail>> associatedEntities;
  @JsonIgnore
  private Map<String, Object> marketPlaceTriggerValues;

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public IdName getOwner() {
    return this.owner;
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.EMAIL;
  }

  @Override
  public void setAssociatedEntities(Map<EntityType, List<EntityDetail>> associatedEntities) {
    this.associatedEntities = associatedEntities;
  }

  @Override
  public void setMarketPlaceTriggerValues(Map<String, Object> marketPlaceTriggerValues) {
    this.marketPlaceTriggerValues=marketPlaceTriggerValues;
  }

  @Override
  public IdName getUserForRelativeDateFilterTimezone() {
    return null;
  }

  @Override
  public String getEntityName() {
    return null;
  }

  @Override
  public EntityDetail createFromEntityResponse(Metadata metadata) {
    return this;
  }

  public Map<String, List<Long>> getRelatedToByEntityGroup(List<EntitySummary> relations) {
    if (ObjectUtils.isEmpty(relations)) {
      return Collections.emptyMap();
    }
    return relations.stream().collect(Collectors.groupingBy(EntitySummary::getEntity,
        Collectors.mapping(EntitySummary::getId, Collectors.toList())));
  }

  @Override
  @JsonIgnore
  public EntityResponse getContent() {
    return this;
  }

  @Override
  public Metadata getMetadata() {
    return null;
  }

  @Override
  public List<EntityResponse> getResponse() {
    return null;
  }

  public void setRelatedTo(List<EntitySummary> relatedTo) {
    this.relatedTo = relatedTo.stream().filter(entitySummary -> List.of(EntityType.LEAD.name().toLowerCase(),
        EntityType.DEAL.name().toLowerCase(), EntityType.CONTACT.name().toLowerCase())
        .contains(entitySummary.getEntity().toLowerCase())).collect(Collectors.toList());
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @JsonInclude(Include.NON_NULL)
  public static class EmailAttachment {
    private final Long id;
    private final String cid;
    private final String fileName;
    private final Long fileSize;
    private final boolean inline;

    @JsonCreator
    public EmailAttachment(
        @JsonProperty("id") Long id,
        @JsonProperty("cid") String cid,
        @JsonProperty("fileName") String fileName,
        @JsonProperty("fileSize") Long fileSize,
        @JsonProperty("inline") boolean inline) {
      this.id = id;
      this.cid = cid;
      this.fileName = fileName;
      this.fileSize = fileSize;
      this.inline = inline;
    }
  }

  @JsonIgnore
  public List<Date> getClickedAt() {
    if (ObjectUtils.isEmpty(linksClickedAt)) {
      return Collections.emptyList();
    }
    return linksClickedAt.stream()
         .filter(linkHistory -> ObjectUtils.isNotEmpty(linkHistory.getClickedAt()))
         .flatMap(linkHistory -> linkHistory.getClickedAt().stream())
        .collect(Collectors.toList());
  }
}
