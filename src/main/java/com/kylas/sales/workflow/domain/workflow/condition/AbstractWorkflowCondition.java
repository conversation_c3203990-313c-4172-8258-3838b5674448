package com.kylas.sales.workflow.domain.workflow.condition;

import com.kylas.sales.workflow.common.dto.condition.WorkflowCondition;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;

@MappedSuperclass
@Getter
@Setter
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public abstract class AbstractWorkflowCondition implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  protected Long id;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  protected WorkflowCondition.ConditionExpression expression;

  @OneToOne
  @JoinColumn(name = "workflow_id")
  protected Workflow workflow;

  public AbstractWorkflowCondition(Long id, WorkflowCondition.ConditionExpression expression, Workflow workflow) {
    this.id = id;
    this.expression = expression;
    this.workflow = workflow;
  }

  public AbstractWorkflowCondition(WorkflowCondition.ConditionExpression expression) {
    this.expression = expression;
  }

  public AbstractWorkflowCondition() {}
}
