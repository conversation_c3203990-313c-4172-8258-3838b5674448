package com.kylas.sales.workflow.domain.service.client;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.Searchable;
import java.time.Duration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service("SEARCH")
@Slf4j
public class SearchService implements Searchable {

  private final String clientBasePath;

  @Autowired
  public SearchService(@Value("${client.search.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Mono<List<EntitySummary>> getSummaries(String entityPlural, List<Long> ids, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries").path("/" + entityPlural).queryParam("id", ids).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(EntitySummary.class)
        .retryBackoff(1, Duration.ofSeconds(2))
        .collectList();
  }

  public Mono<List<IdName>> getUsersSummaries(List<Long> ids, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/users").queryParam("id", ids).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class)
        .collectList();
  }

  public Mono<List<IdName>> getCompanySummaries(List<Long> ids, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/summaries/companies/idName").queryParam("id", ids).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class)
        .collectList();
  }

  @Override
  public Mono<SearchResponse> getEntityDetails(String entity, Long id, String authenticationToken, Class entityClass) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .post()
        .uri(uriBuilder -> uriBuilder.path("/v2/search/" + entity)
            .queryParam("sort", "updatedAt,desc")
            .queryParam("page", 0)
            .queryParam("size", 1)
            .build())
        .bodyValue(JsonRule.getFilterForId(id))
        .accept(MediaType.APPLICATION_JSON)
        .retrieve().bodyToMono(entityClass);
  }

  public Mono<SearchResponse> getAssociatedEntityDetails(String entity, List<Long> ids, String authenticationToken, Class entityClass) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .post()
        .uri(uriBuilder -> uriBuilder.path("/v2/search/" + entity)
            .queryParam("sort", "updatedAt,desc")
            .queryParam("page", 0)
            .queryParam("size", 100)
            .build())
        .bodyValue(JsonRule.getFilterForIds(ids))
        .accept(MediaType.APPLICATION_JSON)
        .retrieve().bodyToMono(entityClass);
  }
}
