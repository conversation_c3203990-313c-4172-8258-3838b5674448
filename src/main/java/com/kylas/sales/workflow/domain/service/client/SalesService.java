package com.kylas.sales.workflow.domain.service.client;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.layout.api.response.list.ListLayout;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class SalesService {

  private final String clientBasePath;

  @Autowired
  public SalesService(@Value("${client.sales.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Mono<List<ContactResponse>> getContactsByIds(List<Long> ids, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/contacts").queryParam("id", ids).queryParam("view", "full").build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(ContactResponse.class)
        .collectList();
  }

  public Mono<ListLayout> getListLayoutResponse(String entity, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/{entity}/layout/list", entity.toLowerCase()+"s")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(ListLayout.class);
  }
}
