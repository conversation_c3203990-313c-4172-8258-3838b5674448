package com.kylas.sales.workflow.domain.workflow.executionLogs;

import java.util.Map;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpMethod;

@Getter
public class MarketplaceLogPayload {
  private final String name;
  private final String description;
  private final String requestUrl;
  private final HttpMethod method;
  private final Map<String, Object> body;

  public MarketplaceLogPayload(String name, String description, String requestUrl, HttpMethod method, Map<String, Object> body) {
    this.name = name;
    this.description = description;
    this.requestUrl = requestUrl;
    this.method = method;
    this.body = ObjectUtils.isEmpty(body) ? null : body;
  }
}
