package com.kylas.sales.workflow.domain.processor.meeting;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.Actionable;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Organizer;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Participants;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;


@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
public class Meeting implements Serializable, Actionable {

  private String title;
  private String description;
  private ScheduledDate from;
  private ScheduledDate allDay;
  private IdName timezone;
  private String status;
  private List<Participants> participants;
  private String medium;
  private String location;
  private Map<String, Object> customFieldValues;


  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.meeting.update";
  }


  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ScheduledDate {

    private final Boolean allDay;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private final Date from;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private final Date to;

    @JsonCreator
    public ScheduledDate(
        @JsonProperty("allDay") Boolean allDay,
        @JsonProperty("from") Date from,
        @JsonProperty("to") Date to) {
      this.allDay = allDay;
      this.from = from;
      this.to = to;
    }
  }
}


