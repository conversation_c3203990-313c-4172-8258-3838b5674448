package com.kylas.sales.workflow.domain.processor.email;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EntitySummary {

  private final Long id;
  private final String name;
  private final String entity;
  private final String email;

  @JsonCreator
  public EntitySummary(
      @JsonProperty("id") Long id,
      @JsonProperty("name") String name,
      @JsonProperty("entity") String entity,
      @JsonProperty("email") String email) {
    this.id = id;
    this.name = name;
    this.entity = entity;
    this.email = email;
  }
}
