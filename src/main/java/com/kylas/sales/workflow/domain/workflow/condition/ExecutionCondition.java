package com.kylas.sales.workflow.domain.workflow.condition;

import com.kylas.sales.workflow.common.dto.condition.WorkflowCondition.ConditionExpression;
import com.kylas.sales.workflow.domain.workflow.ConditionType;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Entity
@Getter
@Setter
@Slf4j
public class ExecutionCondition extends AbstractWorkflowCondition implements Serializable {

  @Enumerated(value = EnumType.STRING)
  private ConditionType type;

  public ExecutionCondition(ConditionType type, ConditionExpression expression) {
    super(expression);
    this.type = type;
  }

  public ExecutionCondition(){ super(); }

  public ExecutionCondition(Long id, ConditionType type, ConditionExpression expression, Workflow workflow) {
    super(id, expression, workflow);
    this.type = type;
  }

  public ExecutionCondition update(ConditionType type, ConditionExpression expression) {
    return new ExecutionCondition(this.id, type, expression, this.workflow);
  }
}
