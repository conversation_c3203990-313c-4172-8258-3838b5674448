package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.CompanyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class CompanyService {

  private final String clientBasePath;

  @Autowired
  public CompanyService(@Value("${client.company.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Mono<IdName> getCompanyNameById(Long companyId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/companies").path("/" + companyId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(IdName.class);
  }

  public Mono<CompanyResponse> getCompanyById(Long companyId, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/companies").path("/" + companyId).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(CompanyResponse.class);
  }

  public Flux<FieldAttribute> getFieldAttributes(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/companies/fields")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(FieldAttribute.class);
  }

}
