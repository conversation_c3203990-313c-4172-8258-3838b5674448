package com.kylas.sales.workflow.domain.workflow.delayed.action;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
@Embeddable
@NoArgsConstructor
public class Recurrence implements Serializable {
  @Enumerated(value = EnumType.STRING)
  private RecurrenceType type;
  private String value;

  @JsonCreator
  public Recurrence(
         @JsonProperty("type") RecurrenceType type,
         @JsonProperty("value") String value
  ) {
    this.type = type;
    this.value = value;
  }

  public enum RecurrenceType{ FREQUENCY, UNTIL_DATE }
}
