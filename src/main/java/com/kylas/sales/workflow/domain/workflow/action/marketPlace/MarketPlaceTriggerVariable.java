package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MarketPlaceTriggerVariable implements Serializable {
  private String displayName;
  private String type;
  private String name;
}