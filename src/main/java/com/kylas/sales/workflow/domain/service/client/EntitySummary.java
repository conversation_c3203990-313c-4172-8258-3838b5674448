package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EntitySummary {

  private final Long id;
  private final String name;
  private final String entity;
  private final List<Email> emails;

  @JsonCreator
  public EntitySummary(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("entity") String entity,
      @JsonProperty("emails") List<Email> emails) {
    this.id = id;
    this.name = name;
    this.entity = entity;
    this.emails = emails;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Email {

    private final boolean primary;
    private final String value;

    @JsonCreator
    public Email(@JsonProperty("value") String value, @JsonProperty("primary") boolean primary) {
      this.value = value;
      this.primary = primary;
    }
  }
}
