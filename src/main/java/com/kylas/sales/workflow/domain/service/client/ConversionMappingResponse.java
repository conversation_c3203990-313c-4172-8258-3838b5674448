package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversionMappingResponse implements Serializable {

  private final List<MappingResponse> mapping;
  private final IdName createdBy;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private final Date createdAt;
  private final IdName updatedBy;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private final Date updatedAt;

  @JsonCreator
  public ConversionMappingResponse(@JsonProperty("mapping") List<MappingResponse> mapping, @JsonProperty("createdBy") IdName createdBy,
      @JsonProperty("createdAt") Date createdAt, @JsonProperty("updatedBy") IdName updatedBy, @JsonProperty("updatedAt") Date updatedAt) {
    this.mapping = mapping;
    this.createdBy = createdBy;
    this.createdAt = createdAt;
    this.updatedBy = updatedBy;
    this.updatedAt = updatedAt;
  }
}
