package com.kylas.sales.workflow.domain.processor;

import static com.kylas.sales.workflow.api.request.Condition.TriggerType.NEW_VALUE;
import static com.kylas.sales.workflow.mq.event.AbstractEntityEvent.getEntityEvent;
import static java.util.Collections.emptySet;
import static java.util.Objects.isNull;

import com.kylas.sales.workflow.api.WorkflowService;
import com.kylas.sales.workflow.api.request.Condition.TriggerType;
import com.kylas.sales.workflow.common.dto.condition.Operator;
import com.kylas.sales.workflow.common.dto.condition.WorkflowCondition.ConditionExpression;
import com.kylas.sales.workflow.domain.ConditionFacade;
import com.kylas.sales.workflow.domain.EditPropertyActionProcessor;
import com.kylas.sales.workflow.domain.ScheduledJobFacade;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.processor.email.EmailDetail;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail;
import com.kylas.sales.workflow.domain.processor.task.AssignToDetail;
import com.kylas.sales.workflow.domain.processor.task.TaskDetail;
import com.kylas.sales.workflow.domain.service.Searchable;
import com.kylas.sales.workflow.domain.service.client.CallLogSearchResponse;
import com.kylas.sales.workflow.domain.service.client.CompanySearchResponse;
import com.kylas.sales.workflow.domain.service.client.ContactSearchResponse;
import com.kylas.sales.workflow.domain.service.client.DealSearchResponse;
import com.kylas.sales.workflow.domain.service.client.EntityResponse;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse;
import com.kylas.sales.workflow.domain.service.client.MeetingSearchResponse;
import com.kylas.sales.workflow.domain.service.client.SearchResponse;
import com.kylas.sales.workflow.domain.service.client.SearchService;
import com.kylas.sales.workflow.domain.service.client.TaskSearchResponse;
import com.kylas.sales.workflow.domain.workflow.ConditionType;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob;
import com.kylas.sales.workflow.domain.workflow.TriggerFrequency;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.CopyConfiguration;
import com.kylas.sales.workflow.domain.workflow.action.EditPropertyAction;
import com.kylas.sales.workflow.domain.workflow.action.EditPropertyAction.EditPropertyActionType;
import com.kylas.sales.workflow.domain.workflow.action.EditPropertyFieldMapping;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import com.kylas.sales.workflow.domain.workflow.action.assignTo.AssignToAction;
import com.kylas.sales.workflow.domain.workflow.action.associatedActions.TriggerWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.associatedActions.TriggerWorkflowActionService;
import com.kylas.sales.workflow.domain.workflow.action.convertLead.ConvertLeadAction;
import com.kylas.sales.workflow.domain.workflow.action.convertLead.ConvertLeadService;
import com.kylas.sales.workflow.domain.workflow.action.email.EmailAction;
import com.kylas.sales.workflow.domain.workflow.action.email.EmailActionService;
import com.kylas.sales.workflow.domain.workflow.action.generateCallSummary.GenerateCallSummaryAction;
import com.kylas.sales.workflow.domain.workflow.action.generateCallSummary.GenerateCallSummaryService;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceService;
import com.kylas.sales.workflow.domain.workflow.action.reassign.ReassignAction;
import com.kylas.sales.workflow.domain.workflow.action.reassign.ReassignActionService;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageAction;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageService;
import com.kylas.sales.workflow.domain.workflow.action.share.ShareAction;
import com.kylas.sales.workflow.domain.workflow.action.share.ShareActionService;
import com.kylas.sales.workflow.domain.workflow.action.task.CreateTaskAction;
import com.kylas.sales.workflow.domain.workflow.action.task.CreateTaskService;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookService;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import com.kylas.sales.workflow.mq.ProcessEventsCommandPublisher;
import com.kylas.sales.workflow.mq.command.EntityUpdatedCommandPublisher;
import com.kylas.sales.workflow.mq.event.AbstractEntityEvent;
import com.kylas.sales.workflow.mq.event.EntityAction;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.MarketplaceTriggerEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import com.kylas.sales.workflow.security.AuthService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorkflowProcessor {

  private final WorkflowService workflowService;
  private final EntityUpdatedCommandPublisher entityUpdatedCommandPublisher;
  private final WebhookService webhookService;
  private final ConditionFacade conditionFacade;
  private final CreateTaskService createTaskService;
  private final EmailActionService emailActionService;
  private final ScheduledJobFacade scheduledJobFacade;
  private final Map<String, Searchable> searchableServiceMap;
  private final AuthService authService;
  private final MarketplaceService marketplaceService;
  private final SearchService searchService;
  private final TriggerWorkflowActionService triggerWorkflowActionService;
  private final ConvertLeadService convertLeadService;
  private final ShareActionService shareActionService;
  private final SendWhatsappMessageService sendWhatsappMessageService;
  private final GenerateCallSummaryService generateCallSummaryService;

  private final ReassignActionService reassignActionService;
  private final ProcessEventsCommandPublisher processEventsCommandPublisher;
  private final EditPropertyActionProcessor editPropertyActionProcessor;

  @Autowired
  public WorkflowProcessor(
      WorkflowService workflowService,
      EntityUpdatedCommandPublisher entityUpdatedCommandPublisher,
      WebhookService webhookService,
      ConditionFacade conditionFacade,
      CreateTaskService createTaskService, EmailActionService emailActionService,
      ScheduledJobFacade scheduledJobFacade, Map<String, Searchable> searchableServiceMap, AuthService authService, MarketplaceService marketplaceService,
      SearchService searchService, TriggerWorkflowActionService triggerWorkflowActionService, ConvertLeadService convertLeadService, ShareActionService shareActionService,
      SendWhatsappMessageService sendWhatsappMessageService, GenerateCallSummaryService generateCallSummaryService,
      ReassignActionService reassignActionService, ProcessEventsCommandPublisher processEventsCommandPublisher,
      EditPropertyActionProcessor editPropertyActionProcessor) {
    this.workflowService = workflowService;
    this.entityUpdatedCommandPublisher = entityUpdatedCommandPublisher;
    this.webhookService = webhookService;
    this.conditionFacade = conditionFacade;
    this.createTaskService = createTaskService;
    this.emailActionService = emailActionService;
    this.scheduledJobFacade = scheduledJobFacade;
    this.searchableServiceMap = searchableServiceMap;
    this.authService = authService;
    this.marketplaceService = marketplaceService;
    this.searchService = searchService;
    this.triggerWorkflowActionService = triggerWorkflowActionService;
    this.convertLeadService = convertLeadService;
    this.shareActionService = shareActionService;
    this.sendWhatsappMessageService = sendWhatsappMessageService;
    this.generateCallSummaryService = generateCallSummaryService;
    this.reassignActionService = reassignActionService;
    this.processEventsCommandPublisher = processEventsCommandPublisher;
    this.editPropertyActionProcessor = editPropertyActionProcessor;
  }

  public void processMarketPlaceTriggerWorkflow(MarketplaceTriggerEvent marketplaceTriggerEvent) {
    log.info("Request received to process marketplace trigger workflow in processor for triggerId:{} and tenantId:{} and requestPayload:{}", marketplaceTriggerEvent.getTriggerId(), marketplaceTriggerEvent.getTenantId(),marketplaceTriggerEvent.getRequestPayload());
    EntityType entityType = EntityType.valueOf(marketplaceTriggerEvent.getEntityType().toUpperCase());
    Map<String, Object> requestPayload = marketplaceTriggerEvent.getRequestPayload();
    Searchable searchable = getSearchableServiceForEntity(entityType);

    long mpEntityId = Long.parseLong(requestPayload.get("mpEntityId").toString());
    String authenticationToken = authService.getAuthenticationToken();

    SearchResponse searchResponse = searchable.getEntityDetails(
        marketplaceTriggerEvent.getEntityType().toLowerCase(),
        mpEntityId,
        authenticationToken,
        getSearchResponseClassForEntity(entityType)
    ).block();


    if(searchResponse !=null && searchResponse.getContent() != null) {
      log.info("Entity with given entity:{} and entityId:{} trigger workflow in processor for triggerId:{} and tenantId:{}", entityType, mpEntityId, marketplaceTriggerEvent.getTriggerId(), marketplaceTriggerEvent.getTenantId());
      executeMarketPlaceTriggerWorkflows(marketplaceTriggerEvent, searchResponse, entityType);
    }

  }

  private void executeMarketPlaceTriggerWorkflows(MarketplaceTriggerEvent marketplaceTriggerEvent, SearchResponse searchResponse, EntityType entityType) {
    EntityResponse entityResponse = searchResponse.getContent();
    log.info("Creating entity detail for entity:{} and tenantId:{}",marketplaceTriggerEvent.getEntityType(),marketplaceTriggerEvent.getTenantId());
    EntityDetail entityDetail = entityResponse.createFromEntityResponse(searchResponse.getMetadata());
    entityDetail.setMarketPlaceTriggerValues(marketplaceTriggerEvent.getRequestPayload());
    log.info("Created entity detail for entity:{} and tenantId:{}",marketplaceTriggerEvent.getEntityType(),marketplaceTriggerEvent.getTenantId());

    Metadata metadata =
        new Metadata(
            marketplaceTriggerEvent.getTenantId(),
            marketplaceTriggerEvent.getUserId(),
            EntityType.valueOf(marketplaceTriggerEvent.getEntityType()),
            null,
            emptySet(),EntityAction.MARKETPLACE_TRIGGER);

    AbstractEntityEvent entityEvent = getEntityEvent(EntityType.valueOf(marketplaceTriggerEvent.getEntityType()), entityDetail, metadata);

    List<Workflow> activeByMarketPlaceTrigger = workflowService.findActiveByMarketPlaceTrigger(marketplaceTriggerEvent.getTenantId(), entityType,
            TriggerFrequency.MARKETPLACE_TRIGGER, marketplaceTriggerEvent.getTriggerId())
        .stream()
        .filter(workflow ->
            !metadata.isProcessed(workflow.getId()) && satisfiesCondition(entityEvent, workflow))
        .collect(Collectors.toList());

    executeWorkflows(activeByMarketPlaceTrigger,metadata,entityEvent);
  }

  public void processDelayedAction(long workflowId, long jobId){
    long start = System.currentTimeMillis();
    log.info("Delayed action processing start workflowId: {}, JobId: {}", workflowId, jobId);
    Workflow workflow = workflowService.getById(workflowId);
    if(!workflow.isActive()){
      log.info("Workflow Inactive, Not processing delayed Action, workflowId: {} inactive, jobId: {}", workflowId, jobId);
      return;
    }
    Optional<ScheduledJob> job = scheduledJobFacade.getUnExecutedJob(workflowId, jobId);
    job.ifPresent(scheduledJob -> {
      Searchable searchable = getSearchableServiceForEntity(workflow.getEntityType());
      SearchResponse searchResponse = searchable
          .getEntityDetails(scheduledJob.getEntityType().getEntityName(), scheduledJob.getEntityId(), authService.getAuthenticationToken(),
              getSearchResponseClassForEntity(scheduledJob.getEntityType())).block();
        if(searchResponse !=null && searchResponse.getContent() != null){
          EntityResponse entityResponse = searchResponse.getContent();
          EntityDetail entityDetail = entityResponse.createFromEntityResponse(searchResponse.getMetadata());
          entityDetail.setMarketPlaceTriggerValues(scheduledJob.getMarketplaceTriggerPayload());
          var existingExecutedWorkflows = scheduledJob.getExecutedWorkflows();
          Set<String> updatedExecutedWorkflows = new HashSet<>();
          if (ObjectUtils.isNotEmpty(existingExecutedWorkflows)){
            updatedExecutedWorkflows.addAll(existingExecutedWorkflows);
          }
          var metaData = new Metadata(
              workflow.getTenantId(),
              authService.getLoggedInUser().getId(),
              workflow.getEntityType(),
              String.format("WF_%d", workflowId),
              updatedExecutedWorkflows,
              EntityAction.UPDATED
          ).withAllWorkflowIds(new HashSet<>(){{ add(workflowId); }})
          .withEntityId(entityDetail.getId()).withWorkflowName(workflow.getName());

          EntityEvent entityEvent = getEntityEvent(workflow.getEntityType(), entityDetail, metaData);
          if(satisfiesExecutionCondition(entityEvent, workflow)) {

            var associatedEntities = getAssociatedEntities(workflow, entityEvent);
            entityEvent.getEntity().setAssociatedEntities(associatedEntities);

            processActions(metaData, workflow.getWorkflowActions(), entityEvent);
            workflowService.updateExecutedEventDetails(workflow);
            scheduledJobFacade.markExecuted(scheduledJob, workflow);
          }else{
            scheduledJobFacade.markInvalid(scheduledJob, workflow);
          }
        }
    });
    log.info("Delayed action processing end workflowId: {}, JobId: {}, totalTimeTaken {}", workflowId, jobId, (System.currentTimeMillis() - start)/1000);
  }

  public void validateForProcessing(EntityEvent event){
    Metadata metadata = event.getMetadata();
    log.info("{} {} event received with metadata {}", metadata.getEntityType(), metadata.getEntityAction(), metadata);
    if (!metadata.canExecuteWorkflow()) {
      log.info("Disabling workflow execution for {} event on entity {}-{}",
          metadata.getEntityAction(), metadata.getEntityType().name(), metadata.getEntityId());
      return;
    }

    if(!workflowService.isWorkflowExistsByEntityEvent(event)){
      log.info("==No workflow exists for tenantId:{}==entityType:{}==entityAction:{}==", metadata.getTenantId(), metadata.getEntityType(), metadata.getEntityAction());
      return;
    }
    processEventsCommandPublisher.publish(event);
  }

  public void process(EntityEvent event) {
    Metadata metadata = event.getMetadata();
    var tenantId = metadata.getTenantId();
    log.info("{} {} event process started with metadata {}", metadata.getEntityType(), metadata.getEntityAction(), metadata);
    List<Long> workflowIds = workflowService.findActiveWorkflowByIdsByEntityTypeAndTriggerFrequency(tenantId, metadata.getEntityType(),
        metadata.getEntityAction());
    List<Workflow> workflows =
        workflowIds.stream().map(wfId -> workflowService.findById(wfId, tenantId))
        .filter(workflow ->
            !metadata.isProcessed(workflow.getId()) && satisfiesCondition(event, workflow))
        .collect(Collectors.toList());

    executeWorkflows(workflows,metadata,event);
  }

  public void processAssociatedEntityWorkflow(EntityEvent event, Long workflowId) {
    Metadata metadata = event.getMetadata();
    if (!metadata.canExecuteWorkflow()) {
      log.info("Disabling workflow execution for {} event on entity {}-{}",
          metadata.getEntityAction(), metadata.getEntityType().name(), metadata.getEntityId());
      return;
    }

    log.info("{} {} workflow event received with metadata {}", metadata.getEntityType(), metadata.getEntityAction(), metadata);
    Optional<Workflow> associatedWorkflow = workflowService.findActiveWorkflowByIdAndTriggerFrequency(workflowId,
        TriggerFrequency.ASSOCIATED);
    if (associatedWorkflow.isPresent() && !metadata.isProcessed(associatedWorkflow.get().getId()) && satisfiesCondition(event, associatedWorkflow.get())) {
      List<Workflow> workflows = new ArrayList<>(Arrays.asList(associatedWorkflow.get()));
      executeWorkflows(workflows, metadata, event);
    }
  }

  private void executeWorkflows(List<Workflow> workflows, Metadata metadata, EntityEvent event) {
    var workflowIds = workflows.stream().map(Workflow::getId).collect(Collectors.toSet());

    workflows.forEach(workflow -> {
      Metadata updatedMetadata = metadata.with(workflow.getId()).withAllWorkflowIds(workflowIds)
          .withEntityId(event.getEntityId()).withWorkflowName(workflow.getName());
      Set<AbstractWorkflowAction> workflowActions = workflow.getWorkflowActions();
      log.info("Workflow execution start for workflowId {} and prev metadata {}", workflow.getId(), metadata);
      if (workflow.getWorkflowTrigger().getTriggerType().equals(com.kylas.sales.workflow.domain.workflow.TriggerType.DELAYED)) {
        scheduledJobFacade.schedule(workflow, event.getEntity(), metadata.getExecutedWorkflows());
      } else {
        var associatedEntitiesMap = getAssociatedEntities(workflow, event);
        event.getEntity().setAssociatedEntities(associatedEntitiesMap);
        processActions(updatedMetadata, workflowActions, event);
        workflowService.updateExecutedEventDetails(workflow);
      }
    });
  }

  private boolean satisfiesCondition(EntityEvent event, Workflow workflow) {
     boolean isSatisfied = isNull(workflow.getWorkflowCondition()) ||
        workflow.getWorkflowCondition().getType().equals(ConditionType.FOR_ALL) ||
        satisfies(workflow.getWorkflowCondition().getExpression(), event, event.getMetadata().getEntityType(), false);
     log.info("Workflow conditions for workflowId : {} satisfies : {}",workflow.getId(), isSatisfied);
    return isSatisfied;
  }

  private boolean satisfiesExecutionCondition(EntityEvent event, Workflow workflow) {
    return workflow.getExecutionCondition().getType().equals(ConditionType.FOR_ALL) || satisfies(workflow.getExecutionCondition().getExpression(), event, event.getMetadata().getEntityType(), true);
  }

  private boolean satisfies(ConditionExpression expression, EntityEvent event, EntityType entityType, boolean isDelayedWf) {
    if (!TriggerType.IS_CHANGED.equals(expression.getTriggerOn())) {
      if (expression.getOperator().equals(Operator.AND)) {
        return satisfies(expression.getOperand1(), event, entityType, isDelayedWf) && satisfies(expression.getOperand2(), event, entityType, isDelayedWf);
      } else if (expression.getOperator().equals(Operator.OR)) {
        return satisfies(expression.getOperand1(), event, entityType, isDelayedWf) || satisfies(expression.getOperand2(), event, entityType, isDelayedWf);
      }
    } else if (TriggerType.IS_CHANGED.equals(expression.getTriggerOn()) && !TriggerType.NEW_VALUE.equals(expression.getTriggerOn())
        && !TriggerType.OLD_VALUE.equals(expression.getTriggerOn())) {
      return conditionFacade.satisfiesValueIsChanged(event, expression);
    }
    return satisfies(expression, event, isDelayedWf);
  }

  private boolean satisfies(ConditionExpression expression, EntityEvent event, boolean isDelayedWf) {
    if (NEW_VALUE.equals(expression.getTriggerOn())) {
      if (ObjectUtils.isNotEmpty(event.getMetadata()) && event.getMetadata().getEntityAction().equals(EntityAction.UPDATED) && !isDelayedWf) {
        return conditionFacade.satisfiesValueIsChanged(event, expression) && conditionFacade.satisfies(expression, event.getEntity());
      } else {
        return conditionFacade.satisfies(expression, event.getEntity());
      }
    }
    return conditionFacade.satisfies(expression, event.getOldEntity());
  }

  private void processActions(Metadata metadata, final Set<AbstractWorkflowAction> workflowActions, EntityEvent event) {
    long start = System.currentTimeMillis();
    log.info("Workflow action processing start entityType {} and entityId {} and metadata {}",metadata.getEntityType(),metadata.getEntityId(),metadata);

    editPropertyActionProcessor.processEditPropertyActions(workflowActions, metadata, event);

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.WEBHOOK))
        .map(workflowAction -> (WebhookAction) workflowAction).forEach(webhookAction ->
            webhookService.execute(webhookAction, event.getEntity(), event.getMetadata().getEntityType()));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.REASSIGN))
        .map(workflowAction -> (ReassignAction) workflowAction).findFirst().ifPresent(
            reassignAction -> reassignActionService.processReassignAction(metadata, event.getEntity(), reassignAction));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.SHARE))
            .map(workflowAction -> (ShareAction) workflowAction).forEach(shareAction -> shareActionService
                    .processShareAction(metadata, event, shareAction));

    workflowActions.stream()
        .filter(workflowAction -> workflowAction.getType().equals(ActionType.CREATE_TASK))
        .map(workflowAction -> (CreateTaskAction) workflowAction)
        .forEach(
            createTaskAction -> {
              log.info(
                  "Processing task creation, entity: {}, metadata:{}", event.getEntity(), metadata);
              createTaskService.processCreateTaskAction(
                  createTaskAction,
                  metadata.getEntityType(),
                  event.getEntity(),
                  metadata);
            });

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.SEND_EMAIL))
        .map(workflowAction -> (EmailAction) workflowAction)
        .forEach(emailAction -> emailActionService.processEmailAction(emailAction, event, metadata));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.MARKETPLACE_ACTION))
        .map(workflowAction -> (MarketplaceAction) workflowAction).filter(activeMarketplaceAction->activeMarketplaceAction.getActive())
        .forEach(marketplaceAction ->
            marketplaceService.execute(marketplaceAction, event.getEntity(), event.getMetadata().getEntityType()));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.ASSIGN_TO))
        .map(workflowAction -> (AssignToAction) workflowAction).findFirst().ifPresent(
            assignToAction -> entityUpdatedCommandPublisher
                .execute(metadata, new AssignToDetail(event.getEntityId(), assignToAction.getAssignTo()), false));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.TRIGGER_WORKFLOW))
        .map(workflowAction -> (TriggerWorkflowAction) workflowAction)
        .forEach(triggerWorkflowAction -> triggerWorkflowActionService.executeTriggerWorkflowAction(triggerWorkflowAction, event.getEntity()));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.CONVERT_LEAD))
        .map(workflowAction -> (ConvertLeadAction) workflowAction).findFirst()
        .ifPresent(convertLeadAction -> convertLeadService.processConvertAction(convertLeadAction, event, metadata));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.SEND_WHATSAPP_MESSAGE))
        .map(workflowAction -> (SendWhatsappMessageAction) workflowAction)
        .forEach(sendWhatsappAction -> sendWhatsappMessageService.processSendWhatsappAction(sendWhatsappAction, event, metadata));

    workflowActions.stream().filter(workflowAction -> workflowAction.getType().equals(ActionType.GENERATE_CALL_SUMMARY))
        .map(workflowAction -> (GenerateCallSummaryAction) workflowAction).findFirst().ifPresent(
            generateCallSummaryAction -> generateCallSummaryService.processGenerateCallSummaryAction(metadata, event, generateCallSummaryAction));

    log.info("Workflow action processing end entityType {} and entityId {} totalTimeTaken {} and metadata {}",metadata.getEntityType(),metadata.getEntityId(),(System.currentTimeMillis() - start)/1000, metadata);

  }

  private Map<EntityType, List<EntityDetail>> getAssociatedEntities(Workflow workflow, EntityEvent event) {
    Map<EntityType, List<EntityDetail>> associatedEntitiesMap = new HashMap<>();

    var relatedToByEntityGroup = getAssociatedEntityIdsByEntityGroup(event);
    for (Entry<String, List<Long>> entry : relatedToByEntityGroup.entrySet()) {
      String entity = entry.getKey();
      List<Long> ids = entry.getValue();
      EntityType entityType = EntityType.valueOf(entity.toUpperCase());

      if(!isAnyAssociatedEntityActionPresent(workflow, entityType) && !shouldFetchAssociatedEntities(workflow, event)){
        continue;
      }

      List<EntityDetail> associatedEntities = getAssociatedEntitiesDetail(entityType, ids);
      if (ObjectUtils.isEmpty(associatedEntities)) {
        continue;
      }
      associatedEntitiesMap.put(entityType, associatedEntities);
    }
    return associatedEntitiesMap;
  }

  private boolean shouldFetchAssociatedEntities(Workflow workflow, EntityEvent entityEvent) {
    List<ActionType> actionTypes = workflow.getWorkflowActions().stream().map(AbstractWorkflowAction::getType).collect(Collectors.toList());

    if (workflow.getEntityType() == EntityType.TASK && actionTypes.contains(ActionType.EDIT_PROPERTY)) {
      TaskDetail taskDetail = (TaskDetail) entityEvent.getEntity();
      if (ObjectUtils.isEmpty(taskDetail.getRelations())){
        return false;
      }

      List<EditPropertyAction> editPropertyActions = workflow.getWorkflowActions().stream()
          .filter(workflowAction -> workflowAction.getType().equals(ActionType.EDIT_PROPERTY))
          .map(EditPropertyAction.class::cast).collect(Collectors.toList());
      if (ObjectUtils.isEmpty(editPropertyActions)) {
        return false;
      }

      List<EditPropertyAction> copyEditProperties = editPropertyActions.stream()
          .filter(editPropertyAction -> editPropertyAction.getEditActionType().equals(EditPropertyActionType.COPY))
          .collect(Collectors.toList());

      for (EditPropertyAction editPropertyAction : copyEditProperties){
        CopyConfiguration copyConfiguration = editPropertyAction.getCopyConfiguration();
        if (copyConfiguration == null){
          return false;
        }
        List<EditPropertyFieldMapping> copyMappings = copyConfiguration.getVariables();
        if (copyMappings.isEmpty()){
          return false;
        }
        List<String> fromFields = copyMappings.stream().map(EditPropertyFieldMapping::getFieldName).collect(Collectors.toList());
        if (fromFields.contains("relation")){
          return true;
        }
      }
    }
    return false;
  }

  private List<EntityDetail> getAssociatedEntitiesDetail(
      EntityType entityType,
      List<Long> entityIds
  ) {
      SearchResponse searchResponse = searchService.getAssociatedEntityDetails(entityType.getEntityName().toLowerCase(), entityIds, authService.getAuthenticationToken(),
          getSearchResponseClassForEntity(entityType)).block();

      if (ObjectUtils.isEmpty(searchResponse) || ObjectUtils.isEmpty(searchResponse.getResponse())) {
        return Collections.emptyList();
      }

    return searchResponse.getResponse()
        .stream().map(entityResponse -> entityResponse.createFromEntityResponse(searchResponse.getMetadata())).collect(Collectors.toList());
  }

  private Map<String, List<Long>> getAssociatedEntityIdsByEntityGroup(EntityEvent event) {
    if (event.getEntity() instanceof CallLogDetail) {
      CallLogDetail callLog = (CallLogDetail) event.getEntity();
      return callLog.getRelatedToByEntityGroup(callLog.getRelatedTo());
    }
    if (event.getEntity() instanceof MeetingDetail){
      MeetingDetail meetingDetail = (MeetingDetail)event.getEntity();
      return meetingDetail.getRelatedToByEntityGroup(meetingDetail.getRelatedTo());
    }
    if (event.getEntity() instanceof TaskDetail){
      TaskDetail taskDetail = (TaskDetail) event.getEntity();
      return taskDetail.getRelatedToByEntityGroup(taskDetail.getRelations());
    }
    if (event.getEntity() instanceof EmailDetail) {
      EmailDetail emailDetail = (EmailDetail) event.getEntity();
      return emailDetail.getRelatedToByEntityGroup(emailDetail.getRelatedTo());
    }
    return Collections.emptyMap();
  }

  private boolean isAnyAssociatedEntityActionPresent(Workflow workflow, EntityType entityType) {
    List<ActionType> actionTypes = workflow.getWorkflowActions().stream().map(AbstractWorkflowAction::getType).collect(Collectors.toList());
    if (actionTypes.contains(ActionType.WEBHOOK) || actionTypes.contains(ActionType.TRIGGER_WORKFLOW)) {
      List<AbstractWorkflowAction> webhookActions = workflow.getWorkflowActions().stream()
          .filter(action -> action.getType().equals(ActionType.WEBHOOK)).collect(Collectors.toList());

      boolean isWebhookActionPresent = webhookActions.stream().anyMatch(webhookAction ->
          ((WebhookAction) webhookAction).getParameters().stream()
              .anyMatch(param -> param.getEntity().equals(getWebhookEntity(entityType))
              ));
      if(isWebhookActionPresent){
        return true;
      }
      List<AbstractWorkflowAction> triggerActions = workflow.getWorkflowActions().stream()
          .filter(action -> action.getType().equals(ActionType.TRIGGER_WORKFLOW)).collect(Collectors.toList());
      return triggerActions.stream().anyMatch(triggerAction ->
          ((TriggerWorkflowAction) triggerAction).getAssociatedWorkflow().getEntityType().equals(entityType));
    }
    return false;
  }

  private WebhookEntity getWebhookEntity(EntityType entityType) {
    if (EntityType.LEAD.equals(entityType)) {
      return WebhookEntity.ASSOCIATED_LEAD;
    }
    if (EntityType.DEAL.equals(entityType)){
      return WebhookEntity.ASSOCIATED_DEAL;
    }
    if (EntityType.CONTACT.equals(entityType)) {
      return WebhookEntity.ASSOCIATED_CONTACT;
    }
    if (EntityType.COMPANY.equals(entityType)) {
      return WebhookEntity.ASSOCIATED_COMPANY;
    }
    return null;
  }

   private Class getSearchResponseClassForEntity(EntityType entityType) {
    switch (entityType){
      case LEAD:
        return LeadSearchResponse.class;
      case DEAL:
        return DealSearchResponse.class;
      case CONTACT:
        return ContactSearchResponse.class;
      case CALL_LOG:
        return CallLogSearchResponse.class;
      case TASK:
        return TaskSearchResponse.class;
      case MEETING:
        return MeetingSearchResponse.class;
      case EMAIL:
        return EmailDetail.class;
      case COMPANY:
        return CompanySearchResponse.class;
    }
    return null;
  }

  private Searchable getSearchableServiceForEntity(EntityType entityType) {
    switch (entityType) {
      case CALL_LOG:
        return searchableServiceMap.get("CALL");
      case TASK:
        return searchableServiceMap.get("PRODUCTIVITY");
      case MEETING:
        return searchableServiceMap.get("MEETING");
      case EMAIL:
        return searchableServiceMap.get("EMAIL");
    }
    return searchableServiceMap.get("SEARCH");
  }

}
