package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class PickListValue {

  private final Long id;
  private final String displayName;
  private final String name;
  private final boolean systemDefault;
  private final boolean disabled;

  @JsonCreator
  public PickListValue(
      @JsonProperty("id") Long id, @JsonProperty("displayName") String displayName,
      @JsonProperty("name") String name, @JsonProperty("systemDefault") boolean systemDefault,
      @JsonProperty("disabled") boolean disabled) {
    this.id = id;
    this.displayName = displayName;
    this.name = name;
    this.systemDefault = systemDefault;
    this.disabled = disabled;
  }
}
