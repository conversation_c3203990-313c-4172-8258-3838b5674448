package com.kylas.sales.workflow.domain.workflow.action.share;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.persistence.AttributeConverter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TextToJsonConverter implements AttributeConverter<Action, String> {

  @Override
  public String convertToDatabaseColumn(Action action) {
    try {
      return new ObjectMapper().writeValueAsString(action);
    } catch (JsonProcessingException e) {
      log.error("Error while converting share action: {} to text, error: {}", action, e.getMessage());
      return null;
    }
  }

  @Override
  public Action convertToEntityAttribute(String s) {
    try {
      return new ObjectMapper().readValue(s, Action.class);
    } catch (Exception e) {
      log.error("Error while converting text: {} to share action, error: {}", s, e.getMessage());
      return null;
    }
  }
}
