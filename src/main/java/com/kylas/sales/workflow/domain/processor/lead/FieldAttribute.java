package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.service.client.PickListResponse;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldAttribute {
  private final String name;
  private final String displayName;
  private final boolean standard;
  private final String type;
  private boolean internal;
  private PickListResponse pickList;
  private boolean active;
  private boolean marketPlaceField;

  @JsonCreator
  public FieldAttribute(@JsonProperty("name") String name, @JsonProperty("displayName") String displayName,
      @JsonProperty("standard") boolean standard, @JsonProperty("type") String type,
      @JsonProperty("internal") boolean internal, @JsonProperty("picklist") PickListResponse pickList,
      @JsonProperty("active") boolean active) {
    this.name = name;
    this.displayName = displayName;
    this.standard = standard;
    this.type = type;
    this.internal = internal;
    this.pickList = pickList;
    this.active = active;
  }

  public FieldAttribute(String name, String displayName, boolean standard, String type) {
    this.name = name;
    this.displayName = displayName;
    this.standard = standard;
    this.type = type;
  }

  public FieldAttribute(String name, String displayName, boolean standard, String type, boolean marketPlaceField) {
    this.name = name;
    this.displayName = displayName;
    this.standard = standard;
    this.type = type;
    this.marketPlaceField = marketPlaceField;
  }

  public FieldAttribute(String name, String displayName, boolean standard, String type, boolean internal, boolean active) {
    this.name = name;
    this.displayName = displayName;
    this.standard = standard;
    this.type = type;
    this.internal = internal;
    this.active = active;
  }
}
