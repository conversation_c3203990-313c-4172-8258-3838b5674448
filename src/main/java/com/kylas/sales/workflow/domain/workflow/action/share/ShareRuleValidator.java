package com.kylas.sales.workflow.domain.workflow.action.share;

import com.kylas.sales.workflow.domain.exception.InsufficientPrivilegeForShareActionException;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.user.Permission;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.security.InternalAuthProvider;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class ShareRuleValidator {

  private final UserService userService;
  private final InternalAuthProvider authProvider;

  @Autowired
  public ShareRuleValidator(UserService userService, InternalAuthProvider authProvider) {
    this.userService = userService;
    this.authProvider = authProvider;
  }

  public Mono<Boolean> validate(long userId, long tenantId, EntityType entity, Action shareRuleAction){
    String token = authProvider.create(userId, tenantId);
    return userService
        .getUserDetails(userId, token)
        .map(userDetails -> {
          Optional<Permission> entityPermission = userDetails.getPermissions().stream()
              .filter(p -> p.getName().equals(entity.getEntityName()))
              .findFirst();
          return entityPermission.map(permission ->
                {
                  com.kylas.sales.workflow.domain.user.Action action = permission.getAction();
                  var valid = (shareRuleAction.isRead() == action.isRead() || action.isRead())  && (shareRuleAction.isUpdate() == action.isUpdate() || action.isUpdate())
                      && (shareRuleAction.isTask() == action.isTask() || action.isTask()) && (shareRuleAction.isNote() == action.isNote() || action.isNote())
                      && (shareRuleAction.isMeeting() == action.isMeeting() || action.isMeeting()) && (shareRuleAction.isEmail() == action.isEmail() || action.isEmail())
                      && (shareRuleAction.isCall() == action.isCall() || action.isCall()) && (shareRuleAction.isSms() == action.isSms() || action.isSms()) && (shareRuleAction.isDocument() == action.isDocument() || action.isDocument());
                  if(!valid){
                    throw new InsufficientPrivilegeForShareActionException();
                  }
                  return true;
                }
              ).orElseThrow(InsufficientPrivilegeForShareActionException::new);
        });
  }
}
