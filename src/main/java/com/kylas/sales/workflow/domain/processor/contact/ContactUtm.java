package com.kylas.sales.workflow.domain.processor.contact;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
public class ContactUtm implements Serializable {
  private String subSource;
  private String utmSource;
  private String utmCampaign;
  private String utmMedium;
  private String utmContent;
  private String utmTerm;

  public ContactUtm(String subSource, String utmSource, String utmCampaign, String utmMedium, String utmContent, String utmTerm) {
    this.subSource = subSource;
    this.utmSource = utmSource;
    this.utmCampaign = utmCampaign;
    this.utmMedium = utmMedium;
    this.utmContent = utmContent;
    this.utmTerm = utmTerm;
  }

  public void populateContactUtm(String utmField, Object utmFieldValue) {
    switch (utmField) {
      case "subSource":
        this.setSubSource(utmFieldValue.toString());
        break;
      case "utmSource":
        this.setUtmSource(utmFieldValue.toString());
        break;
      case "utmCampaign":
        this.setUtmCampaign(utmFieldValue.toString());
        break;
      case "utmMedium":
        this.setUtmMedium(utmFieldValue.toString());
        break;
      case "utmContent":
        this.setUtmContent(utmFieldValue.toString());
        break;
      case "utmTerm":
        this.setUtmTerm(utmFieldValue.toString());
        break;
    }
  }
}


