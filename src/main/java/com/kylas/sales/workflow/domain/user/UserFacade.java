package com.kylas.sales.workflow.domain.user;

import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.mq.event.UserEventV2;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserFacade {

  private final UserRepository userRepository;

  @Autowired
  public UserFacade(UserRepository userRepository) {
    this.userRepository = userRepository;
  }

  public User getExistingOrCreateNewUser(User userDetails, long tenantId) {
    return userRepository
        .findByIdAndTenantId(userDetails.getId(), tenantId)
        .map(
            user -> {
              var updatedUser = user.withName(userDetails.getName()).withEmail(userDetails.getEmail()).withTimezone(userDetails.getTimezone())
                  .withTenantUser(userDetails.isTenantUser()).withReportingManagers(userDetails.getReportingManagers());
              return userRepository.saveAndFlush(updatedUser);
            })
        .orElse(createNewUser(userDetails, tenantId))
        .withPermissions(userDetails.getPermissions());
  }

  public User createNewUser(User userDetails, Long tenantId){
    return userRepository.saveAndFlush(userDetails.withTenantUser(userDetails.isTenantUser()).withTenantId(tenantId));
  }

  public Optional<User> tryGetUserByIdAndTenantId(Long userId, Long tenantId) {
    return userRepository.findByIdAndTenantId(userId, tenantId);
  }

  public Optional<User> tryGetTenantUser(Long tenantId) {
    return userRepository.findByTenantIdAndTenantUserTrue(tenantId);
  }

  public Optional<User> tryUpdateUser(Long userId, Long tenantId, UserEventV2 userEventV2) {
    return tryGetUserByIdAndTenantId(userId, tenantId)
        .map(user -> user.withName(userEventV2.getFirstName(), userEventV2.getLastName())
            .withEmail(userEventV2.getEmail())
            .withTimezone(userEventV2.getTimezone())
            .withTenantUser(userEventV2.isTenantUser())
            .withReportingManagers(Optional.ofNullable(userEventV2.getReportingManagers())
                .orElseGet(Collections::emptySet).stream().map(IdName::getId).collect(Collectors.toList())))
        .map(userRepository::saveAndFlush);
  }
}
