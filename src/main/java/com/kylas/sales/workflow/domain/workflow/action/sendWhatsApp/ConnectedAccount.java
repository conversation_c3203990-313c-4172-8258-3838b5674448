package com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class ConnectedAccount implements Serializable {

  private Long id;
  private String name;

  @JsonCreator
  public ConnectedAccount(@JsonProperty("id") Long id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }

}
