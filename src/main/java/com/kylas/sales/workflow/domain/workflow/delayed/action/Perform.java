package com.kylas.sales.workflow.domain.workflow.delayed.action;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
@Embeddable
@NoArgsConstructor
public class Perform implements Serializable {
  @Enumerated(value = EnumType.STRING)
  private TriggerType trigger;
  private Integer time;
  @Enumerated(value = EnumType.STRING)
  private TimeUnit unit;

  @JsonCreator
  public Perform(
          @JsonProperty("trigger") TriggerType trigger,
          @JsonProperty("time") Integer time,
          @JsonProperty("unit") TimeUnit unit) {
    this.trigger = trigger;
    this.time = time;
    this.unit = unit;
  }

  public enum TimeUnit {
    MINUTES, HOURS, WEEKS, DAYS, MONTHS, YEARS
  }

  public enum TriggerType {
    BEFORE, AFTER
  }
}
