package com.kylas.sales.workflow.domain.workflow.action;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.EDIT_PROPERTY;
import static java.util.Objects.isNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.common.dto.condition.FieldCategory;
import com.kylas.sales.workflow.domain.CopyConfigurationJsonUserType;
import com.kylas.sales.workflow.domain.EditPropertyFieldMappingJsonUserType;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.exception.InvalidCopyConfigurationException;
import com.kylas.sales.workflow.domain.exception.InvalidCopyFromFieldException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.CallLogAttribute;
import com.kylas.sales.workflow.layout.api.response.list.FieldType;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
@TypeDef(name = "CopyConfigurationJsonUserType", typeClass = CopyConfigurationJsonUserType.class)
public class EditPropertyAction extends AbstractWorkflowAction implements com.kylas.sales.workflow.domain.workflow.action.WorkflowAction {

  private String name;
  @Convert(converter = ValueConverter.class)
  private Object value;
  @Enumerated(value = EnumType.STRING)
  private ValueType valueType;
  @JsonProperty(value = "isStandard")
  @Column(name = "is_standard")
  private boolean standard;
  @Enumerated(EnumType.STRING)
  private EditPropertyActionType editActionType;

  @Type(type = "CopyConfigurationJsonUserType")
  @Column(columnDefinition = "jsonb")
  private CopyConfiguration copyConfiguration;

  private EditPropertyAction(String name, Object value, ValueType valueType, boolean standard, EditPropertyActionType editPropertyActionType, CopyConfiguration copyConfiguration) {
    this.name = name;
    this.value = value;
    this.valueType = valueType;
    this.standard = standard;
    this.editActionType =editPropertyActionType;
    this.copyConfiguration = copyConfiguration;
  }


  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    ActionDetail.EditPropertyAction payload = (ActionDetail.EditPropertyAction) actionResponse.getPayload();
    validate(payload);
    return new EditPropertyAction(payload.getName(), payload.getValue(), payload.getValueType(), payload.isStandard(),
        payload.getEditActionType() != null ? payload.getEditActionType() : EditPropertyActionType.CUSTOM,
        payload.getCopyConfiguration());
  }

  private static void validate(ActionDetail.EditPropertyAction payload) {
    if (EditPropertyActionType.COPY.equals(payload.getEditActionType())) {
      CopyConfiguration copyConfiguration = payload.getCopyConfiguration();
      if (isNull(copyConfiguration) || StringUtils.isEmpty(copyConfiguration.getText())) {
        log.error("Invalid Action with empty values {}", payload);
        throw new InvalidActionException();
      }
      String text = copyConfiguration.getText();
      Matcher matcher = Pattern.compile("\\{\\{\\d+\\}\\}").matcher(text);
      int numberOfBraces = 0;
      while (matcher.find()) {
        numberOfBraces++;
      }
      if (copyConfiguration.getVariables().size() != numberOfBraces) {
        log.error("Invalid Action with invalid variables length {}", payload);
        throw new InvalidCopyConfigurationException();
      }
      List<String> variables = copyConfiguration.getVariables().stream()
          .map(EditPropertyFieldMapping::getVariable).collect(Collectors.toList());
      variables.removeIf(StringUtils::isEmpty);
      if (variables.size() != numberOfBraces) {
        log.error("Invalid Action with invalid variable values {}", payload);
        throw new InvalidCopyConfigurationException();
      }
      List<String> invalidFromFields = List.of("phoneNumbers", "companyPhones");
      for (EditPropertyFieldMapping editPropertyFieldMapping : copyConfiguration.getVariables()){
        if (invalidFromFields.contains(editPropertyFieldMapping.getFieldName())
            || editPropertyFieldMapping.getFieldType().equals(FieldType.PHONE)){
          log.error("PhoneNumber fields are not supported into copy from field {}", payload);
          throw new InvalidCopyFromFieldException();
        }
      }
    }
  }

  public static ActionResponse toActionResponse(EditPropertyAction editPropertyAction) {
    Object value = editPropertyAction.value;
    if (("ivrNumber").equals(editPropertyAction.getName()) && value != null && !(value instanceof String)) {
      value = String.valueOf(value);
    }
    return new ActionResponse(editPropertyAction.getId(), EDIT_PROPERTY,
        new ActionDetail.EditPropertyAction(editPropertyAction.name, value, editPropertyAction.valueType, editPropertyAction.standard,editPropertyAction.editActionType, editPropertyAction.copyConfiguration));
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public EditPropertyAction update(ActionResponse action) {
    var payload = (ActionDetail.EditPropertyAction) action.getPayload();
    validate(payload);
    this.setName(payload.getName());
    this.setValue(payload.getValue());
    this.setValueType(payload.getValueType());
    this.setStandard(payload.isStandard());
    this.setEditActionType(payload.getEditActionType());
    this.setCopyConfiguration(payload.getCopyConfiguration());
    return this;
  }

  @Override
  public ActionType getType() {
    return EDIT_PROPERTY;
  }

  public enum EditPropertyActionType {
    CUSTOM,
    COPY,
    APPEND,
    REPLACE
  }
}
