package com.kylas.sales.workflow.domain.processor.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Embeddable
@Getter
public class GPSCoordinateEvent {

  private Double lat;
  private Double lon;

  @JsonCreator
  public GPSCoordinateEvent(@JsonProperty("lat") Double latitude,
      @JsonProperty("lon") Double longitude) {
    this.lat = latitude;
    this.lon = longitude;
  }

}
