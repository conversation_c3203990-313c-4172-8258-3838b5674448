package com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kylas.sales.workflow.domain.processor.lead.ToSendPhoneType.SendType;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@NoArgsConstructor
@ToString
public class SendWhatsappMessageType implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @JsonIgnore
  private long id;

  private String name;

  @Enumerated(value = EnumType.STRING)
  private SendType type;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(name = "whatsapp_message_action_id")
  private SendWhatsappMessageAction sendWhatsappMessageAction;

}
