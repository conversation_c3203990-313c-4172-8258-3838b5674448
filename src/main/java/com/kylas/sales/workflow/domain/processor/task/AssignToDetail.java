package com.kylas.sales.workflow.domain.processor.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.Actionable;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonInclude(Include.NON_NULL)
public class AssignToDetail implements Serializable, Actionable {

  private final Long entityId;
  private final Long assignedTo;

  public AssignToDetail(Long entityId, Long assignedTo) {
    this.entityId = entityId;
    this.assignedTo = assignedTo;
  }

  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.task.assign";
  }
}
