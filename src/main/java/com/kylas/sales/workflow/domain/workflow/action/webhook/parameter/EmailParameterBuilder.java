package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static java.util.Collections.emptyList;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.springframework.http.HttpMethod.GET;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.email.EmailDetail;
import com.kylas.sales.workflow.domain.processor.email.EmailDetail.EmailAttachment;
import com.kylas.sales.workflow.domain.processor.email.EntitySummary;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.EmailAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EmailParameterBuilder extends ParameterBuilder {

  private final AssociatedEntityParameterBuilder associatedEntityParameterBuilder;

  @Autowired
  EmailParameterBuilder(UserService userService,
      ConfigService configService, AssociatedEntityParameterBuilder associatedEntityParameterBuilder) {
    super(userService, configService);
    this.associatedEntityParameterBuilder = associatedEntityParameterBuilder;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return EntityType.EMAIL.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    EmailDetail email = (EmailDetail) entityDetail;
    var buildParamObject = getTenantIfRequired(webhookAction, jwtToken)
        .map(tuple ->
            webhookAction.getParameters().stream()
                .map(parameter -> {
                  Object entity = email;
                  ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
                  if (WebhookEntity.TENANT.equals(parameter.getEntity())) {
                    entity = tuple;
                  } else if (WebhookEntity.EMAIL.equals(parameter.getEntity())) {
                    if (parameter.getAttribute().equals(EmailAttribute.OPENED_AT.getName())) {
                      return new SimpleEntry<>(paramKey, getOpenedAt(webhookAction.getMethod(), email));
                    } else if (parameter.getAttribute().equals(EmailAttribute.SENT_BY.getName())) {
                      return new SimpleEntry<>(paramKey, getFormattedSender(webhookAction.getMethod(), email));
                    } else if (parameter.getAttribute().equals(EmailAttribute.RECEIVED_BY.getName())) {
                      return new SimpleEntry<>(paramKey, getFormattedRecipients(webhookAction.getMethod(), email));
                    } else if (parameter.getAttribute().equals(EmailAttribute.ATTACHMENTS.getName())) {
                      return new SimpleEntry<>(paramKey, getFormattedAttachments(webhookAction.getMethod(), email));
                    } else if (parameter.getAttribute().equals(EmailAttribute.CLICKED_AT.getName())) {
                      return new SimpleEntry<>(paramKey, getFormattedLinksClickedHistory(webhookAction.getMethod(), email));
                    } else if (parameter.getAttribute().equals(EmailAttribute.RELATED_TO.getName())) {
                      return new SimpleEntry<>(paramKey, getFormattedRelations(webhookAction.getMethod(), email));
                    }
                  } else if (AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity())) {
                    return new SimpleEntry<>(new ParamKey("", ""), emptyList());
                  }
                  return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
                })
                .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
        ).block();

    if (webhookAction.getParameters().stream().anyMatch(parameter -> AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity()))
        && !webhookAction.getMethod().equals(HttpMethod.GET)) {
      Map<ParamKey, List<Object>> associatedEntity = associatedEntityParameterBuilder.getAssociatedEntity(email.getAssociatedEntities(), jwtToken, webhookAction);
      buildParamObject.putAll(associatedEntity);
    }

    return buildParamObject;
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    try {
      return getNestedProperty(entity, parameter.fetchPathToField());
    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return null;
  }

  private List<Object> getOpenedAt(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || ObjectUtils.isEmpty(email.getOpenedAt())) {
      return Collections.emptyList();
    }
    return email.getOpenedAt().stream().map(Date::toString).collect(Collectors.toList());
  }

  private List<Object> getFormattedSender(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || ObjectUtils.isEmpty(email.getSentBy())) {
      return Collections.emptyList();
    }
    if (method == GET) {
      return List.of(email.getSentBy().getEmail());
    }
    return List.of(email.getSentBy());
  }

  private List<Object> getFormattedAttachments(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || ObjectUtils.isEmpty(email.getAttachments())) {
      return Collections.emptyList();
    }
    if (method == GET) {
      return email.getAttachments().stream()
          .map(EmailAttachment::getId).collect(Collectors.toList());
    }
    return new ArrayList<>(email.getAttachments());
  }

  private List<Object> getFormattedLinksClickedHistory(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || ObjectUtils.isEmpty(email.getLinksClickedAt())) {
      return Collections.emptyList();
    }

    if (method == GET) {
      return email.getLinksClickedAt().stream()
          .flatMap(linkHistory -> linkHistory.getClickedAt().stream())
          .map(Date::toString)
          .collect(Collectors.toList());
    }
    return email.getLinksClickedAt().stream()
        .map(linkHistory -> {
          var linkHistoryMap = new HashMap<>();
          linkHistoryMap.put("url", linkHistory.getUrl());
          linkHistoryMap.put("clickedAt", linkHistory.getClickedAt().stream()
              .map(Date::toString).collect(Collectors.toList()));
          return linkHistoryMap;
        })
        .collect(Collectors.toList());
  }

  private List<Object> getFormattedRelations(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || ObjectUtils.isEmpty(email.getRelatedTo())) {
      return Collections.emptyList();
    }
    if (method == GET) {
      return email.getRelatedTo().stream()
          .map(EntitySummary::getId).collect(Collectors.toList());
    }
    return new ArrayList<>(email.getRelatedTo());
  }

  private List<Object> getFormattedRecipients(HttpMethod method, EmailDetail email) {
    if (ObjectUtils.isEmpty(email) || isEmptyRecipients(email)) {
      return Collections.emptyList();
    }
    if (method == GET) {
      return Stream.of(email.getToRecipients(), email.getCcRecipients(), email.getBccRecipients())
          .filter(Objects::nonNull)
          .flatMap(Collection::stream)
          .map(EntitySummary::getEmail)
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
    }
    return Stream.of(email.getToRecipients(), email.getCcRecipients(), email.getBccRecipients())
        .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
  }

  private boolean isEmptyRecipients(EmailDetail email) {
    return ObjectUtils.isEmpty(email.getToRecipients()) && ObjectUtils.isEmpty(email.getCcRecipients())
        && ObjectUtils.isEmpty(email.getBccRecipients());
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    return null;
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entity, String authToken) {
    return null;
  }
}
