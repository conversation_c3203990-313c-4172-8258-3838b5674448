package com.kylas.sales.workflow.domain.service;

import com.kylas.sales.workflow.config.WebConfig;
import com.kylas.sales.workflow.domain.processor.lead.FieldAttribute;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.service.client.ConversionMappingResponse;
import com.kylas.sales.workflow.layout.api.response.list.ListLayout;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class ConfigService {

  private final String clientBasePath;

  @Autowired
  public ConfigService(@Value("${client.config.basePath}") String clientBasePath) {
    this.clientBasePath = clientBasePath;
  }

  public Flux<FieldAttribute> getFieldAttributes(String entity, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/entities/{entity}/fields", entity.toLowerCase())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(FieldAttribute.class);
  }

  public Flux<IdName> getCurrency(List<Long> currencyIds, String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/currencies").queryParam("id", currencyIds).build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToFlux(IdName.class);
  }

  public Mono<ConversionMappingResponse> getConversionMapping(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri(uriBuilder -> uriBuilder.path("/v1/conversion-mappings").build())
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(ConversionMappingResponse.class);
  }

  public Mono<ListLayout> getListLayoutResponse(String authenticationToken) {
    return WebConfig.getWebClientBuilder()
        .baseUrl(clientBasePath)
        .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + authenticationToken)
        .build()
        .get()
        .uri("/v1/ui/layouts/list/task")
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(ListLayout.class);
  }

  public Flux<IdName> getAllCurrencies(String authenticationToken) {
    return getCurrency(Collections.emptyList(), authenticationToken);
  }
}
