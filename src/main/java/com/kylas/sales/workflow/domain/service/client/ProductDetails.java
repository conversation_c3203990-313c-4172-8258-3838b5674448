package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductDetails {

  private final long id;
  private final String name;
  private final PriceDetail priceDetail;


  @JsonCreator
  public ProductDetails(
      @JsonProperty("id") long id,
      @JsonProperty("name") String name,
      @JsonProperty("price") PriceDetail priceDetail) {
    this.id = id;
    this.name = name;
    this.priceDetail = priceDetail;
  }
}
