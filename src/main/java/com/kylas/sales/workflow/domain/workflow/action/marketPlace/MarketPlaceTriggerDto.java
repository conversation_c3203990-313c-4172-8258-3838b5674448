package com.kylas.sales.workflow.domain.workflow.action.marketPlace;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class MarketPlaceTriggerDto implements Serializable {

  private UUID id;
  private UUID appId;
  private String name;
  private String entity;
  private List<MarketPlaceTriggerVariable> triggerVariables;


  @JsonCreator
  public MarketPlaceTriggerDto(@JsonProperty("id") String id, @JsonProperty("appId") String appId, @JsonProperty("name") String name,
      @JsonProperty("entity") String entity, @JsonProperty("triggerVariables") List<MarketPlaceTriggerVariable> triggerVariables) {
    this.id = UUID.fromString(id);
    this.appId = UUID.fromString(appId);
    this.name = name;
    this.entity = entity;
    this.triggerVariables = triggerVariables;
  }
}