package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.deal.DealDetail;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;


@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class DealSearchResponse implements SearchResponse {
  private final List<DealDetail> content;

  @JsonCreator
  public DealSearchResponse(@JsonProperty("content") List<DealDetail> content) {
    this.content = content;
  }

  @Override
  public DealDetail getContent() {
    return this.content.isEmpty() ? null : this.content.get(0);
  }

  @Override
  public Metadata getMetadata() {
    return null;
  }

  @Override
  public List<EntityResponse> getResponse() {
    return this.content.isEmpty() ? Collections.emptyList() : this.content.stream().collect(Collectors.toList());
  }
}
