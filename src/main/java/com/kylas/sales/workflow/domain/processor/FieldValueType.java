package com.kylas.sales.workflow.domain.processor;

import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.ARRAY;
import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.ID_NAME;
import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.OBJECT;
import static com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType.PLAIN;
import static java.util.Objects.isNull;

import com.kylas.sales.workflow.common.dto.ActionDetail.EditPropertyAction.ValueType;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface FieldValueType {

  class LeadFieldValueType implements FieldValueType {

    @Getter
    @AllArgsConstructor
    private enum FieldValueTypes {
      PRODUCTS("products", ARRAY),
      PHONE_NUMBERS("phoneNumbers", ARRAY),
      EMAILS("emails", ARRAY),
      COMPANY_PHONES("companyPhones", ARRAY),
      CONVERSION_ASSOCIATION("conversionAssociation", OBJECT),
      PIPELINE("pipeline", OBJECT),
      OTHER("other", PLAIN);

      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      if (isNull(valueType)) {
        return true;
      }
      FieldValueTypes fieldValueTypes = Arrays
          .stream(FieldValueTypes.values()).filter(value -> value.getFieldName().equals(name)).findAny()
          .orElse(FieldValueTypes.OTHER);
      if (fieldValueTypes.equals(FieldValueTypes.OTHER) && (valueType.equals(PLAIN) || valueType
          .equals(ID_NAME) || valueType.equals(ARRAY))) {
        return false;
      }
      return !fieldValueTypes.getValueType().equals(valueType);
    }
  }

  class DealFieldValueType implements FieldValueType {

    @Getter
    @AllArgsConstructor
    private enum FieldValueTypes {
      OWNED_BY("ownedBy", OBJECT),
      ESTIMATED_VALUE("estimatedValue", OBJECT),
      ACTUAL_VALUE("actualValue", OBJECT),
      PRODUCT("product", OBJECT),
      PRODUCTS("products", ARRAY),
      PIPELINE("pipeline", OBJECT),
      ASSOCIATED_CONTACTS("associatedContacts", ARRAY),
      COMPANY("company", OBJECT),
      OTHER("other", PLAIN);



      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      if (isNull(valueType)) {
        return true;
      }
      FieldValueTypes fieldValueTypes = Arrays
          .stream(FieldValueTypes.values()).filter(value -> value.getFieldName().equals(name)).findAny()
          .orElse(FieldValueTypes.OTHER);
      if (fieldValueTypes.equals(DealFieldValueType.FieldValueTypes.OTHER) && (valueType.equals(PLAIN) || valueType
          .equals(ID_NAME)|| valueType.equals(ARRAY))) {
        return false;
      }
      return !fieldValueTypes.getValueType().equals(valueType);
    }
  }

  class ContactFieldValueType implements FieldValueType {

    @Getter
    @AllArgsConstructor
    private enum FieldValueTypes {
      PHONE_NUMBERS("phoneNumbers", ARRAY),
      ASSOCIATED_DEALS("associatedDeals", ARRAY),
      COMPANY("company", OBJECT),
      EMAILS("emails", ARRAY),
      OTHER("other", PLAIN);

      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      if (isNull(valueType)) {
        return true;
      }
      FieldValueTypes fieldValueTypes = Arrays
          .stream(FieldValueTypes.values()).filter(value -> value.getFieldName().equals(name)).findAny()
          .orElse(FieldValueTypes.OTHER);
      if (fieldValueTypes.equals(ContactFieldValueType.FieldValueTypes.OTHER) && (valueType.equals(PLAIN) || valueType
          .equals(ID_NAME) || valueType.equals(ARRAY))) {
        return false;
      }
      return !fieldValueTypes.getValueType().equals(valueType);
    }
  }

  class CallLogFieldValueType implements FieldValueType {

    @Getter
    @AllArgsConstructor
    private enum FieldValueTypes {
      OTHER("other", PLAIN);

      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      return isNull(valueType);
    }
  }

  class TaskFieldValueType implements FieldValueType {

    @Getter
    @AllArgsConstructor
    private enum FieldValueTypes {
      DUE_DATE("dueDate", OBJECT),
      OTHER("other", PLAIN);

      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      if (isNull(valueType)) {
        return true;
      }
      TaskFieldValueType.FieldValueTypes fieldValueTypes = Arrays
          .stream(TaskFieldValueType.FieldValueTypes.values()).filter(value -> value.getFieldName().equals(name)).findAny()
          .orElse(TaskFieldValueType.FieldValueTypes.OTHER);
      if (fieldValueTypes.equals(TaskFieldValueType.FieldValueTypes.OTHER) && (valueType.equals(PLAIN) || valueType
          .equals(ID_NAME))) {
        return false;
      }
      return !fieldValueTypes.getValueType().equals(valueType);
    }
  }

  class MeetingFieldValueType implements FieldValueType{

    @Getter
    @AllArgsConstructor
    public enum FieldValueTypes{
      FROM("from", OBJECT),
      ALL_DAY("allDay", OBJECT),
      PARTICIPANTS("participants", ARRAY),
      TIMEZONE("timezone", OBJECT),
      OTHER("other", PLAIN );

      private final String fieldName;
      private final ValueType valueType;
    }

    @Override
    public boolean isInValidValueType(String name, ValueType valueType) {
      if (isNull(valueType)) {
        return true;
      }
      MeetingFieldValueType.FieldValueTypes fieldValueTypes = Arrays
          .stream(MeetingFieldValueType.FieldValueTypes.values()).filter(value -> value.getFieldName().equals(name)).findAny()
          .orElse(MeetingFieldValueType.FieldValueTypes.OTHER);
      if (fieldValueTypes.equals(MeetingFieldValueType.FieldValueTypes.OTHER) && (valueType.equals(PLAIN) || valueType
          .equals(ID_NAME))) {
        return false;
      }
      return !fieldValueTypes.getValueType().equals(valueType);
    }
  }

  boolean isInValidValueType(String name, ValueType valueType);
}
