package com.kylas.sales.workflow.domain.service.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.callLog.CallLogDetail;
import com.kylas.sales.workflow.domain.service.client.LeadSearchResponse.Metadata;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class CallLogSearchResponse implements SearchResponse{
  private final List<CallLogDetail> content;

  @JsonCreator
  public CallLogSearchResponse(@JsonProperty("content") List<CallLogDetail> content) {
    this.content = content;
  }

  @Override
  public CallLogDetail getContent() {
    return this.content.isEmpty() ? null : this.content.get(0);
  }

  @Override
  public Metadata getMetadata() {
    return null;
  }

  @Override
  public List<EntityResponse> getResponse() {
    return null;
  }
}
