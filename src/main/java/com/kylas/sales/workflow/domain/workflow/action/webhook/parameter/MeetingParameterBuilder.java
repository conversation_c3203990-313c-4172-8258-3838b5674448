package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CREATED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.MEETING;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.ORGANIZER;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.UPDATED_BY;
import static java.util.Collections.emptyList;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.springframework.http.HttpMethod.GET;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.Participants;
import com.kylas.sales.workflow.domain.processor.meeting.MeetingDetail.RelatedTo;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MarketplaceEntity;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.MeetingAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class MeetingParameterBuilder extends ParameterBuilder{

  private final AssociatedEntityParameterBuilder associatedEntityParameterBuilder;

  @Autowired
  protected MeetingParameterBuilder(UserService userService,
      ConfigService configService, AssociatedEntityParameterBuilder associatedEntityParameterBuilder) {
    super(userService, configService);
    this.associatedEntityParameterBuilder = associatedEntityParameterBuilder;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return EntityType.MEETING.equals(entityType);
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + parameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        }
      }
      return getNestedProperty(entity, parameter.fetchPathToField());

    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return actualValue;
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    MeetingDetail meeting = (MeetingDetail) entityDetail;
    var buildParamObject =  Mono.zip(
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.ORGANIZER, new IdName(meeting.getOrganizer().getId(), meeting.getOrganizer().getName())),
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.CREATED_BY, meeting.getCreatedBy()),
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.UPDATED_BY, meeting.getUpdatedBy()),
        getTenantIfRequired(webhookAction, jwtToken)
    ).map(tuple ->
        webhookAction.getParameters().stream()
            .map(parameter -> {
              Object entity = meeting;
              ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
              if (parameter.getEntity().equals(ORGANIZER)) {
                entity = UserDetails.from(tuple.getT1());
              } else if (parameter.getEntity().equals(CREATED_BY)) {
                entity = UserDetails.from(tuple.getT2());
              } else if (parameter.getEntity().equals(UPDATED_BY)) {
                entity = UserDetails.from(tuple.getT3());
              } else if (parameter.getEntity().equals(TENANT)) {
                entity = tuple.getT4();
              } else if (parameter.getEntity().equals(MEETING)) {
                if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.RELATED_TO.getName())) {
                  return new SimpleEntry<>(paramKey, getFormattedRelations(webhookAction.getMethod(), meeting));
                } else if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.INVITEES.getName())){
                  return new SimpleEntry<>(paramKey, getFormattedParticipants(webhookAction.getMethod(), meeting));
                } else if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.ORGANIZER.getName())){
                  return new SimpleEntry<>(paramKey, getFormattedOrganizer(webhookAction.getMethod(), meeting));
                }
              } else if (AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity())){
                return new SimpleEntry<>(new ParamKey("",""),emptyList());
              }
              return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
            })
            .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
            .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
    ).block();

    if (webhookAction.getParameters().stream().anyMatch(parameter -> AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity()))
        && !webhookAction.getMethod().equals(HttpMethod.GET)) {
      Map<ParamKey, List<Object>> associatedEntity = associatedEntityParameterBuilder.getAssociatedEntity(meeting.getAssociatedEntities(), jwtToken, webhookAction);
      buildParamObject.putAll(associatedEntity);
    }
    return buildParamObject;
  }

  private List<Object> getFormattedOrganizer(HttpMethod method, MeetingDetail meeting) {
    if (ObjectUtils.isEmpty(meeting) || ObjectUtils.isEmpty(meeting.getOrganizer())) {
      return null;
    }
    if (method == GET){
      return List.of(meeting.getOrganizer().getName());
    }
    return List.of(meeting.getOrganizer());
  }

  private List<Object> getFormattedParticipants(HttpMethod method, MeetingDetail meeting) {
    if (ObjectUtils.isEmpty(meeting) || ObjectUtils.isEmpty(meeting.getParticipants())) {
      return null;
    }
    if (method == GET) {
      return meeting.getParticipants().stream()
          .map(Participants::getId)
          .collect(Collectors.toList());
    }
    return new ArrayList<>(meeting.getParticipants());
  }

  private List<Object> getFormattedRelations(HttpMethod method, MeetingDetail meeting) {
    if (ObjectUtils.isEmpty(meeting) || ObjectUtils.isEmpty(meeting.getRelatedTo())) {
      return null;
    }
    if (method == GET) {
      return meeting.getRelatedTo().stream()
          .map(RelatedTo::getId)
          .collect(Collectors.toList());
    }
    return new ArrayList<>(meeting.getRelatedTo());
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entityDetail, String jwtToken) {
    MeetingDetail meeting = (MeetingDetail) entityDetail;
    return Mono.zip(
        getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.ORGANIZER, new IdName(meeting.getOrganizer().getId(), meeting.getOrganizer().getName())),
        getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.CREATED_BY, meeting.getCreatedBy()),
        getUserIfRequired(marketplaceAction, jwtToken, MarketplaceEntity.UPDATED_BY, meeting.getUpdatedBy()),
        getTenantIfRequired(marketplaceAction, jwtToken)
    ).map(tuple ->
        marketplaceAction.getParameters().stream()
            .map(parameter -> {
              Object entity = meeting;
              ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
              if (parameter.getEntity().equals(ORGANIZER)) {
                entity = UserDetails.from(tuple.getT1());
              } else if (parameter.getEntity().equals(CREATED_BY)) {
                entity = UserDetails.from(tuple.getT2());
              } else if (parameter.getEntity().equals(UPDATED_BY)) {
                entity = UserDetails.from(tuple.getT3());
              } else if (parameter.getEntity().equals(TENANT)) {
                entity = tuple.getT4();
              } else if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.RELATED_TO.getName())) {
                return new SimpleEntry<>(paramKey, getFormattedRelations(marketplaceAction.getMethod(), meeting));
              } else if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.INVITEES.getName())){
                return new SimpleEntry<>(paramKey, getFormattedParticipants(marketplaceAction.getMethod(), meeting));
              }else if (parameter.getAttribute().equalsIgnoreCase(MeetingAttribute.ORGANIZER.getName())){
                return new SimpleEntry<>(paramKey, getFormattedOrganizer(marketplaceAction.getMethod(), meeting));
              }
              return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
            })
            .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
            .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
    ).block();
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter marketplaceActionParameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!marketplaceActionParameter.isStandard()) {
        try {
          return getNestedProperty(entity, "customFieldValues." + marketplaceActionParameter.getAttribute() + ".name");
        } catch (NoSuchMethodException e) {
          return getMappedProperty(entity, "customFieldValues", marketplaceActionParameter.getAttribute());
        }
      }
      var fetchPathToFieldOptional = marketplaceActionParameter.fetchPathToField();
      if (fetchPathToFieldOptional.isPresent()){
        return getNestedProperty(entity, marketplaceActionParameter.fetchPathToField().get());

      }

    } catch (NestedNullException ignored) {
    } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
      log.error("Exception occurred while getting actual value for {}", marketplaceActionParameter.getName());
    }
    return actualValue;
  }
}
