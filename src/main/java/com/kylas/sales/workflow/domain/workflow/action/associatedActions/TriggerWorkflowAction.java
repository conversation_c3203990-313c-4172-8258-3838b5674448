package com.kylas.sales.workflow.domain.workflow.action.associatedActions;

import static java.util.Objects.isNull;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidActionException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;


@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class TriggerWorkflowAction extends AbstractWorkflowAction implements WorkflowAction {

  @ManyToOne
  @JoinColumn(name = "associated_workflow_id")
  private Workflow associatedWorkflow;

  public TriggerWorkflowAction(Workflow workflow){
    this.associatedWorkflow = workflow;
  }

  public static ActionResponse toActionResponse(TriggerWorkflowAction action) {
    var associatedWorkflow = action.associatedWorkflow;
    return new ActionResponse(action.getId(), ActionType.TRIGGER_WORKFLOW,
        new ActionDetail.TriggerWorkflowAction(associatedWorkflow.getId(), associatedWorkflow.getName(), associatedWorkflow.getEntityType()));
  }

  public static AbstractWorkflowAction createNew(ActionResponse actionResponse) {
    var payload = (ActionDetail.TriggerWorkflowAction) actionResponse.getPayload();
    if (isNull(payload.getId())) {
      log.error("Invalid Triggered Workflow Action {}", actionResponse);
      throw new InvalidActionException();
    }
    return new TriggerWorkflowAction(Workflow.createReference(payload.getId()));
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse actionResponse) {
    var payload = (ActionDetail.TriggerWorkflowAction) actionResponse.getPayload();
    if (isNull(payload.getId())) {
      log.error("Invalid Triggered Workflow Action {}", actionResponse);
      throw new InvalidActionException();
    }
    this.associatedWorkflow = Workflow.createReference(payload.getId());
    return this;
  }

  @Override
  public ActionType getType() {
    return ActionType.TRIGGER_WORKFLOW;
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }
}
