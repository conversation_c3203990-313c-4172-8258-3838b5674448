package com.kylas.sales.workflow.domain.workflow.action.webhook.parameter;

import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.CREATED_BY;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TASK_ASSIGNEE;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.TENANT;
import static com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity.UPDATED_BY;
import static java.util.Collections.emptyList;
import static org.apache.commons.beanutils.BeanUtils.getMappedProperty;
import static org.apache.commons.beanutils.BeanUtils.getNestedProperty;
import static org.springframework.http.HttpMethod.GET;

import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.task.RelationEvent;
import com.kylas.sales.workflow.domain.processor.task.TaskDetail;
import com.kylas.sales.workflow.domain.service.ConfigService;
import com.kylas.sales.workflow.domain.service.UserService;
import com.kylas.sales.workflow.domain.user.UserDetails;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import com.kylas.sales.workflow.domain.workflow.action.webhook.WebhookAction;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.TaskAttribute;
import com.kylas.sales.workflow.domain.workflow.action.webhook.attribute.AttributeFactory.WebhookEntity;
import java.lang.reflect.InvocationTargetException;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class TaskParameterBuilder extends ParameterBuilder {

  private final AssociatedEntityParameterBuilder associatedEntityParameterBuilder;

  @Autowired
  public TaskParameterBuilder(UserService userService,
      ConfigService configService, AssociatedEntityParameterBuilder associatedEntityParameterBuilder) {
    super(userService, configService);
    this.associatedEntityParameterBuilder = associatedEntityParameterBuilder;
  }

  @Override
  public boolean canBuild(EntityType entityType) {
    return EntityType.TASK.equals(entityType);
  }

  @Override
  public Map<ParamKey, List<Object>> build(WebhookAction webhookAction, EntityDetail entityDetail, String jwtToken) {
    TaskDetail task = (TaskDetail) entityDetail;
    var buildParamObject  = Mono.zip(
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.TASK_ASSIGNEE, task.getAssignedTo()),
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.CREATED_BY, task.getCreatedBy()),
        getUserIfRequired(webhookAction, jwtToken, WebhookEntity.UPDATED_BY, task.getUpdatedBy()),
        getTenantIfRequired(webhookAction, jwtToken)
    ).map(tuple ->
        webhookAction.getParameters().stream()
            .map(parameter -> {
              Object entity = task;
              ParamKey paramKey = new ParamKey(parameter.getName(), parameter.getAttribute());
              if (parameter.getEntity().equals(TASK_ASSIGNEE)) {
                entity = UserDetails.from(tuple.getT1());
              } else if (parameter.getEntity().equals(CREATED_BY)) {
                entity = UserDetails.from(tuple.getT2());
              } else if (parameter.getEntity().equals(UPDATED_BY)) {
                entity = UserDetails.from(tuple.getT3());
              } else if (parameter.getEntity().equals(TENANT)) {
                entity = tuple.getT4();
              } else if (WebhookEntity.TASK.equals(parameter.getEntity()) && parameter.getAttribute().equalsIgnoreCase(TaskAttribute.RELATION.getName())) {
                return new SimpleEntry<>(paramKey, getFormattedRelations(webhookAction.getMethod(), task));
              } else if (AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity())){
                return new SimpleEntry<>(new ParamKey("",""),emptyList());
              }
              return new SimpleEntry<>(paramKey, getParameterValue(parameter, entity, jwtToken));
            })
            .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
            .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))
    ).block();

    if (webhookAction.getParameters().stream().anyMatch(parameter -> AssociatedEntityParameterBuilder.ASSOCIATED_ENTITIES.contains(parameter.getEntity()))
        && !webhookAction.getMethod().equals(HttpMethod.GET)) {
      Map<ParamKey, List<Object>> associatedEntity = associatedEntityParameterBuilder.getAssociatedEntity(task.getAssociatedEntities(), jwtToken, webhookAction);
      buildParamObject.putAll(associatedEntity);
    }
    return buildParamObject;
  }

  private List<Object> getFormattedRelations(HttpMethod method, TaskDetail taskDetail) {
    if (ObjectUtils.isEmpty(taskDetail) || ObjectUtils.isEmpty(taskDetail.getRelations())) {
      return null;
    }
    if (method == GET) {
      return taskDetail.getRelations().stream()
          .map(RelationEvent::getEntityId)
          .collect(Collectors.toList());
    }
    return new ArrayList<>(taskDetail.getRelations());
  }

  @Override
  protected String getPropertyValue(Object entity, Parameter parameter, String jwtToken) {
    String actualValue = null;
    try {
      if (!parameter.isStandard() && entity instanceof EntityDetail) {
        TaskDetail task = (TaskDetail) entity;
        actualValue = getMappedProperty(entity, "customFieldValues", parameter.getAttribute());
        if (ObjectUtils.isNotEmpty(actualValue) && task.getIdNameStore().containsKey(parameter.getAttribute())) {
          return task.getIdNameStore().getOrDefault(parameter.getAttribute(), new HashMap<>())
              .getOrDefault(actualValue, actualValue);
        }
        return actualValue;
      }
      return getNestedProperty(entity, parameter.fetchPathToField());
    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
      log.error("Exception occurred while getting actual value for {}", parameter.getName());
    }
    return null;
  }

  @Override
  public Map<ParamKey, List<Object>> build(MarketplaceAction marketplaceAction, EntityDetail entity, String authToken) {
    return null;
  }

  @Override
  protected String getPropertyValue(Object entity, MarketplaceActionParameter parameter, String jwtToken) {
    return null;
  }

}
