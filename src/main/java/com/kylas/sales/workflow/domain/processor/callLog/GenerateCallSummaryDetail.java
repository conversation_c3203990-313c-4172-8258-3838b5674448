package com.kylas.sales.workflow.domain.processor.callLog;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.kylas.sales.workflow.domain.processor.Actionable;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonInclude(Include.NON_NULL)
public class GenerateCallSummaryDetail implements Serializable, Actionable   {

  private Long callLogId;
  private String prompt;

  public GenerateCallSummaryDetail(Long callLogId, String prompt) {
    this.callLogId = callLogId;
    this.prompt = prompt;
  }

  @Override
  @JsonIgnore
  public String getEventName() {
    return "workflow.generate.callLog.summary";
  }
}
