package com.kylas.sales.workflow.domain.workflow.action.email;

import static com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType.SEND_EMAIL;

import com.kylas.sales.workflow.common.dto.ActionDetail;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.domain.exception.InvalidWorkflowRequestException;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.action.AbstractWorkflowAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class EmailAction extends AbstractWorkflowAction implements WorkflowAction {

  private long emailTemplateId;

  @NotNull
  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "email_from_id")
  private EmailRecipient from;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
  @JoinTable(name = "email_to",
      joinColumns = @JoinColumn(name = "email_action_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "email_recipient_id", referencedColumnName = "id"))
  private Set<EmailRecipient> to;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
  @JoinTable(name = "email_cc",
      joinColumns = @JoinColumn(name = "email_action_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "email_recipient_id", referencedColumnName = "id"))
  private Set<EmailRecipient> cc;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
  @JoinTable(name = "email_bcc",
      joinColumns = @JoinColumn(name = "email_action_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "email_recipient_id", referencedColumnName = "id"))
  private Set<EmailRecipient> bcc;

  private boolean trackingEnabled;
  public EmailAction(long emailTemplateId, EmailRecipient from, Set<EmailRecipient> to,
      Set<EmailRecipient> cc, Set<EmailRecipient> bcc,boolean trackingEnabled) {
    this.emailTemplateId = emailTemplateId;
    this.from = from;
    this.to = to;
    this.cc = cc;
    this.bcc = bcc;
    this.trackingEnabled=trackingEnabled;
  }

  public static AbstractWorkflowAction createNew(ActionResponse action) {
    return EmailActionMapper.fromActionResponse(action);
  }

  public static ActionResponse toActionResponse(EmailAction workflowAction) {
    return new ActionResponse(workflowAction.getId(), workflowAction.getType(), EmailActionMapper.fromWorkflowEmailAction(workflowAction));
  }

  @Override
  public AbstractWorkflowAction update(ActionResponse action) {
    var payload = (ActionDetail.EmailAction) action.getPayload();
    EmailActionMapper.validate(payload);
    this.setEmailTemplateId(payload.getEmailTemplateId());
    this.setFrom(payload.getFrom());
    this.setTo(payload.getTo());
    this.setCc(payload.getCc());
    this.setBcc(payload.getBcc());
    this.setTrackingEnabled(payload.isTrackingEnabled());
    return this;
  }

  @Override
  public void setWorkflow(Workflow workflow) {
    super.setWorkflow(workflow);
  }

  @Override
  public ActionType getType() {
    return SEND_EMAIL;
  }

  @Component
  private static class EmailActionMapper {

    private static EmailAction fromActionResponse(ActionResponse actionResponse) {
      ActionDetail.EmailAction emailAction = (ActionDetail.EmailAction) actionResponse.getPayload();
      validate(emailAction);

      return new EmailAction(emailAction.getEmailTemplateId(),
          emailAction.getFrom(),
          emailAction.getTo(),
          emailAction.getCc(),
          emailAction.getBcc(),
          emailAction.isTrackingEnabled());
    }

    private static ActionDetail.EmailAction fromWorkflowEmailAction(EmailAction emailAction) {
      return new ActionDetail.EmailAction(emailAction.getEmailTemplateId(),
          emailAction.getFrom(),
          emailAction.getTo(),
          emailAction.getCc(),
          emailAction.getBcc(),
          emailAction.isTrackingEnabled());
    }

    private static void validate(ActionDetail.EmailAction payload) {

      EmailRecipient emailRecipient = payload.getFrom();
      if (!emailRecipient.getEntity().equals(EmailEntityType.USER.getEntityName())) {
        throw new InvalidWorkflowRequestException();
      }
    }
  }
}
