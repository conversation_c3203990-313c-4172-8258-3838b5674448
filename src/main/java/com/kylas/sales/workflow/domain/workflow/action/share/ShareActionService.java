package com.kylas.sales.workflow.domain.workflow.action.share;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.domain.processor.EntityDetail;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.mq.command.CreateShareRuleCommand;
import com.kylas.sales.workflow.mq.command.CreateShareRuleCommandPublisher;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.mq.event.Metadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class ShareActionService {

    private final CreateShareRuleCommandPublisher createShareRuleCommandPublisher;

    private final ObjectMapper objectMapper;

    @Autowired
    public ShareActionService(CreateShareRuleCommandPublisher createShareRuleCommandPublisher, ObjectMapper objectMapper) {
        this.createShareRuleCommandPublisher = createShareRuleCommandPublisher;
        this.objectMapper = objectMapper;
    }

    public static final Map<EntityType, String> RECORD_PROPERTIES_MAP = new HashMap<>();
    static {
        RECORD_PROPERTIES_MAP.put(EntityType.RECORD_CREATED_BY, "createdBy");
        RECORD_PROPERTIES_MAP.put(EntityType.RECORD_IMPORTED_BY, "importedBy");
        RECORD_PROPERTIES_MAP.put(EntityType.RECORD_CONVERTED_BY, "convertedBy");
    }

    public void processShareAction(Metadata metadata, EntityEvent event, ShareAction shareAction){
        CreateShareRuleCommand createShareRuleCommand = getCreateShareRuleCommand(metadata, shareAction, event);
        if (ObjectUtils.isNotEmpty(createShareRuleCommand)){
            createShareRuleCommandPublisher.execute(metadata.getWorkflowName(), metadata.getEntityId(), metadata.getEntityType(), createShareRuleCommand);
        }
    }

    public CreateShareRuleCommand getCreateShareRuleCommand(Metadata metadata, ShareAction shareAction, EntityEvent event){
        EntityDetail oldDetail = event.getOldEntity();
        EntityDetail newDetail = event.getEntity();
        Long entityId = newDetail.getId();
        Long newOwner = newDetail.getOwner().getId();
        EntityType toType = shareAction.getToType();
        String entityName = newDetail.getEntityName();

        if (toType.equals(EntityType.USER) || toType.equals(EntityType.TEAM)){
            return createShareRuleCommand(metadata, entityId, shareAction, newOwner, toType, shareAction.getToId(), entityName);
        }

        Map entityJson = objectMapper.convertValue(newDetail, Map.class);
        Object recordProperty = entityJson.get(RECORD_PROPERTIES_MAP.get(toType));
        if (ObjectUtils.isNotEmpty(recordProperty)){
            IdName user = objectMapper.convertValue(recordProperty, IdName.class);
            if (user.getId() != null){
                return createShareRuleCommand(metadata, entityId, shareAction, newOwner, EntityType.USER, user.getId(), entityName);
            }
            log.info("User not found on property {} for entityId {} userId {} and tenantId {}", recordProperty, entityId, metadata.getUserId(), metadata.getTenantId());
        }

        if (shareAction.getToType().equals(EntityType.RECORD_OLD_OWNER)){
            if (ObjectUtils.isNotEmpty(oldDetail.getOwner()) && ObjectUtils.isNotEmpty(oldDetail.getOwner().getId())){
                Long toId = oldDetail.getOwner().getId();
                return createShareRuleCommand(metadata, entityId, shareAction, newOwner, EntityType.USER, toId, entityName);
            }
            log.info("Old record user not found for entityId {} userId {} and tenantId {}", entityId, metadata.getUserId(), metadata.getTenantId());
        }
        return null;
    }

    private CreateShareRuleCommand createShareRuleCommand(Metadata metadata, Long entityId, ShareAction shareAction, Long fromId, EntityType toType, Long toId, String entityName) {
        return new CreateShareRuleCommand(shareAction.getName(),
                String.format("Share rule created by workflow: %s", metadata.getWorkflowName()),
                EntityType.USER, fromId, toType, toId,
                false, entityId, metadata.getEntityType(), shareAction.getPermissions(), metadata.getTenantId(), fromId, false, Long.parseLong(metadata.getWorkflowId().split("_")[1]), entityName);
    }
}
