package com.kylas.sales.workflow.common.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerVariable;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MarketPlaceTriggerResponse implements Serializable {

  private final UUID id;
  private final String name;
  private final UUID appId;
  private final List<MarketPlaceTriggerVariable> triggerVariables;

  @JsonCreator
  public MarketPlaceTriggerResponse(@JsonProperty("id") UUID id, @JsonProperty("name") String name, @JsonProperty("appId") UUID appId,
      @JsonProperty("triggerVariables") List<MarketPlaceTriggerVariable> triggerVariables) {
    this.id =id;
    this.name = name;
    this.appId = appId;
    this.triggerVariables = triggerVariables;
  }
}
