package com.kylas.sales.workflow.common.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MarketPlaceTriggerRequest implements Serializable {

  private final UUID id;
  private final String name;
  private final UUID appId;

  @JsonCreator
  public MarketPlaceTriggerRequest(@JsonProperty("id") String id, @JsonProperty("name") String name, @JsonProperty("appId") String appId) {
    this.id = UUID.fromString(id);
    this.name = name;
    this.appId = UUID.fromString(appId);
  }
}
