package com.kylas.sales.workflow.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.kylas.sales.workflow.common.dto.ActionDetail.AssignToAction;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ActionResponse {

  private UUID id;

  private ActionType type;

  @JsonTypeInfo(use = Id.NAME, property = "type", include = As.EXTERNAL_PROPERTY)
  @JsonSubTypes({
      @JsonSubTypes.Type(value = ActionDetail.EditPropertyAction.class, name = "EDIT_PROPERTY"),
      @JsonSubTypes.Type(value = ActionDetail.WebhookAction.class, name = "WEBHOOK"),
      @JsonSubTypes.Type(value = ActionDetail.ReassignAction.class, name = "REASSIGN"),
      @JsonSubTypes.Type(value = ActionDetail.CreateTaskAction.class, name = "CREATE_TASK"),
      @JsonSubTypes.Type(value = ActionDetail.EmailAction.class, name = "SEND_EMAIL"),
      @JsonSubTypes.Type(value = ActionDetail.ShareAction.class, name = "SHARE"),
      @JsonSubTypes.Type(value = ActionDetail.MarketplaceAction.class, name = "MARKETPLACE_ACTION"),
      @JsonSubTypes.Type(value = AssignToAction.class, name = "ASSIGN_TO"),
      @JsonSubTypes.Type(value = ActionDetail.TriggerWorkflowAction.class, name = "TRIGGER_WORKFLOW"),
      @JsonSubTypes.Type(value = ActionDetail.ConvertLeadAction.class, name = "CONVERT_LEAD"),
      @JsonSubTypes.Type(value = ActionDetail.SendWhatsappMessageAction.class, name = "SEND_WHATSAPP_MESSAGE"),
      @JsonSubTypes.Type(value = ActionDetail.GenerateCallSummaryAction.class, name = "GENERATE_CALL_SUMMARY")
  })
  private ActionDetail payload;

  public ActionResponse(ActionType type, ActionDetail payload) {
    this.type = type;
    this.payload = payload;
  }
}
