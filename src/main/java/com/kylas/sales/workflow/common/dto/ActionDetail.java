package com.kylas.sales.workflow.common.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.CopyConfiguration;
import com.kylas.sales.workflow.domain.workflow.action.EditPropertyAction.EditPropertyActionType;
import com.kylas.sales.workflow.domain.workflow.action.email.EmailRecipient;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceActionParameter;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.ConnectedAccount;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.SendWhatsappMessageType;
import com.kylas.sales.workflow.domain.workflow.action.sendWhatsApp.Template;
import com.kylas.sales.workflow.domain.workflow.action.share.Action;
import com.kylas.sales.workflow.domain.workflow.action.task.AssignedTo;
import com.kylas.sales.workflow.domain.workflow.action.task.DueDate;
import com.kylas.sales.workflow.domain.workflow.action.webhook.Parameter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.http.HttpMethod;

public interface ActionDetail {

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString(exclude ={"authorizationParameter"})
  class WebhookAction implements ActionDetail {

    private final String name;
    private final String description;
    private final HttpMethod method;
    private String requestUrl;
    private final AuthorizationType authorizationType;
    private final List<Parameter> parameters;
    private final String authorizationParameter;

    public void setRequestUrl(String requestUrl) {
      this.requestUrl = requestUrl;
    }

    public enum AuthorizationType {
      NONE, API_KEY, BEARER_TOKEN, BASIC_AUTH
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  class MarketplaceAction implements ActionDetail {

    private final String name;
    private final String description;
    private final HttpMethod method;
    private final String resourceId;
    private String requestUrl;
    private final List<MarketplaceActionParameter> parameters;
    private Long actionId;
    private String appId;
    private boolean active;


    public void setRequestUrl(String requestUrl) {
      this.requestUrl = requestUrl;
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class EditPropertyAction implements ActionDetail {

    private final String name;
    private final Object value;
    private final ValueType valueType;
    @JsonProperty(value = "isStandard")
    private final boolean standard;
    private final EditPropertyActionType editActionType;
    private final CopyConfiguration copyConfiguration;

    public enum ValueType {
      ARRAY, OBJECT, PLAIN, ID_NAME
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class ReassignAction implements ActionDetail {

    private final Long id;
    private final String name;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class CreateTaskAction implements ActionDetail {

    private final String name;
    private final String description;
    private final Long priority;
    private final ReminderType reminder;
    private final Long type;
    private final Long status;
    private final AssignedTo assignedTo;
    private final DueDate dueDate;
    private final Map<String,Object> customFieldValues;

    public enum ReminderType {
      FIFTEEN_MINUTES, THIRTY_MINUTES, ONE_HOUR, TWO_HOURS, ONE_DAY, NO_REMINDER;
    }

  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class EmailAction implements ActionDetail {

    private final long emailTemplateId;
    private final EmailRecipient from;
    private final Set<EmailRecipient> to;
    private final Set<EmailRecipient> cc;
    private final Set<EmailRecipient> bcc;
    private final boolean trackingEnabled;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class ShareAction implements ActionDetail {
    private final String name;
    @JsonProperty(value = "toType")
    private final EntityType toType;
    private final IdName toIdName;
    @JsonProperty(value = "permissions")
    private final Action permissions;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class AssignToAction implements ActionDetail {

    private final Long id;
    private final String name;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class TriggerWorkflowAction implements ActionDetail {

    private final Long id;
    private final String name;
    private final EntityType entityType;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  class ConvertLeadAction implements ActionDetail {

    private final boolean deal;
    private final boolean contact;
    private final boolean company;

  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @AllArgsConstructor
  @ToString
  class SendWhatsappMessageAction implements ActionDetail {

    private ConnectedAccount connectedAccount;
    private Set<SendWhatsappMessageType> to;
    private Template template;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  @ToString
  class GenerateCallSummaryAction implements ActionDetail {

    private String prompt;

    @JsonCreator
    public GenerateCallSummaryAction(@JsonProperty("prompt") String prompt) {
      this.prompt = prompt;
    }
  }
}
