package com.kylas.sales.workflow.common.dto.condition;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpressionField {
  PIPELINE("pipeline", "pipeline.id"),
  PIPELINE_STAGE("pipelineStage", "pipelineStage.id"),
  PIPELINE_STAGE_REASON("pipelineStageReason", "pipelineStageReason"),
  DEAL_PIPELINE_STAGE("pipeline", "pipeline.stage"),
  STAGE("pipeline", "pipeline.stage.id"),
  PRODUCT("product", "product.id"),
  PRODUCTS("products", "id"),
  SOURCE("source", "source.id"),
  CAMPAIGN("campaign", "campaign.id"),
  SALUTATION("salutation", "salutation.id"),
  CREATED_BY("createdBy", "createdBy.id"),
  UPDATED_BY("updatedBy", "updatedBy.id"),
  CONVERTED_BY("convertedBy", "convertedBy.id"),
  OWNER_ID("ownerId", "ownerId.id"),
  OWNED_BY("ownedBy", "ownedBy.id"),
  COMPANY("company", "company.id"),
  ASSOCIATED_CONTACTS("associatedContacts", "id"),
  ESTIMATED_VALUE("estimatedValue", "estimatedValue.value"),
  ACTUAL_VALUE("actualValue", "actualValue.value"),
  COMPANY_EMPLOYEES("companyEmployees", "companyEmployees.id"),
  STATUS("status", "status.id"),
  ASSIGNED_TO("assignedTo", "assignedTo.id"),
  TYPE("type", "type.id"),
  PRIORITY("priority", "priority.id"),
  RELATIONS("relations", "entityType"),
  OWNER("owner", "owner.id"),
  CANCELLED_BY("cancelledBy", "cancelledBy.id"),
  CONDUCTED_BY("conductedBy", "conductedBy.id"),
  IMPORTED_BY("importedBy", "importedBy.id"),
  ORGANIZER("organizer", "organizer.id"),
  RELATED_TO("relatedTo","entity"),
  INVITEES("participants", "id"),
  CALL_RECORDING("callRecording", "callRecording.fileName"),
  CALL_DISPOSITION("callDisposition", "callDisposition.id"),
  CUSTOMER_EMOTION("customerEmotion", "id");


  private final String name;
  private final String fieldName;

  public static String getFieldByName(String name) {
    return Arrays.stream(values())
        .filter(expressionField -> expressionField.getName().equals(name))
        .findFirst()
        .map(ExpressionField::getFieldName)
        .orElse(name);
  }
}
