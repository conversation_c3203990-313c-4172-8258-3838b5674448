package com.kylas.sales.workflow.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClient.Builder;

@Configuration
public class WebConfig {

  public static Builder getWebClientBuilder() {
    final int size = 16 * 1024 * 1024;
    ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
        .build();

    return WebClient.builder()
        .exchangeStrategies(exchangeStrategies);
  }
}
