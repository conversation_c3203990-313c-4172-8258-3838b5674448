package com.kylas.sales.workflow.config;


import com.kylas.sales.workflow.domain.service.client.ConversionMappingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CacheFacade {

  @Cacheable(value = "default", key = "{#tenantId+'-conversion-mapping-workflow'}")
  public ConversionMappingResponse getConversionMapping(long tenantId) {
    log.info("Conversion mapping not present into cache for tenant id {}", tenantId);
    return null;
  }

  @CachePut(value = "default", key = "{#tenantId+'-conversion-mapping-workflow'}")
  public ConversionMappingResponse putConversionMapping(long tenantId, ConversionMappingResponse conversionMappingResponse) {
    log.info("Conversion mapping put into cache for tenant id {}", tenantId);
    return conversionMappingResponse;
  }

  @CacheEvict(value = "default", key = "{#tenantId+'-conversion-mapping-workflow'}")
  public void refreshConversionMapping(long tenantId) {
    log.info("Conversion mapping cache refreshed for tenant id {}", tenantId);
  }

}
