package com.kylas.sales.workflow.config;

import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

@Component
public class WebClientBuilderFactory {
  public WebClient.Builder getWebClientBuilder() {
    final int size = 16 * 1024 * 1024;
    ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
        .build();

    return WebClient.builder()
        .exchangeStrategies(exchangeStrategies);
  }
}
