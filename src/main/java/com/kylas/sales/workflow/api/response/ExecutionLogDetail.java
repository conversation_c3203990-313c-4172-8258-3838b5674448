package com.kylas.sales.workflow.api.response;

import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog.StatusType;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class ExecutionLogDetail {
  private final Long id;
  private final Long workflowId;

  private final Long entityId;
  private final String entityName;
  private final EntityType entityType;

  private final ActionType workflowAction;
  private final Map<String, Object> actionPayload;
  private final Date executedAt;
  private final StatusType status;
  private final Integer statusCode;
  private final String errorCode;
  private final String errorMessage;
  private final Map<String, Object> executionDetails;

  public static ExecutionLogDetail from(ExecutionLog executionLog) {
    return new ExecutionLogDetail(
        executionLog.getId(), executionLog.getWorkflowId(),
        executionLog.getEntityId(), executionLog.getEntityName(), executionLog.getEntityType(),
        executionLog.getWorkflowAction(), executionLog.getActionPayload(),
        executionLog.getExecutedAt(), executionLog.getStatus(),
        executionLog.getStatusCode(), executionLog.getErrorCode(), executionLog.getErrorMessage(), executionLog.getExecutionDetails()
    );
  }
}
