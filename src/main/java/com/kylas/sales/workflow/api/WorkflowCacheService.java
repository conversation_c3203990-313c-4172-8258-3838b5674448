package com.kylas.sales.workflow.api;

import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.TriggerFrequency;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.mq.event.EntityAction;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WorkflowCacheService {

  public static Map<TriggerFrequency, EntityAction> triggerFrequencyEntityActionMap = new HashMap<>() {{
    put(TriggerFrequency.CREATED, EntityAction.CREATED);
    put(TriggerFrequency.UPDATED, EntityAction.UPDATED);
  }};

  private final WorkflowFacade workflowFacade;

  @Autowired
  public WorkflowCacheService(WorkflowFacade workflowFacade) {
    this.workflowFacade = workflowFacade;
  }

  @Cacheable(value = "default", key = "{#tenantId+'-'+#entityType+'-'+#entityAction}")
  public List<Long> getWorkflowIdsBy(long tenantId, EntityType entityType, EntityAction entityAction) {
    log.info("Cache miss while fetching workflowIds By tenantId:{}, entityType: {}, entityAction: {}", tenantId, entityType, entityAction);
    return workflowFacade.getWorkflowIdsByTenantAndEntityAndTrigger(tenantId, entityType, entityAction);
  }

  @CacheEvict(value = "default", key = "{#tenantId+'-'+#entityType+'-'+#entityAction}")
  public void refreshWorkflowIdsBy(long tenantId, EntityType entityType, EntityAction entityAction) {
    log.info("Cache refresh for workflowIds By tenantId:{}, entityType: {}, entityAction: {}", tenantId, entityType, entityAction);
  }

  @Cacheable(value = "default", key = "{'WF_'+#id}")
  public Workflow getWorkflowById(Long id, Long tenantId) {
    log.info("Cache miss while fetching workflow by Id: {}", id);
    return workflowFacade.findById(id, tenantId).orElse(null);
  }

  @CacheEvict(value = "default", key = "{'WF_'+#id}")
  public void refreshWorkflowById(Long id) {
    log.info("Cache refresh for workflow By id: {}", id);
  }
}
