package com.kylas.sales.workflow.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.response.ScheduledJobEntry;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/scheduled-jobs")
@Slf4j
public class ScheduledJobController {

  private ScheduledJobService scheduledJobService;

  @Autowired
  public ScheduledJobController(ScheduledJobService scheduledJobService){
    this.scheduledJobService = scheduledJobService;
  }

  @PostMapping(
      value = "/search",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Page<ScheduledJobEntry> search(
      @PageableDefault(page = 0, size = 10) Pageable pageable,
      @RequestBody(required = false) FilterRequest filterRequest){
      return scheduledJobService.search(pageable, Optional.ofNullable(filterRequest));
  }

  @PostMapping(value = "/{id}/cancel")
  public ResponseEntity cancelScheduledJob(@PathVariable("id") long scheduledJobId){
    scheduledJobService.cancel(scheduledJobId, false);
    return ResponseEntity.ok().build();
  }

  @PostMapping(value = "/{id}/cancel-all-recurrences")
  public ResponseEntity cancelAllRecurrences(@PathVariable("id") long scheduledJobId){
    scheduledJobService.cancel(scheduledJobId, true);
    return ResponseEntity.ok().build();
  }
}

