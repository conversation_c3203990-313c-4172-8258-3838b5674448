package com.kylas.sales.workflow.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob.StatusType;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class ScheduledJobEntry {
  private final Long id;
  private final IdName workflow;
  private final Date executeAt;
  private final EntityType entityType;
  private final Long entityId;
  private final IdName user;
  private final StatusType status;
  @JsonProperty("recordActions")
  private final Action allowedActions;
  private final IdName updatedBy;
  private final Date updatedAt;
  private final boolean recurringJob;
}
