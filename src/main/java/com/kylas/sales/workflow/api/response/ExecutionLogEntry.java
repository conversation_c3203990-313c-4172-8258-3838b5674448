package com.kylas.sales.workflow.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog.StatusType;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class ExecutionLogEntry {

  private final Long id;
  private final Long workflowId;
  private final String workflowName;

  private final Long entityId;
  private final EntityType entityType;

  private final ActionType workflowAction;
  private final Date executedAt;
  private final StatusType status;

  @JsonProperty("recordActions")
  private final Action allowedActions;

  public static ExecutionLogEntry from(ExecutionLog executionLog) {
    return new ExecutionLogEntry(
        executionLog.getId(), executionLog.getWorkflowId(),
        executionLog.getWorkflowName(), executionLog.getEntityId(),
        executionLog.getEntityType(), executionLog.getWorkflowAction(),
        executionLog.getExecutedAt(), executionLog.getStatus(), executionLog.getAllowedActions()
    );
  }
}