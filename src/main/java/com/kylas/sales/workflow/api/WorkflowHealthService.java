package com.kylas.sales.workflow.api;

import com.kylas.sales.workflow.domain.WorkflowHealthFacade;
import org.hibernate.ObjectNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class WorkflowHealthService {
  private final WorkflowHealthFacade workflowHealthFacade;
  @Value("${health.tenantId}")
  private long tenantId;

  @Autowired
  public WorkflowHealthService(WorkflowHealthFacade workflowHealthFacade) {
    this.workflowHealthFacade = workflowHealthFacade;
  }

  public Integer getHealthStatus() {
    try {
      workflowHealthFacade.getWorkflowIdByTenantId(tenantId);
      return 200;
    } catch (ObjectNotFoundException objectNotFoundException) {
      return 200;
    } catch (Exception e) {
      return 503;
    }
  }
}
