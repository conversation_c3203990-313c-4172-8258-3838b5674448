package com.kylas.sales.workflow.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.request.WorkflowRequest;
import com.kylas.sales.workflow.api.response.WorkflowDetail;
import com.kylas.sales.workflow.api.response.WorkflowEntry;
import com.kylas.sales.workflow.api.response.WorkflowLookUp;
import com.kylas.sales.workflow.domain.exception.InvalidWorkflowRequestException;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.integration.IntegrationConfig;
import com.kylas.sales.workflow.integration.request.IntegrationRequest;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import java.util.List;
import java.util.Optional;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/v1/workflows")
@Slf4j
public class WorkflowController {

  private final WorkflowService workflowService;

  @Autowired
  public WorkflowController(WorkflowService workflowService) {
    this.workflowService = workflowService;
  }

  @PostMapping(value = "", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  @ApiOperation(
      value = "Create workflow",
      extensions = {
        @Extension(
            name = "access-policy",
            properties = {
              @ExtensionProperty(name = "action", value = "create"),
              @ExtensionProperty(name = "policy", value = "workflows"),
              @ExtensionProperty(name = "resource", value = "workflow")
            })
      })
  public Mono<ResponseEntity> createWorkflow(@RequestBody WorkflowRequest workflowRequest) {
    log.info("Create workflow request : {}", workflowRequest.toString());
    return workflowService
        .create(workflowRequest)
        .map(
            workflowSummary -> {
              var workflowDetailUri =
                  UriComponentsBuilder.fromPath("/{id}")
                      .buildAndExpand(workflowSummary.getId())
                      .toUri();
              return ResponseEntity.created(workflowDetailUri).body(workflowSummary);
            });
  }

  @GetMapping(value = "/{id}", produces = APPLICATION_JSON_VALUE)
  public Mono<WorkflowDetail> getWorkflow(@PathVariable("id") long workflowId) {
    return workflowService.get(workflowId);
  }

  @PutMapping(value = "/{id}", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public Mono<WorkflowDetail> updateWorkflow(
      @PathVariable("id") long workflowId, @RequestBody WorkflowRequest workflowRequest) {
    log.info("Update workflow request : {}", workflowRequest.toString());
    return workflowService.update(workflowId, workflowRequest);
  }

  @PostMapping(
      value = "/list",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Mono<Page<WorkflowDetail>> getAllWorkflow(
      @PageableDefault(page = 0, size = 10) Pageable pageable) {
    return workflowService.list(pageable);
  }

  @PostMapping(
      value = "/search",
      consumes = APPLICATION_JSON_VALUE,
      produces = APPLICATION_JSON_VALUE)
  public Page<WorkflowEntry> search(
      @PageableDefault(page = 0, size = 10) Pageable pageable,
      @RequestBody(required = false) FilterRequest filterRequest) {

    Pageable lastTriggeredAt =
        pageable.getSort().stream()
            .filter(order -> order.getProperty().equalsIgnoreCase("lastTriggeredAt"))
            .findFirst()
            .map(
                order -> {
                  Order sortByLastTriggeredAt =
                      order.withProperty("workflowExecutedEvent.lastTriggeredAt");
                  return (Pageable)
                      PageRequest.of(
                          pageable.getPageNumber(),
                          pageable.getPageSize(),
                          Sort.by(sortByLastTriggeredAt));
                })
            .orElse(pageable);

    return workflowService.search(lastTriggeredAt, Optional.ofNullable(filterRequest));
  }

  @PostMapping(value = "/{id}/deactivate", produces = APPLICATION_JSON_VALUE)
  public Mono<WorkflowDetail> deactivate(@PathVariable("id") long workflowId) {
    return workflowService.deactivate(workflowId);
  }

  @PostMapping(value = "/{id}/activate", produces = APPLICATION_JSON_VALUE)
  @ApiOperation(
      value = "Activate workflow",
      extensions = {
        @Extension(
            name = "access-policy",
            properties = {
              @ExtensionProperty(name = "action", value = "activate"),
              @ExtensionProperty(name = "policy", value = "workflows"),
              @ExtensionProperty(name = "resource", value = "workflow")
            })
      })
  public Mono<WorkflowDetail> activate(@PathVariable("id") long workflowId) {
    return workflowService.activate(workflowId);
  }

  @PostMapping(value = "/integrations/{event}")
  public Mono<WorkflowDetail> registerSystemDefault(
      @PathVariable("event") String event, @RequestBody IntegrationRequest request) {
    return workflowService.registerIntegration(IntegrationConfig.from(event, request));
  }

  @DeleteMapping(value = "/integrations/{event}")
  public Mono<Boolean> unregisterSystemDefault(
      @PathVariable("event") String event, @RequestBody IntegrationRequest request) {
    return workflowService.unregisterIntegration(IntegrationConfig.from(event, request));
  }

  @GetMapping(value = "/email/{emailTemplateId}/list", produces = APPLICATION_JSON_VALUE)
  public Mono<List<String>> getWorkflowNamesAssociatedWithEmailTemplate(
      @PathVariable("emailTemplateId") long emailTemplateId) {
    return workflowService.getWorkflowNamesByEmailTemplateId(emailTemplateId);
  }

  @ApiOperation("Lookup workflow by name")
  @GetMapping(value = "/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<Page<WorkflowLookUp>> lookUpWorkflows(@RequestParam("q") String queryParam,
      @RequestParam("associatedType") Optional<EntityType> entityType) {
    var queryParamValue = getQueryParamValue(queryParam);
    return ResponseEntity.ok(workflowService.getWorkflowLookup(queryParamValue, getPageRequest(queryParamValue), entityType));
  }

  private String getQueryParamValue(String queryParam) {
    if (!queryParam.contains(":")) {
      throw new InvalidWorkflowRequestException();
    }
    String[] split = queryParam.split(":");
    if (split.length == 2 && !"".equalsIgnoreCase(split[1])) {
      return split[1];
    }
    return "";
  }

  private PageRequest getPageRequest(String value) {
    Sort sortOnUpdatedAtDesc = Sort.by(Direction.DESC, "updatedAt");
    if ("".equalsIgnoreCase(value)) {
      return PageRequest.of(0, 10, sortOnUpdatedAtDesc);
    }
    return PageRequest.of(0, 25, sortOnUpdatedAtDesc);
  }

}
