package com.kylas.sales.workflow.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/vYWJOAWPLZXRTZDEZ/workflows")
public class WorkflowHealthController {
  private final WorkflowHealthService workflowHealthService;

  @Autowired
  public WorkflowHealthController(WorkflowHealthService workflowHealthService) {
    this.workflowHealthService = workflowHealthService;
  }

  @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<Integer> getWorkflowHealth() {
    int workflowHealthResponse = workflowHealthService.getHealthStatus();

    if (workflowHealthResponse == 200) {
      return ResponseEntity.ok().build();
    }
    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
  }
}
