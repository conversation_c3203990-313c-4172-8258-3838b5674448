package com.kylas.sales.workflow.api.request;

import static com.kylas.sales.workflow.common.dto.condition.Operator.AND;
import static com.kylas.sales.workflow.common.dto.condition.Operator.OR;
import static com.kylas.sales.workflow.common.dto.condition.Operator.getByName;
import static java.util.Objects.isNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kylas.sales.workflow.common.dto.condition.ExpressionField;
import com.kylas.sales.workflow.common.dto.condition.FieldCategory;
import com.kylas.sales.workflow.common.dto.condition.Operator;
import com.kylas.sales.workflow.domain.exception.InvalidConditionException;
import com.kylas.sales.workflow.domain.processor.RelativeDateFilter;
import com.kylas.sales.workflow.domain.workflow.ConditionType;
import com.kylas.sales.workflow.layout.api.response.list.FieldType;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Slf4j
public class Condition {

  private final ConditionType conditionType;
  private final List<ExpressionElement> conditions;

  @JsonCreator
  public Condition(
      @JsonProperty("conditionType") String conditionType,
      @JsonProperty("conditions") List<ExpressionElement> conditions) {
    this.conditionType = ConditionType.valueOf(conditionType);
    this.conditions = conditions;
  }

  @Getter
  @JsonInclude(Include.NON_NULL)
  @ToString
  public static class ExpressionElement {

    private final Operator operator;
    private final String name;
    private final Object value;
    private final TriggerType triggerOn;
    private final boolean standard;
    @JsonIgnore
    private String fieldType;
    private FieldCategory fieldCategory;

    @JsonCreator
    public ExpressionElement(
        @JsonProperty("operator") String operator,
        @JsonProperty("name") String name,
        @JsonProperty("value") Object value,
        @JsonProperty("triggerOn") String triggerOn, boolean standard) {
      this.name = name;
      this.triggerOn = isNull(triggerOn) ? null : TriggerType.valueOf(triggerOn);
      this.standard = standard;
      if (TriggerType.IS_CHANGED.name().equals(triggerOn)) {
        this.operator = null;
        this.value = List.of(ExpressionField.PIPELINE_STAGE.getName(), ExpressionField.PIPELINE_STAGE_REASON.getName()).contains(name) ? value : null;
      } else {
        this.operator = getByName(operator);
        this.value = value;
      }
    }

    public boolean hasBinaryOperator() {
      return operator.equals(AND) || operator.equals(OR);
    }

    public ExpressionElement setFieldFactory(FieldCategory fieldCategory) {
      this.fieldCategory=fieldCategory;
      return this;
    }
  }


  @Getter
  @AllArgsConstructor
  public enum TriggerType {
    NEW_VALUE, OLD_VALUE, IS_CHANGED
  }

  public void validateAndUpdateConditions(Map<String, String> fieldTypeMap) {
    if (ObjectUtils.isEmpty(conditions)) {
      return;
    }
    conditions.stream()
        .filter(expressionElement -> ObjectUtils.isNotEmpty(expressionElement.getName()))
        .forEach(expressionElement -> {
          if (!fieldTypeMap.containsKey(expressionElement.getName())) {
            log.info("Field is not present for condition expression element name {}", expressionElement.getName());
            throw new InvalidConditionException();
          }
          expressionElement.fieldType = fieldTypeMap.get(expressionElement.getName());
          validateRelativeDateFilterCondition(expressionElement);
        });
  }

  private void validateRelativeDateFilterCondition(ExpressionElement expression) {
    if (!List.of(FieldType.DATE_PICKER.name(), FieldType.DATETIME_PICKER.name()).contains(expression.getFieldType().toUpperCase())) {
      return;
    }

    if (Operator.BETWEEN.equals(expression.getOperator()) && !isDateTimeInputValues(expression.getValue())) {
      throw new InvalidConditionException();
    }

    RelativeDateFilter relativeDateFilter = getRelativeDateFilter(expression.getValue());
    if (ObjectUtils.isNotEmpty(relativeDateFilter) && ObjectUtils.isNotEmpty(relativeDateFilter.getType())) {
      relativeDateFilter.validate();
    }
  }

  private boolean isDateTimeInputValues(Object values) {
    if (!(values instanceof List)) {
      return false;
    }
    return ((List) values).stream().allMatch(value -> {
      if (!(value instanceof String)) {
        return false;
      }
      try {
        OffsetDateTime.parse(value.toString());
        return true;
      } catch (Exception e) {
        return false;
      }
    });
  }

  private RelativeDateFilter getRelativeDateFilter(Object value) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      return objectMapper.readValue(objectMapper.writeValueAsString(value), RelativeDateFilter.class);
    } catch (JsonProcessingException e) {
      return null;
    }
  }
}
