package com.kylas.sales.workflow.api.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class WorkflowLookUp {

  private final Long id;
  private final String name;

  @JsonCreator
  public WorkflowLookUp(@JsonProperty("id") Long id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }
}
