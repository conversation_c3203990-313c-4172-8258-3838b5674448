package com.kylas.sales.workflow.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.response.ExecutionLogDetail;
import com.kylas.sales.workflow.api.response.ExecutionLogEntry;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/workflows/execution-logs")
public class ExecutionLogController {

  private final ExecutionLogService executionLogService;

  @Autowired
  public ExecutionLogController(ExecutionLogService executionLogService) {
    this.executionLogService = executionLogService;
  }

  @PostMapping(value = "/search", consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
  public Page<ExecutionLogEntry> search(
      @PageableDefault(page = 0, size = 10) Pageable pageable,
      @RequestBody(required = false) FilterRequest filterRequest) {
    return executionLogService.search(pageable, Optional.ofNullable(filterRequest));
  }

  @GetMapping(value = "/{id}", produces = APPLICATION_JSON_VALUE)
  public ExecutionLogDetail getExecutionLog(@PathVariable("id") long executionLogId) {
    return executionLogService.get(executionLogId);
  }

}
