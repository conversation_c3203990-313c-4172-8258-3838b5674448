package com.kylas.sales.workflow.api;

import static com.kylas.sales.workflow.api.WorkflowCacheService.triggerFrequencyEntityActionMap;
import static com.kylas.sales.workflow.domain.workflow.ConditionType.CONDITION_BASED;
import static com.kylas.sales.workflow.domain.workflow.ConditionType.FOR_ALL;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

import com.kylas.sales.workflow.api.request.Condition;
import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.request.Schedule;
import com.kylas.sales.workflow.api.request.WorkflowRequest;
import com.kylas.sales.workflow.api.response.WorkflowDetail;
import com.kylas.sales.workflow.api.response.WorkflowEntry;
import com.kylas.sales.workflow.api.response.WorkflowLookUp;
import com.kylas.sales.workflow.api.response.WorkflowSummary;
import com.kylas.sales.workflow.common.dto.ActionDetail.CreateTaskAction;
import com.kylas.sales.workflow.common.dto.ActionDetail.ReassignAction;
import com.kylas.sales.workflow.common.dto.ActionDetail.ShareAction;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.common.dto.Applications;
import com.kylas.sales.workflow.common.dto.MarketPlaceTriggerResponse;
import com.kylas.sales.workflow.common.dto.User;
import com.kylas.sales.workflow.common.dto.WorkflowTrigger;
import com.kylas.sales.workflow.domain.ConditionFacade;
import com.kylas.sales.workflow.domain.WorkflowFacade;
import com.kylas.sales.workflow.domain.WorkflowFilter;
import com.kylas.sales.workflow.domain.exception.InsufficientPrivilegeException;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.processor.task.AssignedToType;
import com.kylas.sales.workflow.domain.service.ValueResolver;
import com.kylas.sales.workflow.domain.workflow.ConditionType;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.TriggerFrequency;
import com.kylas.sales.workflow.domain.workflow.Workflow;
import com.kylas.sales.workflow.domain.workflow.WorkflowExecutedEvent;
import com.kylas.sales.workflow.domain.workflow.action.WorkflowAction.ActionType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketplaceAction;
import com.kylas.sales.workflow.domain.workflow.action.task.AssignedTo;
import com.kylas.sales.workflow.integration.IntegrationConfig;
import com.kylas.sales.workflow.mq.event.EntityAction;
import com.kylas.sales.workflow.mq.event.EntityEvent;
import com.kylas.sales.workflow.security.AuthService;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class WorkflowService {

  private final WorkflowFacade workflowFacade;
  private final AuthService authService;
  private final ConditionFacade conditionFacade;
  private final ValueResolver valueResolver;
  private final WorkflowCacheService workflowCacheService;

  @Autowired
  public WorkflowService(
      WorkflowFacade workflowFacade, AuthService authService, ConditionFacade conditionFacade,
      ValueResolver valueResolver, WorkflowCacheService workflowCacheService) {
    this.workflowFacade = workflowFacade;
    this.authService = authService;
    this.conditionFacade = conditionFacade;
    this.valueResolver = valueResolver;
    this.workflowCacheService = workflowCacheService;
  }

  public Mono<WorkflowSummary> create(WorkflowRequest workflowRequest) {
    workflowFacade.validate(workflowRequest);
    return workflowFacade
        .create(workflowRequest)
        .map(workflow -> {
          workflowCacheService.refreshWorkflowIdsBy(workflow.getTenantId(), workflow.getEntityType(), triggerFrequencyEntityActionMap.get(workflow.getWorkflowTrigger().getTriggerFrequency()));
          return new WorkflowSummary(workflow.getId());
        });
  }

  public List<Workflow> findActiveBy(long tenantId, EntityType entityType, TriggerFrequency triggerFrequency) {
    return workflowFacade.findActiveBy(tenantId, entityType, triggerFrequency);
  }

  public List<Workflow> findActiveByMarketPlaceTrigger(long tenantId, EntityType entityType, TriggerFrequency triggerFrequency, UUID triggerId) {
    return workflowFacade.findActiveByMarketPlaceTrigger(tenantId, entityType, triggerFrequency,triggerId);
  }

  public Mono<WorkflowDetail> get(long workflowId) {
    String authToken = authService.getAuthenticationToken();
    return toWorkflowDetail(getById(workflowId), authToken);
  }

  public Workflow getById(long workflowId) {
    return workflowFacade.get(workflowId);
  }

  public Mono<WorkflowDetail> update(long workflowId, WorkflowRequest workflowRequest) {
    workflowFacade.validate(workflowRequest);
    workflowFacade.validateTriggerTypeWithAssociatedWorkflow(workflowId, workflowRequest);
    String authToken = authService.getAuthenticationToken();
    return workflowFacade.update(workflowId, workflowRequest)
        .flatMap(workflow -> {
          workflowCacheService.refreshWorkflowIdsBy(workflow.getTenantId(), workflow.getEntityType(), EntityAction.CREATED);
          workflowCacheService.refreshWorkflowIdsBy(workflow.getTenantId(), workflow.getEntityType(), EntityAction.UPDATED);
          workflowCacheService.refreshWorkflowById(workflowId);
          return toWorkflowDetail(workflow, authToken);
        });
  }

  private Mono<WorkflowDetail> toWorkflowDetail(Workflow workflow, String authenticationToken) {
    var workflowTrigger =
        new WorkflowTrigger(
            workflow.getWorkflowTrigger().getTriggerType(),
            workflow.getWorkflowTrigger().getTriggerFrequency());

    ConditionType conditionType = workflow.getWorkflowCondition().getType();
    var conditionMono =
        conditionType.equals(FOR_ALL)
            ? Mono.just(new Condition(conditionType.name(), null))
            : conditionFacade
                .nameResolved(workflow.getWorkflowCondition().getExpression(), authenticationToken)
                .map(expression -> new Condition(conditionType.name(), conditionFacade.flattenExpression(expression)));

    var executionConditionMono = workflow.getExecutionCondition() != null
        ? workflow.getExecutionCondition().getType().equals(FOR_ALL)
        ? Mono.just(new Condition(workflow.getExecutionCondition().getType().name(), null))
        : conditionFacade
            .nameResolved(workflow.getExecutionCondition().getExpression(), authenticationToken)
            .map(expression -> new Condition(CONDITION_BASED.name(), conditionFacade.flattenExpression(expression)))
        : Mono.just(new Condition(CONDITION_BASED.toString(), null));

    var createdBy = new User(workflow.getCreatedBy().getId(), workflow.getCreatedBy().getName());
    var updatedBy = new User(workflow.getUpdatedBy().getId(), workflow.getUpdatedBy().getName());

    var executedEvent =
        nonNull(workflow.getWorkflowExecutedEvent())
            ? workflow.getWorkflowExecutedEvent()
            : WorkflowExecutedEvent.createNew(workflow);

    var schedule = workflow.getSchedule() != null ?
        new Schedule(
            workflow.getSchedule().getPerform(),
            workflow.getSchedule().getDateField(),
            workflow.getSchedule().getAtTime(),
            workflow.getSchedule().getRepeat(),
            workflow.getSchedule().getRecurrence()
        ) : null;

    var marketplaceTrigger = workflow.getMarketPlaceTrigger() != null ?
        new MarketPlaceTriggerResponse(
            workflow.getMarketPlaceTrigger().getMarketPlaceTriggerDetail().getTriggerId(),workflow.getMarketPlaceTrigger().getMarketPlaceTriggerDetail().getName(),
            workflow.getMarketPlaceTrigger().getMarketPlaceTriggerDetail().getAppId(),workflow.getMarketPlaceTrigger().getMarketPlaceTriggerDetail().getTriggerVariables()): null;

    List<ActionResponse> actionResponses = workflow.getWorkflowActions()
        .stream()
        .map(action -> action.getType().toActionResponse(action))
        .collect(toList());

    return Flux.fromIterable(actionResponses)
        .flatMap(actionResponse -> getResolvedAction(actionResponse, authenticationToken, workflow.getEntityType()))
        .collectList()
        .zipWith(conditionMono)
        .zipWith(executionConditionMono)
        .map(objects -> {
              Condition executionCondition =
                  (objects.getT2().getConditionType().name().equals("CONDITION_BASED") && objects.getT2().getConditions() == null) ? null
                      : objects.getT2();
              return new WorkflowDetail(
                  workflow.getId(), workflow.getName(), workflow.getDescription(), workflow.getEntityType(),
                  workflowTrigger, objects.getT1().getT2(), objects.getT1().getT1(), createdBy, updatedBy,
                  workflow.getCreatedAt(), workflow.getUpdatedAt(), executedEvent.getLastTriggeredAt(),
                  executedEvent.getTriggerCount(), workflow.getAllowedActions(), workflow.isActive(), executionCondition, schedule,marketplaceTrigger);
            }
        );
  }

  private Mono<ActionResponse> getResolvedAction(ActionResponse action, String authenticationToken, EntityType entityType) {
    if (action.getType().equals(ActionType.REASSIGN)) {
      var actionDetail = (ReassignAction) action.getPayload();
      if (actionDetail.getId() == null){
        return Mono.just(action);
      }
      return valueResolver
          .getUserName(actionDetail.getId(), authenticationToken)
          .map(name ->
              new ActionResponse(
                  action.getId(),
                  action.getType(),
                  new ReassignAction(actionDetail.getId(), name)));
    }
    if (action.getType().equals(ActionType.CREATE_TASK)) {
      var actionDetail = (CreateTaskAction) action.getPayload();
      if (actionDetail.getAssignedTo().getType() != null && actionDetail.getAssignedTo().getType().equals(AssignedToType.USER)) {
        return actionDetail.getAssignedTo().getId() == null ? Mono.just(action) :
            valueResolver.getUserName(actionDetail.getAssignedTo().getId(), authenticationToken)
                .map(name -> new ActionResponse(action.getId(), action.getType(),
                    new CreateTaskAction(actionDetail.getName(), actionDetail.getDescription(), actionDetail.getPriority(),
                        actionDetail.getReminder(),
                        actionDetail.getType(), actionDetail.getStatus(),
                        new AssignedTo(actionDetail.getAssignedTo().getType(), actionDetail.getAssignedTo().getId(), name),
                        actionDetail.getDueDate(),actionDetail.getCustomFieldValues())));
      }
    }
    if (action.getType().equals(ActionType.SHARE)) {
      ShareAction shareAction = (ShareAction) action.getPayload();
      if (shareAction.getToIdName() == null || shareAction.getToIdName().getId() == null) {
        return Mono.just(action);
      }
      Mono<String> response = shareAction.getToType().equals(EntityType.USER)
          ? valueResolver.getUserName(shareAction.getToIdName().getId(), authenticationToken)
          : valueResolver.getTeamName(shareAction.getToIdName().getId(), authenticationToken);
      return response
          .map(name -> new ActionResponse(action.getId(), action.getType(),
              new ShareAction(shareAction.getName(), shareAction.getToType(), new IdName(shareAction.getToIdName().getId(), name),
                  shareAction.getPermissions())
          ));
    }
    return Mono.just(action);
  }

  private WorkflowEntry toWorkflowEntry(Workflow workflow) {
    var createdBy = new User(workflow.getCreatedBy().getId(), workflow.getCreatedBy().getName());
    var updatedBy = new User(workflow.getUpdatedBy().getId(), workflow.getUpdatedBy().getName());
    var executedEvent =
        nonNull(workflow.getWorkflowExecutedEvent())
            ? workflow.getWorkflowExecutedEvent()
            : WorkflowExecutedEvent.createNew(workflow);
    return new WorkflowEntry(workflow.getId(), workflow.getName(), workflow.getEntityType(),
        createdBy, updatedBy, workflow.getCreatedAt(), workflow.getUpdatedAt(), executedEvent.getLastTriggeredAt(),
        executedEvent.getTriggerCount(), workflow.getAllowedActions(), workflow.isActive(), workflow.getWorkflowTrigger().getTriggerFrequency());
  }

  public Mono<Page<WorkflowDetail>> list(Pageable pageable) {
    Page<Workflow> list = workflowFacade.list(pageable);
    String authToken = authService.getAuthenticationToken();
    return Flux
        .fromIterable(list.getContent())
        .flatMap(workflow -> toWorkflowDetail(workflow, authToken))
        .collectList()
        .map(workflowDetails -> new PageImpl<>(workflowDetails, list.getPageable(), list.getTotalElements()));
  }

  public void updateExecutedEventDetails(Workflow workflow) {
    workflowFacade.updateExecutedEvent(workflow);
  }

  public Mono<WorkflowDetail> deactivate(long workflowId) {
    String authToken = authService.getAuthenticationToken();
    return workflowFacade.deactivate(workflowId)
        .flatMap(workflow -> {
          workflowCacheService.refreshWorkflowIdsBy(workflow.getTenantId(), workflow.getEntityType(), triggerFrequencyEntityActionMap.get(workflow.getWorkflowTrigger().getTriggerFrequency()));
          workflowCacheService.refreshWorkflowById(workflowId);
          return toWorkflowDetail(workflow, authToken);
        })
        .map(workflowDetail -> workflowDetail);
  }

  public Mono<WorkflowDetail> activate(long workflowId) {
    String authToken = authService.getAuthenticationToken();
    return workflowFacade.activate(workflowId)
        .flatMap(workflow -> {
          workflowCacheService.refreshWorkflowIdsBy(workflow.getTenantId(), workflow.getEntityType(), triggerFrequencyEntityActionMap.get(workflow.getWorkflowTrigger().getTriggerFrequency()));
          workflowCacheService.refreshWorkflowById(workflowId);
          return toWorkflowDetail(workflow, authToken);
        })
        .map(workflowDetail -> workflowDetail);
  }

  public Page<WorkflowEntry> search(
      Pageable pageable, Optional<FilterRequest> optionalFilterRequest) {
    Optional<Set<WorkflowFilter>> workflowFilters = optionalFilterRequest
        .map(filterRequest -> filterRequest.getFilters().stream()
            .map(filter -> new WorkflowFilter(filter.getOperator(), filter.getFieldName(), filter.getFieldType(), filter.getValue()))
            .collect(Collectors.toSet()));
    Page<Workflow> list = workflowFacade.search(pageable, workflowFilters);

    List<WorkflowEntry> workflowEntries = list.getContent().stream().map(this::toWorkflowEntry).collect(toList());
    return new PageImpl<>(workflowEntries, list.getPageable(), list.getTotalElements());
  }

  public Mono<WorkflowDetail> registerIntegration(IntegrationConfig config) {
    var authToken = authService.getAuthenticationToken();
    return workflowFacade
        .registerIntegration(config, authService.getLoggedInUser())
        .flatMap(workflow -> toWorkflowDetail(workflow, authToken));
  }

  public Mono<Boolean> unregisterIntegration(IntegrationConfig config) {
    return workflowFacade
        .unregisterIntegration(config, authService.getLoggedInUser());
  }

  public Mono<List<String>> getWorkflowNamesByEmailTemplateId(long emailTemplateId) {
    return Mono.just(workflowFacade.getWorkflowNamesByEmailTemplateId(emailTemplateId));
  }

  public Page<WorkflowLookUp> getWorkflowLookup(String name, Pageable page, Optional<EntityType> entityType) {
    if (!authService.getLoggedInUser().canQueryAllWorkflow()) {
      throw new InsufficientPrivilegeException();
    }
    return workflowFacade.getWorkflowsByName(name, page, entityType);
  }

  public List<MarketplaceAction> activeMarketplaceAction(Long appActionId, String appId, long tenantId) {
    return workflowFacade.activeMarketplaceAction(appActionId, appId, tenantId);
  }

  public List<MarketplaceAction> inactiveMarketplaceAction(Long appActionId, String appId, long tenantId) {
    return workflowFacade.inactiveMarketplaceAction(appActionId, appId, tenantId);
  }

  public void disableOrUninstalledApp(List<Applications> applicationsList) {
    List<MarketplaceAction> marketplaceActions = workflowFacade.disableOrUninstalledApp(applicationsList);
    marketplaceActions.stream()
        .map(marketplaceAction -> marketplaceAction.getWorkflow().getId())
        .distinct()
        .forEach(workflowCacheService::refreshWorkflowById);
  }

  public List<MarketplaceAction> installedApp(String appId, long tenantId) {
    List<MarketplaceAction> marketplaceActions = workflowFacade.installedApp(appId, tenantId);
    marketplaceActions.stream()
        .map(marketplaceAction -> marketplaceAction.getWorkflow().getId())
        .distinct()
        .forEach(workflowCacheService::refreshWorkflowById);
    return marketplaceActions;

  }

  public Optional<Workflow> findActiveWorkflowByIdAndTriggerFrequency(Long workflowId, TriggerFrequency triggerFrequency) {
    return workflowFacade.findByIdAndTriggerFrequency(workflowId, triggerFrequency);
  }

  public List<Long> findActiveWorkflowByIdsByEntityTypeAndTriggerFrequency(Long tenantId, EntityType entityType, EntityAction entityAction) {
    return workflowCacheService.getWorkflowIdsBy(tenantId, entityType, entityAction);
  }

  public boolean isWorkflowExistsByEntityEvent(EntityEvent entityEvent){
    return workflowFacade.isWorkflowExistsByTenantAndEntityAndTrigger(entityEvent.getMetadata().getTenantId(), entityEvent.getMetadata().getEntityType(), entityEvent.getMetadata().getEntityAction());
  }

  public Workflow findById(Long id, Long tenantId){
    return workflowCacheService.getWorkflowById(id, tenantId);
  }
}
