package com.kylas.sales.workflow.api;

import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.response.ScheduledJobEntry;
import com.kylas.sales.workflow.domain.ScheduledJobFacade;
import com.kylas.sales.workflow.domain.WorkflowFilter;
import com.kylas.sales.workflow.domain.processor.lead.IdName;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob;
import com.kylas.sales.workflow.domain.workflow.ScheduledJob.StatusType;
import com.kylas.sales.workflow.domain.workflow.delayed.action.Schedule.Frequency;
import com.kylas.sales.workflow.security.AuthService;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class ScheduledJobService {

  private final AuthService authService;
  private final ScheduledJobFacade scheduledJobFacade;

  @Autowired
  public ScheduledJobService(AuthService authService, ScheduledJobFacade scheduledJobFacade) {
    this.authService = authService;
    this.scheduledJobFacade = scheduledJobFacade;
  }

  public Page<ScheduledJobEntry> search(Pageable pageable, Optional<FilterRequest> request){
    Optional<Set<WorkflowFilter>> workflowFilters = request
        .map(filterRequest -> filterRequest.getFilters().stream()
            .map(filter -> new WorkflowFilter(filter.getOperator(), filter.getFieldName(), filter.getFieldType(), filter.getValue()))
            .collect(Collectors.toSet()));
    Page<ScheduledJob> scheduledJobsList = scheduledJobFacade.search(pageable, workflowFilters);
    boolean canUpdateAllWorkflow = authService.getLoggedInUser().canUpdateAllWorkflow();
    return scheduledJobsList.map(scheduledJob -> toScheduledJobEntry(scheduledJob, canUpdateAllWorkflow));
  }

  private ScheduledJobEntry toScheduledJobEntry(ScheduledJob scheduledJob, boolean canUpdate){
    Action action = new Action();
    action.setRead(true);
    action.setUpdate(canUpdate);
    return new ScheduledJobEntry(
        scheduledJob.getId(),
        new IdName(scheduledJob.getWorkflow().getId(), scheduledJob.getWorkflow().getName()),
        scheduledJob.getExecuteAt(),
        scheduledJob.getEntityType(),
        scheduledJob.getEntityId(),
        new IdName(scheduledJob.getUser().getId(), scheduledJob.getUser().getName()),
        scheduledJob.getStatus().equals(StatusType.ENQUEUED)? StatusType.SCHEDULED : scheduledJob.getStatus(),
        action,
        scheduledJob.getUpdatedBy() != null ? new IdName(scheduledJob.getUpdatedBy().getId(), scheduledJob.getUpdatedBy().getName()): null,
        scheduledJob.getUpdatedAt(),
        scheduledJob.getWorkflow().getSchedule() != null && scheduledJob.getWorkflow().getSchedule().isRecurring()
    );
  }

  public void cancel(long scheduledJobId, boolean cancelAll) {
    scheduledJobFacade.cancel(scheduledJobId, cancelAll);
  }
}
