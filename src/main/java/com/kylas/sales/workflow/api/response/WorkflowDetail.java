package com.kylas.sales.workflow.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.api.request.Condition;
import com.kylas.sales.workflow.api.request.Schedule;
import com.kylas.sales.workflow.common.dto.ActionResponse;
import com.kylas.sales.workflow.common.dto.MarketPlaceTriggerResponse;
import com.kylas.sales.workflow.common.dto.User;
import com.kylas.sales.workflow.common.dto.WorkflowTrigger;
import com.kylas.sales.workflow.domain.user.Action;
import com.kylas.sales.workflow.domain.workflow.EntityType;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTrigger;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class WorkflowDetail {

  private final long id;
  private final String name;
  private final String description;
  private final EntityType entityType;
  private final WorkflowTrigger trigger;
  private final Condition condition;
  private final List<ActionResponse> actions;
  private final User createdBy;
  private final User updatedBy;
  private final Date createdAt;
  private final Date updatedAt;
  private final Date lastTriggeredAt;
  private final Long triggerCount;
  @JsonProperty("recordActions")
  private final Action allowedActions;
  private final boolean active;
  private final Condition executionCondition;
  private final Schedule schedule;
  private final MarketPlaceTriggerResponse marketPlaceTrigger;
}
