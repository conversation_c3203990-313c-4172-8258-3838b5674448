package com.kylas.sales.workflow.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/workflows/triggers")
@Slf4j
public class WorkflowTriggerController {

  private final WorkflowTriggerService workflowTriggerService;

  @Autowired
  public WorkflowTriggerController(WorkflowTriggerService workflowTriggerService) {
    this.workflowTriggerService = workflowTriggerService;
  }

  @PostMapping(value = "/{entityType}/{triggerId}", produces = APPLICATION_JSON_VALUE)
  public  ResponseEntity<Void> processTriggers(@PathVariable("entityType") String entityType,
      @PathVariable("triggerId") String triggerId, @RequestBody Map<String,Object> requestPayload) {
    workflowTriggerService.processMarketPlaceTrigger(entityType.toUpperCase(),triggerId,requestPayload);
    return ResponseEntity.accepted().build();
  }

}
