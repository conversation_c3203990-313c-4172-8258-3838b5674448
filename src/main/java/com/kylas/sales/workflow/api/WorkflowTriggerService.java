package com.kylas.sales.workflow.api;

import com.kylas.sales.workflow.domain.MarketPlaceTriggerDetailRepository;
import com.kylas.sales.workflow.domain.MarketPlaceTriggerPayloadRepository;
import com.kylas.sales.workflow.domain.jsonSchema.CustomEmailValidator;
import com.kylas.sales.workflow.domain.jsonSchema.CustomUrlValidator;
import com.kylas.sales.workflow.domain.MarketPlaceTriggerRepository;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerPayload;
import com.kylas.sales.workflow.error.DomainException;
import com.kylas.sales.workflow.error.ErrorCode;
import com.kylas.sales.workflow.error.ErrorResource;
import com.kylas.sales.workflow.error.FieldErrorResource;
import com.kylas.sales.workflow.mq.command.MarketPlaceTriggerEventPublisher;
import com.kylas.sales.workflow.security.AuthService;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.everit.json.schema.Schema;
import org.everit.json.schema.ValidationException;
import org.everit.json.schema.loader.SchemaLoader;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorkflowTriggerService {

  private final AuthService authService;
  private final MarketPlaceTriggerRepository marketPlaceTriggerRepository;
  private final WorkflowTriggerFacade workflowTriggerFacade;
  private final MarketPlaceTriggerPayloadRepository marketPlaceTriggerPayloadRepository;
  private final MarketPlaceTriggerEventPublisher marketPlaceTriggerEventPublisher;

  @Autowired
  public WorkflowTriggerService(AuthService authService,
      MarketPlaceTriggerRepository marketPlaceTriggerRepository, WorkflowTriggerFacade workflowTriggerFacade,
      MarketPlaceTriggerPayloadRepository marketPlaceTriggerPayloadRepository, MarketPlaceTriggerEventPublisher marketPlaceTriggerEventPublisher) {
    this.authService = authService;
    this.marketPlaceTriggerRepository = marketPlaceTriggerRepository;
    this.workflowTriggerFacade = workflowTriggerFacade;
    this.marketPlaceTriggerPayloadRepository = marketPlaceTriggerPayloadRepository;
    this.marketPlaceTriggerEventPublisher = marketPlaceTriggerEventPublisher;
  }

  public void processMarketPlaceTrigger(String entityType, String triggerId,Map<String, Object> requestPayload) {
    log.info("Request Payload for triggerId:{} and entity:{}",triggerId,requestPayload);
    long tenantId = authService.getLoggedInUser().getTenantId();
    UUID uuid = UUID.fromString(triggerId);

    boolean workflowExists = marketPlaceTriggerRepository.hasWorkflow(
        entityType, uuid, tenantId);

    if(!workflowExists){
      log.info("Workflow not exists for entity:{}, tenantId:{} and triggerId:{}",entityType,tenantId,triggerId);
      return;
    }

    JSONObject jsonSchema = new JSONObject(workflowTriggerFacade.getJsonSchema(entityType, triggerId));
    JSONObject dataJson = new JSONObject(requestPayload);

    Schema schema = SchemaLoader.builder()
        .schemaJson(jsonSchema)
        .addFormatValidator(new CustomUrlValidator())
        .addFormatValidator(new CustomEmailValidator())
        .build().load().build();

    try {
      schema.validate(dataJson);
      MarketPlaceTriggerPayload marketPlaceTriggerPayload = MarketPlaceTriggerPayload.createNew(tenantId, entityType,uuid, requestPayload);
      marketPlaceTriggerPayloadRepository.saveAndFlush(marketPlaceTriggerPayload);
      marketPlaceTriggerEventPublisher.execute(marketPlaceTriggerPayload.getTriggerId().toString(),marketPlaceTriggerPayload.getTenantId(),authService.getLoggedInUser()
          .getId(), marketPlaceTriggerPayload.getEntity(),marketPlaceTriggerPayload.getRequestPayload());
    } catch (ValidationException e) {
      List<FieldErrorResource> fieldErrorResources;
      if(ObjectUtils.isEmpty(e.getCausingExceptions())){
        fieldErrorResources=Collections.singletonList(new FieldErrorResource(e.getPointerToViolation(),e.getMessage()));
      }else {
        fieldErrorResources=e.getCausingExceptions()
            .stream()
            .map(e1 -> new FieldErrorResource(e1.getPointerToViolation(), e1.getMessage()))
            .collect(Collectors.toList());
      }
      log.info("Validation failed for triggerId:{} and entity:{} with message:{}",triggerId,entityType,e.getLocalizedMessage());
      ErrorResource jsonSchemaValidationFailed = ErrorCode.JSON_SCHEMA_VALIDATION_FAILED;
      jsonSchemaValidationFailed.setFieldErrors(fieldErrorResources);
      throw new DomainException(jsonSchemaValidationFailed);
    }
  }

}
