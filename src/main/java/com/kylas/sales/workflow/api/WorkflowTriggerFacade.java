package com.kylas.sales.workflow.api;

import com.kylas.sales.workflow.domain.MarketPlaceTriggerDetailRepository;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerDetail;
import com.kylas.sales.workflow.domain.workflow.action.marketPlace.MarketPlaceTriggerDto;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorkflowTriggerFacade {

  private final MarketPlaceTriggerDetailRepository marketPlaceTriggerDetailRepository;

  @Autowired
  public WorkflowTriggerFacade(MarketPlaceTriggerDetailRepository marketPlaceTriggerDetailRepository) {
    this.marketPlaceTriggerDetailRepository = marketPlaceTriggerDetailRepository;
  }


  public void processTriggerVariablesOnAppTriggerUpdate(MarketPlaceTriggerDto marketplaceAppActionEvent)  {
    Optional<MarketPlaceTriggerDetail> marketPlaceTrigger = marketPlaceTriggerDetailRepository.findByTriggerIdAndAppIdAndEntity(
        marketplaceAppActionEvent.getId(), marketplaceAppActionEvent.getAppId(), marketplaceAppActionEvent.getEntity());
    if(marketPlaceTrigger.isEmpty()){
      log.info("No marketplace trigger present for appId:{} and triggerId:{} and entity:{}",
          marketplaceAppActionEvent.getAppId(),marketplaceAppActionEvent.getId(),marketplaceAppActionEvent.getEntity());
      return;
    }
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = marketPlaceTrigger.get();
    marketPlaceTriggerDetail.setTriggerVariables(marketplaceAppActionEvent.getTriggerVariables());
    marketPlaceTriggerDetail.setName(marketplaceAppActionEvent.getName());
    marketPlaceTriggerDetailRepository.saveAndFlush(marketPlaceTriggerDetail);
  }

  @Cacheable(value = "default#36000", key = "{#triggerId+#entityType+'-marketplace-trigger'}")
  public String getJsonSchema(String entityType, String triggerId) {
    UUID uuid = UUID.fromString(triggerId);
    log.info("Json schema is not present in cache creating new for triggerId:{} and entity:{}",triggerId,entityType);
    JSONObject jsonSchema = new JSONObject();
    JSONObject properties = new JSONObject();
    properties.put("mpEntityId",generateSchema("NUMBER"));
    jsonSchema.put("$schema","http://json-schema.org/draft-07/schema");
    jsonSchema.put("type", "object");
    MarketPlaceTriggerDetail marketPlaceTriggerDetail = marketPlaceTriggerDetailRepository.findByTriggerIdAndEntity(uuid,
        entityType.toUpperCase());
    marketPlaceTriggerDetail.getTriggerVariables()
        .forEach(marketPlaceTriggerVariable -> {
          properties.put(marketPlaceTriggerVariable.getName(),generateSchema(marketPlaceTriggerVariable.getType()));
        });
    jsonSchema.put("properties", properties);
    JSONArray requiredFields = new JSONArray();
    requiredFields.put("mpEntityId");
    jsonSchema.put("required", requiredFields);
    return jsonSchema.toString();
  }
  @CacheEvict(value = "default", key = "{#triggerId+#entityType+'-marketplace-trigger'}")
  public void refresh(String entityType, String triggerId) {
    log.info("Refresh cache for trigger id:{} and entity:{}",triggerId,entityType);
  }


  private JSONObject generateSchema(String dataType) {
    JSONObject schema = new JSONObject();

    switch (dataType) {
      case "TEXT_FIELD":
        schema.put("type", "string");
        schema.put("minLength", 3);
        schema.put("maxLength", 255);
        break;

      case "PARAGRAPH_TEXT":
        schema.put("type", "string");
        schema.put("minLength", 3);
        schema.put("maxLength", 2550);
        break;

      case "NUMBER":
        schema.put("type", "number");
        break;

      case "DATE_PICKER":
      case "DATETIME_PICKER":
        schema.put("type", "string");
        schema.put("format", "date-time");
        break;

      case "URL":
        schema.put("type", "string");
        schema.put("format", "url");
        break;

      case "TOGGLE":
      case "CHECKBOX":
        schema.put("type", "boolean");
        break;

      case "PHONE":
        JSONObject phoneNumberSchema = new JSONObject();
        phoneNumberSchema.put("type", "object");
        phoneNumberSchema.put("properties", new JSONObject()
            .put("id", new JSONObject().put("type", "number"))
            .put("type", new JSONObject().put("type", "string"))
            .put("code", new JSONObject().put("type", "string"))
            .put("dialCode", new JSONObject().put("type", "string"))
            .put("primary", new JSONObject().put("type", "boolean"))
            .put("value", new JSONObject().put("type", "string"))
        );
        phoneNumberSchema.put("required", new String[]{"type", "code", "primary", "value"});

        schema.put("type", "array");
        schema.put("items", phoneNumberSchema);
        break;

      case "EMAIL":
        JSONObject emailSchema = new JSONObject();
        emailSchema.put("type", "object");
        emailSchema.put("properties", new JSONObject()
            .put("type", new JSONObject().put("type", "string"))
            .put("value", new JSONObject().put("type", "string").put("format", "emails"))
            .put("primary", new JSONObject().put("type", "boolean")));
        emailSchema.put("required", new String[]{"type", "primary", "value"});

        schema.put("type", "array");
        schema.put("items", emailSchema);

        break;
    }

    return schema;
  }
}
