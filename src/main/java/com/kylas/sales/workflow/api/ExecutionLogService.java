package com.kylas.sales.workflow.api;

import static java.util.stream.Collectors.toList;
import com.kylas.sales.workflow.api.request.FilterRequest;
import com.kylas.sales.workflow.api.response.ExecutionLogDetail;
import com.kylas.sales.workflow.api.response.ExecutionLogEntry;
import com.kylas.sales.workflow.domain.ExecutionLogFacade;
import com.kylas.sales.workflow.domain.ExecutionLogFilter;
import com.kylas.sales.workflow.domain.workflow.ExecutionLog;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class ExecutionLogService {

  private final ExecutionLogFacade executionLogFacade;

  @Autowired
  public ExecutionLogService(ExecutionLogFacade executionLogFacade) {
    this.executionLogFacade = executionLogFacade;
  }

  public Page<ExecutionLogEntry> search(Pageable pageable, Optional<FilterRequest> optionalFilterRequest) {
    Optional<Set<ExecutionLogFilter>> executionLogFilters = optionalFilterRequest
        .map(filterRequest -> filterRequest.getFilters().stream()
            .map(filter -> new ExecutionLogFilter(filter.getOperator(), filter.getFieldName(), filter.getFieldType(), filter.getValue()))
            .collect(Collectors.toSet()));

    Page<ExecutionLog> executionLogs = executionLogFacade.search(pageable, executionLogFilters);

    List<ExecutionLogEntry> executionLogEntries = executionLogs.getContent().stream().map(this::toExecutionLogEntry).collect(toList());

    return new PageImpl<>(executionLogEntries, executionLogs.getPageable(), executionLogs.getTotalElements()) ;
  }

  private ExecutionLogEntry toExecutionLogEntry(ExecutionLog executionLog) {
    return new ExecutionLogEntry(executionLog.getId(), executionLog.getWorkflowId(), executionLog.getWorkflowName(),
        executionLog.getEntityId(), executionLog.getEntityType(), executionLog.getWorkflowAction(),
        executionLog.getExecutedAt(), executionLog.getStatus(), executionLog.getAllowedActions());
  }

  public ExecutionLogDetail get(long executionLogId) {
    return ExecutionLogDetail.from(executionLogFacade.get(executionLogId));
  }
}
