package com.kylas.sales.workflow.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kylas.sales.workflow.domain.workflow.delayed.action.Perform;
import com.kylas.sales.workflow.domain.workflow.delayed.action.Recurrence;
import com.kylas.sales.workflow.domain.workflow.delayed.action.Schedule.Timing;
import com.kylas.sales.workflow.domain.workflow.delayed.action.Schedule.Frequency;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Schedule {

  private final Perform perform;
  private final String dateField;
  private final Timing at;
  private final Frequency repeat;
  private final Recurrence recurrence;

  @JsonCreator
  public Schedule(
          @JsonProperty("perform") Perform perform,
          @JsonProperty("dateField") String dateField,
          @JsonProperty("at") Timing at,
          @JsonProperty("repeat") Frequency repeat,
          @JsonProperty("recurrence") Recurrence recurrence
  ) {
    this.perform = perform;
    this.dateField = dateField;
    this.at = at;
    this.repeat = repeat;
    this.recurrence = recurrence;
  }
}
